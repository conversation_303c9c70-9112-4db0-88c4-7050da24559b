from typing import List
import pandas as pd
from loguru import logger
from src.utils.common import parse_indices
from src.utils.hhcsv import HHCSV

def get_file_path(required_cols: List[str], file_description: str = "") -> str:
    """获取用户输入的文件路径
    
    Args:
        required_cols: 必需的列名列表
        file_description: 文件描述信息（可选）
    
    Returns:
        str: 处理后的文件路径
    """
    description = file_description or f"表头格式：{required_cols}"
    file_path = input(
        f"请输入Excel数据文件的完整路径({description})：\n"
        "提示：\n"
        "1. 支持的文件格式：csv\n"
        "2. 支持拖拽文件到终端\n"
        "3. 输入 'e' 退出程序\n"
        "> "
    ).strip()
    
    return file_path.strip("'\"")

def read_csv_data(file_path: str, required_cols: List[str]) -> pd.DataFrame:
    """读取CSV文件数据并验证必需列
    
    Args:
        file_path: CSV文件路径
        required_cols: 必需的列名列表
    
    Returns:
        pd.DataFrame: 读取的数据
    
    Raises:
        ValueError: 当缺少必需列时抛出
    """
    hhcsv = HHCSV(file_path, required_cols)
    return hhcsv.query()

def filter_data_by_indices(data, indices: list[int]) -> list[dict]:
    filtered = []
    for row in data:
        try:
            row_id = row.get("id")
            if row_id is None:
                logger.warning(f"跳过缺少id的数据: {row}")
                continue

            if int(row_id) in indices:
                filtered.append(row)
        except ValueError:
            logger.warning(f"无法将id转换为整数: {row}")
            continue
    return filtered

def process_input_file(file_path: str = None, required_cols: List[str] = None, 
                      index: str = None) -> pd.DataFrame:
    """处理输入文件的完整流程
    
    Args:
        file_path: 可选的文件路径，如果为None则提示用户输入
        required_cols: 必需的列名列表
        index: 可选的索引字符串
    
    Returns:
        pd.DataFrame: 处理后的数据
    """
    if not file_path:
        file_path = get_file_path(required_cols)

    data = read_csv_data(file_path, required_cols)

    if index:
        try:
            indices = parse_indices(index)
            filtered_data = filter_data_by_indices(data, indices)
            if not filtered_data:
                logger.warning(f"未找到匹配的数据，请检查输入的序号 {index} 是否正确")
                return None
            return filtered_data
        except ValueError as e:
            logger.error(f"解析索引出错: {e}")
            return None
    
    return data