import re

url = "VERIFY YOUR EMAILTo complete your registration, please verify your email address by clicking the button below.VERIFY EMAILCan't click the button above? Copy and paste this link into your browser:https://app.drops.house/email-verification?token=GVRoHrPnbxJHRwrVXxVN&return_url=https://drops.linera.io/home?ext_id=5oqo4TUSGThank you"
pattern = r'token=([^&]+)'
match = re.search(pattern, url)

if match:
    token = match.group(1)
    print(f"Token: {token}")
else:
    print("No token found")