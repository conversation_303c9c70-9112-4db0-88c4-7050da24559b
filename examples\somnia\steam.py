import json
import os
import random
from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/steam.log", rotation="10MB", level="SUCCESS")


class Steam:
    def __init__(self, controller: BrowserController):
        self.browser_controller = controller
        self.page = self.browser_controller.page
        self.id = self.browser_controller.id
        data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
        self.data_util = DataUtil(os.path.join(data_dir, "steam.csv"))
        self.address = self.browser_controller.browser_config.evm_address

    def login(self):
        item = self.data_util.get(self.address)
        if not item:
            logger.error(f"账号 {self.id} 未配置")
            return False

        login = item.get("login")
        password = item.get("password")
        if not login or not password:
            logger.error(f"账号 {self.id} 未配置")
            return False

        tab = self.page.new_tab(
            "https://store.steampowered.com/login/?redir=&redir_ssl=1"
        )
        sleep(2)

        user_ele = tab.ele("x://button[@id='account_pulldown']", timeout=5)
        if user_ele:
            logger.success(f"账号 {self.id} 已经登录成功")
            return tab

        username_input = tab.ele(
            "x://input[@type='password']/ancestor::form//input[@type='text']"
        )
        username_input.input(login)
        sleep(1)
        password_input = tab.ele("x://input[@type='password']")
        password_input.input(password)
        sleep(1)
        tab.ele("x://button[@type='submit']").click()
        sleep(1)

        if not tab.wait.url_change(text="https://store.steampowered.com/", timeout=10):
            logger.error(f"账号 {self.id} 登录失败")
            return False

        user_ele = tab.ele("x://button[@id='account_pulldown']", timeout=30)
        if user_ele:
            logger.success(f"账号 {self.id} 登录成功")
            return tab

        logger.error(f"账号 {self.id} 登录失败")
        return False

    def _change_public(self, tab):
        """
        切换到公开
        """
        try:
            # 点击头像
            avatar_ele = tab.ele(
                "x://a[contains(@href,'https://steamcommunity.com/profiles/') and @data-miniprofile]"
            )
            if not avatar_ele:
                logger.error(f"账号 {self.id} 没有找到头像按钮")
                return False
            href = avatar_ele.attr("href")
            if not href:
                logger.error(f"账号 {self.id} 没有找到url")
                return False

            # 打开编辑profile页面
            tab = self.page.new_tab(href + "edit/settings")
            tab.listen.start("ajaxsetprivacy/")
            sleep(1)

            # 切换Game details 到公开
            ele = tab.ele(
                "x:(//div[@class='ProfilePrivacyHeader']/div[@class='ProfilePrivacyDropDown'])[2]",
                timeout=10,
            )
            if not ele:
                logger.error(f"账号 {self.id} 点击选择框按钮没有找到")
                return False
            ele.click()

            ele = tab.ele(
                "x:(//div[@role='option' and contains(@class,'contextMenuItem')])[1]",
                timeout=10,
            )
            if not ele:
                logger.error(f"账号 {self.id} 选择框没有找到")
                return False
            ele.click()
            sleep(1)

            res = tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"账号 {self.id} 选择公开失败")
                return False

            response_data = res.response.body
            success = response_data.get("success")
            if success == 1:
                logger.success(f"账号 {self.id} 选择公开成功")
                return True

            logger.error(f"账号 {self.id} 选择公开失败")
            return False
        except Exception as e:
            logger.error(f"账号 {self.id} 切换到公开失败: {e}")
            return False

    def add_wishlist(self):
        """
        添加愿望单
        """
        try:
            tab = self.page.new_tab("")
            tab.listen.start("https://store.steampowered.com/dynamicstore/userdata/")
            wishlist_id = 672890
            tab.get(f"https://store.steampowered.com/app/{wishlist_id}/Sparkball/")
            sleep(1)

            res = tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"账号 {self.id} 监听数据失败")
                return False

            data = res.response.body
            if not data:
                logger.error(f"账号 {self.id} 获取数据失败")
                return False

            rg_wishlist = data.get("rgWishlist")
            for wish_id in rg_wishlist:
                if wish_id == wishlist_id:
                    logger.success(f"账号 {self.id} 已经添加愿望单")
                    return True

            ele = tab.ele("x://div[@id='add_to_wishlist_area']", timeout=10)
            if not ele:
                logger.error(f"账号 {self.id} 没有找到愿望单")
                return False

            tab.listen.start("https://store.steampowered.com/api/addtowishlist")
            ele.click()
            sleep(1)

            res = tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"账号 {self.id} 添加愿望单失败")
                return False

            data = res.response.body
            if not data:
                logger.error(f"账号 {self.id} 添加愿望单失败")
                return False

            success = data.get("success")
            if success == 1:
                logger.success(f"账号 {self.id} 添加愿望单成功")
                return True

            logger.error(f"账号 {self.id} 添加愿望单失败")
            return False
        except Exception as e:
            logger.error(f"账号 {self.id} 添加愿望单失败: {e}")
            return False

    def execute(self):
        tab = self.login()
        if not tab:
            return False

        result = self._change_public(tab)
        if not result:
            logger.error(f"账号 {self.id} 切换到公开失败")
            # return False

        result = self.add_wishlist()
        if not result:
            logger.error(f"账号 {self.id} 添加愿望单失败")
            return False

        return True


def _execute_task(type, index):
    controller = BrowserController(type, index)
    try:
        controller.window_max()
        steam = Steam(controller)
        steam.execute()
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False
    else:
        return True
    finally:
        if controller:
            controller.close_page()


def safe_int(value, default=0):
    """安全地将值转换为整数，处理空字符串和其他异常情况."""
    if value == "":
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _execute_task(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=2 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Steam-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 执行任务
# python examples/steam/index.py run -t ads -i 1-100

if __name__ == "__main__":
    cli()
    # _execute_task(BrowserType.CHROME, "1")
