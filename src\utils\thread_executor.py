from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED, TimeoutError
from typing import Callable, Optional, Union, Any
from functools import wraps
from loguru import logger
import time


class ThreadExecutor:
    """
    线程执行器
    用于处理需要多线程执行的任务，支持超时控制和失败重试
    """

    def __init__(
        self,
        workers: int = 5,  # 工作线程数
        timeout: int = 1800,  # 单任务超时时间(秒)
        retries: int = 3,  # 失败重试次数
        interval: int = 1,  # 任务间隔时间(秒)
        raise_exception: bool = False,  # 是否抛出异常
        task_name: str = "",  # 任务名称，用于日志标识
    ):
        """
        初始化线程执行器
        Args:
            workers: 工作线程数，必须大于0
            timeout: 单任务超时时间(秒)，必须大于0
            retries: 失败重试次数，必须大于0
            interval: 任务间隔时间(秒)，必须大于等于0
            raise_exception: 是否在最后一次重试失败时抛出异常
            task_name: 任务名称，用于日志标识
        """
        self._validate_params(workers, timeout, retries, interval)
        self.workers = workers
        self.timeout = timeout
        self.retries = retries
        self.interval = interval
        self.raise_exception = raise_exception
        self.task_name = task_name or "Task"

    @staticmethod
    def _validate_params(
        workers: int, timeout: int, retries: int, interval: int
    ) -> None:
        """
        验证输入参数
        Args:
            workers: 工作线程数
            timeout: 超时时间
            retries: 重试次数
            interval: 间隔时间
        Raises:
            ValueError: 当参数不合法时抛出
        """
        if workers < 1:
            raise ValueError("workers must be greater than 0")
        if timeout < 1:
            raise ValueError("timeout must be greater than 0")
        if retries < 1:
            raise ValueError("retries must be greater than 0")
        if interval < 0:
            raise ValueError("interval must be greater than or equal to 0")

    def run(self, func: Callable[..., Any], *args, **kwargs) -> Union[bool, Any]:
        """
        执行多线程任务，只有在发生异常时才重试
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            **kwargs: 函数关键字参数
        Returns:
            bool: 是否执行成功，或者函数的返回值
        Raises:
            Exception: 如果 raise_exception=True，则在最后一次重试失败时抛出异常
        """
        last_error: Optional[Exception] = None
        task_id = f"{self.task_name}-{time.strftime('%Y%m%d%H%M%S')}"

        @wraps(func)
        def wrapped_func(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.success(
                    f"[{task_id}] 任务执行完成，耗时: {execution_time:.2f}秒"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"[{task_id}] 任务执行异常，耗时: {execution_time:.2f}秒, 错误: {str(e)}"
                )
                raise

        for attempt in range(self.retries):
            try:
                with ThreadPoolExecutor(
                    max_workers=self.workers,
                    thread_name_prefix=f"{task_id}-Worker-{attempt+1}",
                ) as executor:
                    future = executor.submit(wrapped_func, *args, **kwargs)

                    # 等待任务完成或超时
                    done, _ = wait(
                        [future], timeout=self.timeout, return_when=ALL_COMPLETED
                    )

                    # 超时异常，需要重试
                    if not done:
                        error_msg = f"[{task_id}] 任务执行超时 (attempt {attempt + 1}/{self.retries}, timeout={self.timeout}s)"
                        logger.error(error_msg)
                        last_error = TimeoutError(error_msg)
                        continue

                    # 执行异常，需要重试
                    if future.exception():
                        last_error = future.exception()
                        logger.error(
                            f"[{task_id}] 任务执行异常 (attempt {attempt + 1}/{self.retries}): {str(last_error)}"
                        )
                        continue

                    # 获取结果，无论是 True 还是 False 都直接返回
                    result = future.result()
                    logger.info(f"[{task_id}] 任务执行结果: {result}")
                    return result if isinstance(result, bool) else True

            except Exception as e:
                # 其他异常，需要重试
                last_error = e
                logger.error(
                    f"[{task_id}] 任务执行过程异常: {str(e)} (attempt {attempt + 1}/{self.retries})"
                )

            if attempt < self.retries - 1:
                logger.info(
                    f"[{task_id}] 等待 {self.interval} 秒后进行第 {attempt + 2} 次重试"
                )
                time.sleep(self.interval)

        if self.raise_exception and last_error:
            raise last_error

        return False

    def run_batch(self, func: Callable[..., Any], items: list, *args, **kwargs) -> list:
        """
        批量执行多线程任务
        Args:
            func: 要执行的函数
            items: 任务项列表，每一项会作为func的第一个参数
            *args: 额外的函数位置参数
            **kwargs: 额外的函数关键字参数
        Returns:
            list: 所有任务的执行结果列表
        """
        task_id = f"{self.task_name}-{time.strftime('%Y%m%d%H%M%S')}"
        total_tasks = len(items)
        logger.info(
            f"[{task_id}] 开始批量执行任务，共 {total_tasks} 个任务，并发数 {self.workers}"
        )

        with ThreadPoolExecutor(
            max_workers=self.workers, thread_name_prefix=f"{task_id}-Worker"
        ) as executor:
            # 提交所有任务
            futures = []
            for item in items:
                future = executor.submit(
                    lambda x: self.run(lambda: func(x, *args, **kwargs)), item
                )
                futures.append(future)
                time.sleep(3)

            # 等待所有任务完成
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=self.timeout)
                    results.append(result)
                except Exception as e:
                    logger.error(f"[{task_id}] 任务执行失败: {e}")
                    results.append(False)

            # 统计结果
            success_count = sum(1 for r in results if r)
            if success_count == total_tasks:
                logger.success(
                    f"[{task_id}] 所有任务执行成功: {success_count}/{total_tasks}"
                )
            else:
                logger.warning(
                    f"[{task_id}] 部分任务执行失败: {success_count}/{total_tasks}"
                )

            return results
