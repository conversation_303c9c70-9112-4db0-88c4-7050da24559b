from loguru import logger
from retry import retry
from src.browsers import BrowserType
from src.socials.discord_chat_bot import Discord<PERSON>hatBot
from src.utils.browser_config import BrowserConfigInstance
from dotenv import load_dotenv
import os

load_dotenv()


class StorySaharaChat(BrowserConfigInstance):
    """Sahara 聊天任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        # self.channel_id = "1275205888977801339"  # 中文频道
        # self.channel_id = "1275205956715937803"  # 越南
        self.channel_id = "1288573569252720794"  # portuguese
        self.user_id = self.browser_config.dc_id
        self.openai_api_key = os.getenv("OPENAI_API_KEY")

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Sahara 聊天任务"""
        discord = DiscordChatBot(
            self.channel_id,
            self.browser_config.proxy,
            self.browser_config.dc_token,
            self.user_id,
            self.openai_api_key,
            self.browser_config.user_agent,
        )
        # discord.auto_chat(language="zh", max_messages=100, interval=10)
        time_diff = discord.get_latest_message_time_diff()
        if time_diff > 3600:
            discord.send_del_message(
                count=100, msg="que coisa boaaa​", min_delay=5, max_delay=10
            )
        logger.success(f"{self.browser_id} Sahara 聊天任务完成")
        return True
