import re
import random
from typing import Optional
from retry import retry
from loguru import logger
from time import sleep
from DrissionPage.common import make_session_ele

from .base import StoryBase, WalletConnectionError, TaskExecutionError
from config import ICLOUD_EMAIL, ICLOUD_EMAIL_PASSWORD
from src.browsers import BrowserType
from src.browsers.operations import try_click
from src.emails import GmailWebClient
from src.wallets.okx_wallet import OKXWallet
from src.utils.yescaptcha import YesCaptcha
from config import MAX_GAS_PRICE


class StoryColormp(StoryBase):
    """Colormp NFT任务"""

    # JavaScript脚本常量
    JS_SCRIPT_CHOOSE_WALLET = """
        let shadowHost = document.querySelector('w3m-modal');
        let shadowRoot = shadowHost.shadowRoot;
        let w3m_router = shadowRoot.querySelector('w3m-router');
        let normal_div = w3m_router.shadowRoot.querySelector('div');
        let connect_view = normal_div.querySelector('w3m-connect-view');
        let login_list = connect_view.shadowRoot.querySelector('w3m-wallet-login-list');
        let connect_list = login_list.shadowRoot.querySelector('wui-flex').querySelector('w3m-connector-list');
        let w3m_connect_injected_widget = connect_list.shadowRoot.querySelector('w3m-connect-injected-widget');
        let btn_okx_wallet = w3m_connect_injected_widget.shadowRoot.querySelector('wui-list-wallet[data-testid="wallet-selector-com.okex.wallet"]');
        return btn_okx_wallet;
    """

    JS_SCRIPT_SWITCH_NETWORK = """
        let switch_network_button = document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-unsupported-chain-view").shadowRoot.querySelector("wui-flex > wui-flex:nth-child(2) > wui-list-network").shadowRoot.querySelector("button");
        return switch_network_button;
    """

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "colormp"
        self.home_url = "https://www.colormp.com/badge"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"

    def _connect_wallet(self, page) -> bool:
        """连接钱包"""
        try:
            # 检查是否已连接
            connected_address = page.ele("x://button[@aria-haspopup='menu']", timeout=5)
            if connected_address:
                logger.info(f"{self.browser_id} 钱包已连接")
                return True

            connect_button = page.ele("x://button[text()='Connect Wallet']", timeout=5)
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            # 切换站点
            is_success = OKXWallet(self.browser.browser).change_connect_site(
                "Story Odyssey Testnet"
            )
            if not is_success:
                raise WalletConnectionError(f"{self.browser_id} 切换站点失败")

            connect_button.click()

            btn_okx_wallet = page.run_js(self.JS_SCRIPT_CHOOSE_WALLET)
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # 切换网络
            try:
                sleep(1)
                page.run_js(self.JS_SCRIPT_SWITCH_NETWORK).click()
                sleep(1)
            except Exception:
                pass

            # 检查连接状态
            connected_address = page.ele("x://button[@aria-haspopup='menu']", timeout=5)
            if connected_address:
                logger.success(f"{self.browser_id} 钱包连接成功")
                return True

            raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _check_task_status(self, page, task_text: str) -> bool:
        """检查任务状态是否完成

        Args:
            page: 页面对象
            task_text: 任务文本描述

        Returns:
            bool: 任务是否已完成
        """
        try:
            complete = page.ele(
                f"""x://p[text()="{task_text}"]/following-sibling::div[text()='Complete']""",
                timeout=3,
            )
            if complete:
                logger.success(f"{self.browser_id} {task_text}任务已完成")
                return True
            return False
        except Exception as e:
            logger.error(f"{self.browser_id} 检查任务状态失败: {e}")
            return False

    def _task_mint_badge(self, page) -> bool:
        """领取勋章任务"""
        try:
            # 点击同步按钮
            self._sync_nft(page)

            # 检查资格
            if page.ele("x://span[text()='Qualifications not met']", timeout=3):
                return False

            # 检查是否已领取
            if page.ele("x://span[text()='Badge already claimed']", timeout=3):
                logger.success(f"{self.browser_id} 勋章已领取")
                self._update_task_status(True)
                return True

            # 领取勋章
            claim_badge_button = page.ele("x://span[text()='Claim Badge']", timeout=3)
            if not claim_badge_button:
                raise TaskExecutionError(f"{self.browser_id} 查找Claim Badge按钮失败")

            claim_badge_button.click()
            self._sign_okx_wallet()

            # 等待领取结果
            congratulations = page.ele("Congratulations", timeout=60)
            if not congratulations:
                raise TaskExecutionError(f"{self.browser_id} 领取勋章失败")

            self._update_task_status(True)
            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 领取勋章失败: {str(e)}")

    def _task_email(self, page) -> bool:
        """邮箱任务"""
        try:
            # 检查邮箱任务状态
            if self._check_task_status(page, "Verify your email address"):
                return True

            # 获取邮箱按钮
            email_button = page.ele(
                """x://p[text()="Verify your email address"]/following-sibling::button""",
                timeout=5,
            )
            if not email_button:
                raise TaskExecutionError(f"{self.browser_id} 查找邮箱按钮失败")

            # 如果按钮文本是 Verify，进行邮箱验证
            if "Verify" in email_button.text:
                email_button.click()
                return self._verify_email(page)

            # 如果按钮文本是 Sign Up，进行邮箱注册
            if "Sign Up" in email_button.text:
                # 注册邮箱
                result = self._sign_up(page)
                if not result:
                    raise TaskExecutionError(f"{self.browser_id} 注册邮箱失败")

                # # 点击同步按钮
                # self._sync_nft(page)
                page.refresh()
                sleep(3)

                # 验证邮箱
                email_button = page.ele(
                    """x://p[text()="Verify your email address"]/following-sibling::button""",
                    timeout=5,
                )
                if email_button:
                    email_button.click()
                    return self._verify_email(page)

            raise TaskExecutionError(f"{self.browser_id} 邮箱验证失败")

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 邮箱任务失败: {str(e)}")

    def _sign_up(self, page) -> bool:
        """注册邮箱"""
        try:
            address = self.browser.browser_config.evm_address
            if not address:
                raise TaskExecutionError(f"{self.browser_id} 钱包地址为空")

            lasted_tab = page.new_tab("https://www.colormp.com/account/" + address)
            try:
                # 检查是否已注册
                if lasted_tab.ele("Balance:", timeout=3):
                    logger.success(f"{self.browser_id} 邮箱已验证")
                    return True

                # 输入邮箱
                email_input = lasted_tab.ele("x://input[@type='email']", timeout=5)
                if not email_input:
                    raise TaskExecutionError(f"{self.browser_id} 查找邮箱输入框失败")

                email = self.browser.browser_config.email
                if not email:
                    raise TaskExecutionError(f"{self.browser_id} 邮箱为空")

                email_input.input(email)

                # 点击注册
                sign_up_button = lasted_tab.ele(
                    "x://button[text()='Sign up']", timeout=5
                )
                if not sign_up_button:
                    raise TaskExecutionError(f"{self.browser_id} 查找Sign up按钮失败")

                sign_up_button.click()

                # 验证注册结果
                if not lasted_tab.ele("Balance:", timeout=60):
                    raise TaskExecutionError(f"{self.browser_id} 添加邮箱失败")

                logger.success(f"{self.browser_id} 添加邮箱成功")
                return True

            finally:
                lasted_tab.close()

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 注册邮箱失败: {str(e)}")

    def _task_join_discord(self, page) -> bool:
        """加入Discord任务"""
        try:
            # 检查任务状态
            if self._check_task_status(page, "Join Color's official Discord"):
                return True

            # 点击加入按钮
            join_button = page.ele("x://button[text()='Join']", timeout=5)
            if not join_button:
                raise TaskExecutionError(f"{self.browser_id} 查找Join按钮失败")

            join_button.click()
            logger.success(f"{self.browser_id} 加入Color的Discord服务器成功")
            sleep(3)
            self._close_other_tabs(page)

            # 验证任务完成
            complete_discord = page.ele(
                """x://p[text()="Join Color's official Discord"]/following-sibling::div[text()='Complete']""",
                timeout=3,
            )
            if complete_discord:
                logger.success(f"{self.browser_id} Discord 加入任务已完成")
                return True

            raise TaskExecutionError(f"{self.browser_id} Discord 加入任务失败")

        except Exception as e:
            raise TaskExecutionError(
                f"{self.browser_id} Discord 加入任务失败: {str(e)}"
            )

    def _task_follow_x(self, page) -> bool:
        """关注X任务"""
        try:
            # 检查任务状态
            if self._check_task_status(page, "Follow Color's official X account"):
                logger.success(f"{self.browser_id} 关注X任务已完成")
                return True

            # 点击关注按钮
            follow_button = page.ele("x://button[text()='Follow']", timeout=5)
            if not follow_button:
                raise TaskExecutionError(f"{self.browser_id} 查找Follow按钮失败")

            follow_button.click()
            sleep(3)
            self._close_other_tabs(page)

            # 验证任务完成
            complete_x = page.ele(
                """x://p[text()="Follow Color's official X account"]/following-sibling::div[text()='Complete']""",
                timeout=3,
            )
            if complete_x:
                logger.success(f"{self.browser_id} X 关注任务已完成")
                return True

            raise TaskExecutionError(f"{self.browser_id} X 关注任务失败")

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} X 关注任务失败: {str(e)}")

    def _task_mint_nft(self, page) -> bool:
        """Mint NFT任务"""
        try:
            # 检查任务状态
            if self._check_task_status(
                page, "Mint the Color Commemorative Odyssey NFT"
            ):
                logger.success(f"{self.browser_id} mint nft任务已完成")
                return True

            # 打开NFT页面
            lasted_tab = page.new_tab("https://www.colormp.com/launchpad/odyssey-nft")
            try:
                # 点击Mint按钮
                result = try_click(lasted_tab, "x://button[text()='Mint']", timeout=5)
                if not result:
                    raise TaskExecutionError(f"{self.browser_id} mint nft失败")

                self._sign_okx_wallet()

                # 等待Mint结果
                success_text = lasted_tab.ele(
                    "You've successfully minted your Color Commemorative Launch NFT!",
                    timeout=60,
                )
                if not success_text:
                    raise TaskExecutionError(f"{self.browser_id} mint nft失败")

                return True

            finally:
                lasted_tab.close()

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} mint nft失败: {str(e)}")

    def _select_valid_nft(self, tab) -> Optional[str]:
        """选择有效的NFT"""
        try:
            nft_img = tab.ele(
                "x:(//img[@class='rounded-t-md aspect-square'])[1]", timeout=5
            )
            if not nft_img:
                raise TaskExecutionError(f"{self.browser_id} 查找NFT图片失败")

            nft_name = nft_img.attr("alt")
            if not nft_name:
                raise TaskExecutionError(f"{self.browser_id} 获取NFT名称失败")

            if "Odyssey Testnet Commemorative IP Asset" in nft_name:
                logger.warning(f"{self.browser_id} 此NFT不进行上架, 请检查账号")
                return None

            nft_img.click()
            return nft_img
        except Exception as e:
            logger.error(f"{self.browser_id} 选择NFT失败: {e}")
            return None

    def _register_story_protocol(self, tab, nft_img) -> bool:
        """注册Story Protocol"""
        try:
            if nft_img.next().ele("IP Asset", timeout=5):
                logger.success(f"{self.browser_id} 已注册Story Protocol")
                return True

            register_button = tab.ele(
                "x://button[text()='Register on Story Protocol']", timeout=5
            )
            if not register_button:
                raise TaskExecutionError(
                    f"{self.browser_id} 查找Register on Story Protocol按钮失败"
                )

            register_button.click()
            self._sign_okx_wallet()

            if not nft_img.next().ele("IP Asset", timeout=60):
                raise TaskExecutionError(f"{self.browser_id} 注册Story Protocol失败")

            return True
        except Exception as e:
            logger.error(f"{self.browser_id} 注册Story Protocol失败: {e}")
            return False

    def _mint_license(self, tab) -> bool:
        """铸造许可证"""
        try:
            mint_license_button = tab.ele(
                "x://button[text()='Mint License']", timeout=5
            )
            if not mint_license_button:
                raise TaskExecutionError(f"{self.browser_id} 查找Mint License按钮失败")
            mint_license_button.click()

            mint_button = tab.ele("x://button[text()='Mint']", timeout=5)
            if not mint_button:
                raise TaskExecutionError(f"{self.browser_id} 查找Mint按钮失败")

            mint_button.click()
            sleep(5)
            self._sign_okx_wallet()

            if tab.ele("Transaction completed succesfully!", timeout=60):
                return True

            raise TaskExecutionError(f"{self.browser_id} 领取许可证失败")
        except Exception as e:
            logger.error(f"{self.browser_id} 铸造许可证失败: {e}")
            return False

    # Register an IP Asset
    def _task_register_ip_asset(self, page) -> bool:
        """注册IP资产"""
        if self._check_task_status(page, "Register an IP Asset"):
            return True

        lasted_tab = page.new_tab(
            f"https://www.colormp.com/account/{self.wallet_address}"
        )
        try:
            sleep(3)
            # 选择NFT
            nft_img = self._select_valid_nft(lasted_tab)
            if not nft_img:
                return False

            # 注册Story Protocol
            if not self._register_story_protocol(lasted_tab, nft_img):
                return False

            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 注册IP资产失败: {str(e)}")
        finally:
            lasted_tab.close()

    def _task_mint_license(self, page) -> bool:
        """mint许可证主流程"""

        # 检查任务状态
        if self._check_task_status(page, "Mint a license"):
            logger.success(f"{self.browser_id} 领取许可证任务已完成")
            return True

        lasted_tab = page.new_tab(
            f"https://www.colormp.com/account/{self.wallet_address}"
        )
        try:
            sleep(3)

            # 选择NFT
            nft_img = self._select_valid_nft(lasted_tab)
            if not nft_img:
                return False

            # 注册Story Protocol
            if not self._register_story_protocol(lasted_tab, nft_img):
                return False

            # 查看详情
            view_details_button = lasted_tab.ele(
                "x://button[text()='View Details']", timeout=5
            )
            if not view_details_button:
                raise TaskExecutionError(f"{self.browser_id} 查找View Details按钮失败")

            view_details_button.click()
            sleep(3)
            if "assets" not in lasted_tab.url:
                raise TaskExecutionError(f"{self.browser_id} 进入详情页面失败")

            # 铸造许可证
            return self._mint_license(lasted_tab)

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} mint许可证失败: {str(e)}")
        finally:
            lasted_tab.close()

    @retry(tries=3, delay=3)
    def _task_buy_nft(self, page) -> bool:
        """购买NFT任务"""
        try:
            # # 检查任务状态
            # if self._check_task_status(page, "Buy an NFT from the marketplace"):
            #     logger.success(f"{self.browser_id} 市场购买nft任务已完成")
            #     return True

            # NFT合集列表
            collections = [
                "0x112385c32d6e16229d688B3116F33a8897609399",
                "0xCa8086c0fF4E5444Dfd82118eF2E73de485E148A",
                "0xba13A15E618aeb2AcABb854DF65b7B44598fE89C",
                "0x39526F6B2D66CCBf00c77D5C016BD1FE455d920B",
                "0x405279e484C62aec39D7D525e1F55333135edAC9",
                "0x090Cb89aeAE80963D58F23e0bdBC74dADD498fbD",
            ]

            # 随机选择一个合集
            random_collection = random.choice(collections)
            lasted_tab = page.new_tab(
                "https://www.colormp.com/collections/" + random_collection
            )
            try:
                sleep(3)
                # 选择NFT
                item_imgs = lasted_tab.eles(
                    "x://img[@data-tooltip-content='Click to select item']", timeout=5
                )
                if not item_imgs:
                    raise TaskExecutionError(f"{self.browser_id} 查找item图片失败")

                random_index = random.randint(0, min(len(item_imgs) - 1, 5))
                item_imgs[random_index].click()

                # 购买NFT
                buy_button = lasted_tab.ele(
                    "x://button/div[contains(text(), 'Buy')]", timeout=5
                )
                if not buy_button:
                    raise TaskExecutionError(f"{self.browser_id} 查找buy按钮失败")

                buy_button.click()
                sleep(5)
                self._sign_okx_wallet()

                # 等待购买结果
                for _ in range(20):
                    buy_button = lasted_tab.ele(
                        "x://button/div[contains(text(), 'Buy')]", timeout=1
                    )
                    if not buy_button:
                        return True
                    sleep(3)

                raise TaskExecutionError(f"{self.browser_id} 购买nft失败")

            finally:
                lasted_tab.close()

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 购买nft失败: {str(e)}")

    def _verify_email(self, page) -> bool:
        """验证邮箱"""
        try:
            # 等待邮箱验证弹窗出现
            dialog_panel = page.ele(
                """x://div[contains(@id, "headlessui-dialog-panel")]""", timeout=5
            )
            if not dialog_panel:
                raise TaskExecutionError(f"{self.browser_id} 邮箱验证弹窗未出现")

            # 获取验证码
            captcha_token = self._get_captcha_token(page)
            if not captcha_token:
                raise TaskExecutionError(f"{self.browser_id} 获取验证码失败")

            # 发送验证邮件
            send_verification_email_button = page.ele(
                "x://button[text()='Send Verification Email']", timeout=5
            )
            if not send_verification_email_button:
                raise TaskExecutionError(
                    f"{self.browser_id} 查找Send Verification Email按钮失败"
                )

            send_verification_email_button.click()

            # 获取验证链接
            verification_url = self._get_verification_url(page)
            if not verification_url:
                raise TaskExecutionError(f"{self.browser_id} 获取验证链接失败")

            # 打开验证链接
            logger.info(f"{self.browser_id} 打开验证链接: {verification_url}")
            verification_tab = page.new_tab(verification_url)
            try:
                # 等待验证成功
                verification_success = verification_tab.ele(
                    "Email verified successfully"
                )
                if not verification_success:
                    raise TaskExecutionError(f"{self.browser_id} 验证失败")

                return True
            finally:
                verification_tab.close()

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 邮箱验证失败: {str(e)}")

    def _get_captcha_token(self, page) -> Optional[str]:
        return YesCaptcha.get_web_plugin_token_hcaptcha(page, self.browser_id)

    def _get_verification_url_gmail(self, page) -> Optional[str]:
        """从Gmail获取验证链接"""

        def get_colormp_verify_link(tab) -> Optional[str]:
            verify_link = tab.ele(
                """x://a[contains(@href, 'sendgrid.net') and contains(text(),'Verify Email')]""",
                timeout=5,
            )
            return verify_link.attr("href") if verify_link else None

        gmail_client = GmailWebClient(self.browser)
        return gmail_client.get_gmail_verification_url(
            sender_email="<EMAIL>",
            get_link_func=get_colormp_verify_link,
        )

    def _get_verification_url_icloud(self, page) -> Optional[str]:
        """从iCloud获取验证链接"""
        """获取验证码"""
        from src.emails import ICloudClient, SearchCriteria

        # icloud 采用隐藏邮箱模式，所以需要使用真实邮箱
        email = ICLOUD_EMAIL
        password = ICLOUD_EMAIL_PASSWORD

        if not email or not password:
            raise TaskExecutionError(
                f"{self.browser_id} 邮箱或密码为空, 请检查.env配置"
            )

        try:
            with ICloudClient(email, password).connect() as client:
                # 搜索验证码邮件
                emails = client.search_emails_with_retry(
                    SearchCriteria(
                        folder="INBOX",
                        subject="Verify your email on Color MP",
                    )
                )

                if not emails:
                    emails = client.search_emails_with_retry(
                        SearchCriteria(
                            folder="Junk",
                            subject="Verify your email on Color MP",
                        )
                    )

                if not emails:
                    logger.error(f"{self.browser_id} 未找到验证邮件")
                    return None

                # 检查收件人是否匹配
                email_info = emails[0]
                to_email = email_info["to"]
                hidden_email = self.browser.browser_config.email.lower()
                if hidden_email not in to_email.lower():
                    logger.error(
                        f"{self.browser_id} 验证邮件收件人不匹配: {to_email} != {email}"
                    )
                    return None

                # 从邮件内容中提取验证码
                content = email_info["content"]
                verification_url = self._extract_verification_url(content)
                if not verification_url:
                    logger.error(f"{self.browser_id} 提取验证码失败")
                    return None

                logger.success(f"{self.browser_id} 匹配到的url: {verification_url}")

                # 删除邮件
                client.delete_emails([email_info["id"]])
                return verification_url

        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
            return None

    def _extract_verification_url(self, content: str) -> Optional[str]:
        """从邮件内容中提取验证链接"""
        try:
            ele = make_session_ele(content)
            text_content = ele.text

            # 直接匹配 colormp.com 的验证链接
            pattern = r"https://colormp\.com/api/v1/users/verify\?token=[^\n]+"
            match = re.search(pattern, text_content)

            if match:
                url = match.group(0)
                return url

            logger.error("未找到验证链接")
            return None

        except Exception as e:
            logger.error(f"提取验证链接失败: {str(e)}")
            return None

    def _get_verification_url(self, page) -> Optional[str]:
        """获取验证链接"""
        email = self.browser.browser_config.email
        if not email:
            raise TaskExecutionError(f"{self.browser_id} 邮箱为空")

        if "gmail" in email:
            return self._get_verification_url_gmail(page)
        elif "icloud" in email:
            return self._get_verification_url_icloud(page)

        return None

    def _sync_nft(self, page) -> bool:
        """同步NFT状态"""
        try:
            sync_button = page.ele("x://button[text()='Sync Eligibility']", timeout=5)
            if not sync_button or not sync_button.states.is_clickable:
                logger.warning(f"{self.browser_id} 同步按钮不可点击")
                return False

            sync_button.click()
            sleep(3)  # 等待同步完成
            return True

        except Exception as e:
            logger.error(f"{self.browser_id} 同步NFT状态失败: {e}")
            return False

    def _task_buy_license(self, page) -> bool:
        """购买许可证"""
        # # 检查任务状态
        # if self._check_task_status(page, "Buy a License from the marketplace"):
        #     logger.success(f"{self.browser_id} 领取许可证任务已完成")
        #     return True

        # 打开许可证页面
        base_url = "https://www.colormp.com/collections"
        contract_addresses = [
            "0x112385c32d6e16229d688B3116F33a8897609399",
            "0xba13A15E618aeb2AcABb854DF65b7B44598fE89C",
            "0xD5cb863Dfbb1B280D76b5d41E6a2c54F6C5302a3",
            "0x704Eb7828c0959C621184b2A2f651B1CE3dFBD49",
            "0x14A08f17BF7a4C844288E120efCDF5A32436b934",
            "0xFF2b94520B5BFBfF8134aAC59cB6AF7295c6c0b6",
            "0xaA5fd79315223Dd5FA796a56ef6624808D6FF0FC",
            "0xCa8086c0fF4E5444Dfd82118eF2E73de485E148A",
        ]

        random_address = random.choice(contract_addresses)
        random_url = f"{base_url}/{random_address}/licenses"
        lasted_tab = page.new_tab(random_url)
        try:
            sleep(3)

            # 选择license
            item_imgs = lasted_tab.eles(
                "x://img[@data-tooltip-content='Click to select item']", timeout=5
            )
            if not item_imgs:
                raise TaskExecutionError(f"{self.browser_id} 查找item图片失败")

            random_index = random.randint(0, min(len(item_imgs) - 1, 5))
            item_imgs[random_index].click()

            # 购买license
            buy_button = lasted_tab.ele(
                "x://button/div[contains(text(), 'Buy')]", timeout=5
            )
            if not buy_button:
                raise TaskExecutionError(f"{self.browser_id} 查找buy按钮失败")

            buy_button.click()
            self._sign_okx_wallet()

            # 等待购买结果
            for _ in range(20):
                buy_button = lasted_tab.ele(
                    "x://button/div[contains(text(), 'Buy')]", timeout=1
                )
                if not buy_button:
                    return True
                sleep(3)

            raise TaskExecutionError(f"{self.browser_id} 市场购买nft失败")

        finally:
            lasted_tab.close()

    def _execute_task(self, task_func, task_name: str) -> bool:
        """执行单个任务的通用方法"""
        try:
            result = task_func(self.browser.page)
            if result:
                logger.success(f"{self.browser_id} {task_name}成功")
            return result
        except Exception as e:
            logger.error(f"{self.browser_id} {task_name}失败: {e}")
            return False

    def _task_list_item_for_sale(self, page) -> bool:
        """上架NFT"""
        # 检查任务状态
        if self._check_task_status(page, "List an item for sale"):
            logger.success(f"{self.browser_id} 上架NFT任务已完成")
            return True

        lasted_tab = page.new_tab(
            "https://www.colormp.com/account/" + self.wallet_address
        )
        try:
            sleep(3)

            # 点击第一个NFT
            nft_img = lasted_tab.ele(
                "x:(//img[@class='rounded-t-md aspect-square'])[1]", timeout=5
            )
            if not nft_img:
                raise TaskExecutionError(f"{self.browser_id} 查找NFT图片失败")

            nft_name = nft_img.attr("alt")
            if not nft_name:
                raise TaskExecutionError(f"{self.browser_id} 获取NFT名称失败")

            if "Odyssey Testnet Commemorative IP Asset" in nft_name:
                logger.warning(f"{self.browser_id} 此NFT不进行上架, 请检查账号")
                return False

            if "Color Commemorative Odyssey NFT" in nft_name:
                logger.warning(f"{self.browser_id} 此NFT不进行上架, 请检查账号")
                return False

            if nft_img.parent().parent().ele("Derivative", timeout=3):
                logger.warning(f"{self.browser_id} 此NFT不进行上架, 请检查账号")
                return False

            nft_img.click()

            list_for_sale_button = lasted_tab.ele(
                "x://button[text()='List for Sale']", timeout=5
            )
            if not list_for_sale_button:
                raise TaskExecutionError(f"{self.browser_id} 查找List for Sale按钮失败")

            list_for_sale_button.click()

            # 判断是否显示弹窗
            aria_modal = lasted_tab.ele("x://div[@aria-modal='true']", timeout=5)
            if not aria_modal:
                raise TaskExecutionError(f"{self.browser_id} 显示上架NFT弹窗页面失败")

            # 输入价格
            price_input = lasted_tab.ele("x://input[@type='number']", timeout=5)
            if not price_input:
                raise TaskExecutionError(f"{self.browser_id} 查找价格输入框失败")

            # 生成0.1-1之间的随机价格
            random_price = round(random.uniform(0.1, 1), 2)
            price_input.input(str(random_price))

            # 点击确认按钮
            confirm_button = lasted_tab.ele(
                "x://button[text()='List for sale']", timeout=5
            )
            if not confirm_button:
                raise TaskExecutionError(f"{self.browser_id} 查找确认按钮失败")

            confirm_button.click()
            sleep(5)
            self._sign_okx_wallet()
            sleep(5)
            self._sign_okx_wallet()

            on_sale_tag = lasted_tab.ele(
                "x://span[contains(text(), 'On Sale')]", timeout=60
            )
            if not bool(on_sale_tag):
                raise TaskExecutionError(f"{self.browser_id} 上架NFT失败")

            return True

        finally:
            lasted_tab.close()

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开徽章页面
            self.browser.open_url(self.home_url)

            # 切换站点
            is_success = OKXWallet(self.browser.browser).change_connect_site(
                "Story Odyssey Testnet"
            )
            if not is_success:
                raise WalletConnectionError(f"{self.browser_id} 切换站点失败")

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 定义任务列表
            tasks = [
                (self._task_buy_nft, "购买NFT"),
                (self._task_buy_license, "购买许可证"),
            ]

            # 随机执行任务
            task = random.choice(tasks)
            task_func, task_name = task
            if self._execute_task(task_func, task_name):
                return True

            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Colormp任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Colormp任务失败")

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Colormp任务"""
        if self._check_task_completed():
            return True

        try:

            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开徽章页面
            self.browser.open_url(self.home_url)

            # 切换站点
            is_success = OKXWallet(self.browser.browser).change_connect_site(
                "Story Odyssey Testnet"
            )
            if not is_success:
                raise WalletConnectionError(f"{self.browser_id} 切换站点失败")

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # gas低了再启用
            gas_prices = self.get_gas_price()
            if gas_prices and gas_prices.average > MAX_GAS_PRICE:
                logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
                return False

            # 定义任务列表
            tasks = [
                (self._task_mint_badge, "领取勋章"),
                (self._task_email, "邮箱任务"),
                (self._task_follow_x, "关注X"),
                (self._task_join_discord, "加入Discord"),
                (self._task_mint_nft, "mint NFT"),
                (self._task_buy_nft, "购买NFT"),
                (self._task_buy_license, "购买许可证"),
                # (self._task_register_ip_asset, "注册IP资产"),
                # (self._task_mint_license, "mint license"),
                # (self._task_list_item_for_sale, "上架NFT"),
                (self._task_mint_badge, "最终领取勋章"),
            ]

            # 执行任务列表
            for task_func, task_name in tasks:
                if task_name == "领取勋章":
                    if self._execute_task(task_func, task_name):
                        return True
                    continue
                elif task_name == "最终领取勋章":
                    # 最后一次领取勋章的结果决定整个任务的成功失败
                    result = self._execute_task(task_func, task_name)
                    if result:
                        logger.success(f"{self.browser_id} Colormp任务完成")
                    else:
                        logger.warning(f"{self.browser_id} Colormp任务未完成")
                    return result
                else:
                    self._execute_task(task_func, task_name)

            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Colormp任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Colormp任务失败")
