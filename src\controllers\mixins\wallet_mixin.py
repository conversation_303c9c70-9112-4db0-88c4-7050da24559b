from typing import Optional

from config import (
    BACK<PERSON>CK_WALLET_PASSWORD,
    K<PERSON><PERSON>_WALLET_PASSWORD,
    METAMASK_WALLET_PASSWORD,
    OKX_WALLET_PASSWORD,
    RAZOR_WALLET_PASSWORD,
)
from src.enums.wallet_enums import Gas<PERSON>evel
from src.utils.secure_encryption import SecureEncryption
from src.wallets import Meta<PERSON>askWallet, OKXWallet
from src.wallets.backpack_wallet import BackpackWallet
from src.wallets.keplr_wallet import KeplrWallet
from src.wallets.razor_wallet import RazorWallet

from ..decorators import require_page_and_config
from ..exceptions import BrowserControllerError


class WalletMixin:
    # 导入助记词到OKX钱包
    @require_page_and_config
    def okx_wallet_setup(
        self,
        password: str | None = None,
        is_key: bool = False,
        wallet_name: str | None = None,
    ) -> bool:
        """设置OKX钱包."""
        phrase_key = self.browser_config.evm_private_key if is_key else self.browser_config.mnemonic

        if not phrase_key:
            raise BrowserControllerError(f"{self.id} 助记词/私钥未配置")

        password = password or OKX_WALLET_PASSWORD

        if SecureEncryption.is_encrypted(phrase_key):
            phrase_key = SecureEncryption.decrypt(phrase_key)

        return OKXWallet(self.browser).setup(password, phrase_key, is_key, wallet_name)

    @require_page_and_config
    def okx_wallet_reset(self) -> bool:
        """重置OKX钱包."""
        return OKXWallet(self.browser).reset()

    @require_page_and_config
    def okx_wallet_remove_custom_rpc(self, name: str) -> bool:
        """移除OKX钱包自定义RPC."""
        return OKXWallet(self.browser).remove_custom_rpc(name)

    @require_page_and_config
    def okx_wallet_login(self, password: str | None = None) -> bool:
        """登录OKX钱包."""
        password = password or OKX_WALLET_PASSWORD
        return OKXWallet(self.browser).login(password)

    @require_page_and_config
    def okx_wallet_sign(self, password: str | None = None) -> bool:
        """签名."""
        # password = password or OKX_WALLET_PASSWORD
        return OKXWallet(self.browser).sign()

    @require_page_and_config
    def okx_wallet_approve(self, gas_level: GasLevel = GasLevel.AVERAGE) -> bool:
        """授权."""
        # password = password or OKX_WALLET_PASSWORD
        return OKXWallet(self.browser).approve(gas_level)

    @require_page_and_config
    def okx_wallet_connect(self, password: str | None = None) -> bool:
        """连接钱包."""
        password = password or OKX_WALLET_PASSWORD
        return OKXWallet(self.browser).connect(password)

    @require_page_and_config
    def okx_wallet_change_chain(self, target_network):
        return OKXWallet(self.browser).change_network(target_network)

    @require_page_and_config
    def okx_wallet_disconnect_site_all(self):
        return OKXWallet(self.browser).disconnect_site_all()

    @require_page_and_config
    def okx_wallet_change_connect_site(self, site):
        return OKXWallet(self.browser).change_connect_site(site)

    # 导入助记词到metamask钱包
    @require_page_and_config
    def metamask_wallet_setup(self, password: str | None = None) -> bool:
        """设置metamask钱包."""
        phrase = self.browser_config.mnemonic
        if not phrase:
            raise BrowserControllerError(f"{self.id} 助记词未配置")

        if SecureEncryption.is_encrypted(phrase):
            phrase = SecureEncryption.decrypt(phrase)

        password = password or METAMASK_WALLET_PASSWORD

        return MetaMaskWallet(self.browser).setup(password, phrase)

    @require_page_and_config
    def metamask_wallet_login(self, password: str | None = None) -> bool:
        """登录metamask钱包."""
        password = password or METAMASK_WALLET_PASSWORD
        return MetaMaskWallet(self.browser).login(password)

    @require_page_and_config
    def metamask_wallet_connect(self) -> bool:
        """连接钱包."""
        return MetaMaskWallet(self.browser).connect()

    @require_page_and_config
    def backpack_wallet_setup(self, mnemonic: str, password: str | None = None) -> bool:
        """设置backpack钱包."""
        password = password or BACKPACK_WALLET_PASSWORD
        if SecureEncryption.is_encrypted(mnemonic):
            mnemonic = SecureEncryption.decrypt(mnemonic)

        return BackpackWallet(self.browser).setup(password, mnemonic)

    @require_page_and_config
    def backpack_wallet_login(self, password: str | None = None) -> bool:
        """登录backpack钱包."""
        password = password or BACKPACK_WALLET_PASSWORD
        return BackpackWallet(self.browser).login(password)

    @require_page_and_config
    def backpack_wallet_sign(self) -> bool:
        """签名."""
        return BackpackWallet(self.browser).sign()

    @require_page_and_config
    def backpack_wallet_approve(self) -> bool:
        """授权."""
        return BackpackWallet(self.browser).approve()

    @require_page_and_config
    def backpack_wallet_connect(self, password: str | None = None) -> bool:
        """连接钱包."""
        password = password or BACKPACK_WALLET_PASSWORD
        return BackpackWallet(self.browser).connect(password)

    @require_page_and_config
    def keplr_wallet_setup(self, mnemonic: str, password: str | None = None) -> bool:
        """设置keplr钱包."""
        password = password or KEPLR_WALLET_PASSWORD
        if SecureEncryption.is_encrypted(mnemonic):
            mnemonic = SecureEncryption.decrypt(mnemonic)

        return KeplrWallet(self.browser).setup(password, mnemonic)

    @require_page_and_config
    def keplr_wallet_login(self, password: str | None = None) -> bool:
        """登录keplr钱包."""
        password = password or KEPLR_WALLET_PASSWORD
        return KeplrWallet(self.browser).login(password)

    @require_page_and_config
    def razor_wallet_setup(self, mnemonic: str, password: str | None = None) -> bool:
        """设置razor钱包."""
        password = password or RAZOR_WALLET_PASSWORD
        if SecureEncryption.is_encrypted(mnemonic):
            mnemonic = SecureEncryption.decrypt(mnemonic)

        return RazorWallet(self.browser).setup(password, mnemonic)

    @require_page_and_config
    def razor_wallet_login(self, password: str | None = None) -> bool:
        """登录razor钱包."""
        password = password or RAZOR_WALLET_PASSWORD
        return RazorWallet(self.browser).login(password)
