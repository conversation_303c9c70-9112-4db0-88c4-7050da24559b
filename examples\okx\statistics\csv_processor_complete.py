#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的CSV过滤和匹配脚本
根据satlayer2_chrome.csv中的status字段过滤数据，然后在chrome.csv中匹配钱包地址
包含完整的错误处理、日志记录、数据验证和报告生成功能
"""

import pandas as pd
import os
import sys
import logging
import argparse
from datetime import datetime
from typing import Tuple, Optional, List, Dict, Any
import json


class CSVProcessor:
    """CSV处理器类"""

    def __init__(self, log_level: str = "INFO"):
        """初始化处理器"""
        self.setup_logging(log_level)
        self.stats = {
            "original_count": 0,
            "filtered_count": 0,
            "matched_count": 0,
            "processing_time": 0,
        }

    def setup_logging(self, log_level: str) -> None:
        """设置日志配置"""
        log_format = "%(asctime)s - %(levelname)s - %(message)s"
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(
                    f"csv_processing_{datetime.now().strftime('%Y%m%d')}.log"
                ),
                logging.StreamHandler(sys.stdout),
            ],
        )
        logging.info("CSV处理器初始化完成")

    def validate_file_exists(self, file_path: str) -> bool:
        """验证文件是否存在"""
        if not os.path.exists(file_path):
            logging.error(f"文件不存在: {file_path}")
            return False

        file_size = os.path.getsize(file_path)
        logging.info(f"文件验证通过: {file_path} (大小: {file_size:,} 字节)")
        return True

    def load_csv_with_validation(
        self, file_path: str, required_columns: List[str] = None
    ) -> Optional[pd.DataFrame]:
        """
        加载CSV文件并验证必要的列

        Args:
            file_path: CSV文件路径
            required_columns: 必需的列名列表

        Returns:
            DataFrame或None（如果加载失败）
        """
        try:
            logging.info(f"开始加载文件: {file_path}")
            df = pd.read_csv(file_path)
            logging.info(
                f"成功加载 {file_path}，共 {len(df):,} 行，{len(df.columns)} 列"
            )

            if required_columns:
                missing_columns = [
                    col for col in required_columns if col not in df.columns
                ]
                if missing_columns:
                    logging.error(f"文件 {file_path} 缺少必需的列: {missing_columns}")
                    logging.info(f"可用的列: {list(df.columns)}")
                    return None
                logging.info(f"列验证通过: {required_columns}")

            # 显示数据基本信息
            logging.info(f"数据类型信息:")
            for col in df.columns[:10]:  # 只显示前10列的信息
                dtype = df[col].dtype
                null_count = df[col].isnull().sum()
                logging.info(f"  {col}: {dtype}, 空值: {null_count}")

            return df

        except Exception as e:
            logging.error(f"加载文件 {file_path} 时出错: {str(e)}")
            return None

    def filter_status_data(
        self, df: pd.DataFrame, status_column: str = "status"
    ) -> pd.DataFrame:
        """
        过滤status字段为空或为0的数据

        Args:
            df: 输入DataFrame
            status_column: status列名

        Returns:
            过滤后的DataFrame
        """
        if status_column not in df.columns:
            logging.error(f"列 '{status_column}' 不存在于数据中")
            logging.info(f"可用的列: {list(df.columns)}")
            return pd.DataFrame()

        logging.info(f"开始过滤 {status_column} 字段...")

        # 分析status字段的值分布
        status_counts = df[status_column].value_counts(dropna=False)
        logging.info(f"Status字段值分布:")
        for value, count in status_counts.head(10).items():
            logging.info(f"  {repr(value)}: {count:,} 行")

        # 处理status字段，将空值和0都视为需要过滤的条件
        condition = (
            (df[status_column].isna())
            | (df[status_column] == 0)
            | (df[status_column] == "0")
            | (df[status_column] == "")
            | (df[status_column] == "None")
            | (df[status_column].astype(str).str.strip() == "")
        )

        filtered_df = df[condition].copy()

        logging.info(f"过滤结果: 原始 {len(df):,} 行 -> 过滤后 {len(filtered_df):,} 行")
        return filtered_df

    def match_wallet_addresses(
        self,
        satlayer_df: pd.DataFrame,
        chrome_df: pd.DataFrame,
        satlayer_addr_col: str = "address",
        chrome_addr_col: str = "evm_address",
    ) -> pd.DataFrame:
        """
        根据钱包地址匹配两个DataFrame，只返回chrome数据

        Args:
            satlayer_df: satlayer数据（用于获取需要匹配的地址）
            chrome_df: chrome数据
            satlayer_addr_col: satlayer中的地址列名
            chrome_addr_col: chrome中的地址列名

        Returns:
            匹配后的DataFrame（只包含chrome数据的所有列）
        """
        # 验证列是否存在
        if satlayer_addr_col not in satlayer_df.columns:
            logging.error(f"列 '{satlayer_addr_col}' 不存在于satlayer数据中")
            return pd.DataFrame()

        if chrome_addr_col not in chrome_df.columns:
            logging.error(f"列 '{chrome_addr_col}' 不存在于chrome数据中")
            return pd.DataFrame()

        logging.info(f"开始匹配钱包地址...")
        logging.info(
            f"Satlayer地址列: {satlayer_addr_col}, Chrome地址列: {chrome_addr_col}"
        )

        # 数据清理：去除空值和重复值
        satlayer_clean = satlayer_df.dropna(subset=[satlayer_addr_col])
        chrome_clean = chrome_df.dropna(subset=[chrome_addr_col])

        logging.info(
            f"清理后数据: Satlayer {len(satlayer_clean):,} 行, Chrome {len(chrome_clean):,} 行"
        )

        # 显示地址样例
        logging.info("Satlayer地址样例:")
        for i, addr in enumerate(satlayer_clean[satlayer_addr_col].head(5)):
            logging.info(f"  {i+1}. {addr}")

        logging.info("Chrome地址样例:")
        for i, addr in enumerate(chrome_clean[chrome_addr_col].head(5)):
            logging.info(f"  {i+1}. {addr}")

        # 获取需要匹配的地址列表（转换为小写进行匹配）
        target_addresses = set(satlayer_clean[satlayer_addr_col].str.lower())

        # 过滤chrome数据，只保留匹配的地址（只返回chrome数据的列）
        matched_data = chrome_clean[
            chrome_clean[chrome_addr_col].str.lower().isin(target_addresses)
        ].copy()

        # 数据类型优化：将id和browser_id转换为整型
        if "id" in matched_data.columns:
            matched_data["id"] = matched_data["id"].fillna(0).astype(int)
        if "browser_id" in matched_data.columns:
            matched_data["browser_id"] = (
                matched_data["browser_id"].fillna(0).astype(int)
            )

        logging.info(f"匹配结果: {len(matched_data):,} 行数据")

        if len(matched_data) > 0:
            logging.info("匹配成功的地址样例:")
            for i, addr in enumerate(matched_data[chrome_addr_col].head(5)):
                logging.info(f"  {i+1}. {addr}")

        return matched_data

    def save_results(
        self, df: pd.DataFrame, output_prefix: str = "filtered_matched_data"
    ) -> str:
        """
        保存结果到CSV文件

        Args:
            df: 要保存的DataFrame
            output_prefix: 输出文件名前缀

        Returns:
            输出文件路径
        """
        if df.empty:
            logging.warning("没有数据需要保存")
            return ""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{output_prefix}_{timestamp}.csv"

        try:
            df.to_csv(output_file, index=False, encoding="utf-8")
            file_size = os.path.getsize(output_file)
            logging.info(f"结果已保存到: {output_file} (大小: {file_size:,} 字节)")
            return output_file

        except Exception as e:
            logging.error(f"保存文件时出错: {str(e)}")
            return ""

    def generate_detailed_report(
        self,
        original_count: int,
        filtered_count: int,
        matched_count: int,
        output_file: str,
        matched_df: pd.DataFrame,
    ) -> None:
        """
        生成详细的处理报告

        Args:
            original_count: 原始数据行数
            filtered_count: 过滤后数据行数
            matched_count: 匹配成功行数
            output_file: 输出文件路径
            matched_df: 匹配后的DataFrame
        """
        print("\n" + "=" * 80)
        print("📊 CSV处理完成 - 详细报告")
        print("=" * 80)

        # 基本统计
        print(f"📁 输出文件: {output_file}")
        print(f"⏰ 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\n📈 数据流转统计:")
        print(f"  ├─ 原始satlayer数据: {original_count:,} 行")
        print(
            f"  ├─ 过滤后数据: {filtered_count:,} 行 ({filtered_count/original_count*100:.1f}%)"
        )
        print(
            f"  └─ 最终匹配成功: {matched_count:,} 行 ({matched_count/filtered_count*100:.1f}% 匹配率)"
        )

        # 文件信息
        if output_file and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"\n📄 输出文件信息:")
            print(f"  ├─ 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
            print(f"  ├─ 列数: {len(matched_df.columns)}")
            print(f"  └─ 行数: {len(matched_df)}")

        # 列信息
        if not matched_df.empty:
            print(f"\n📋 输出文件包含的列 ({len(matched_df.columns)} 个):")
            print(f"  🌐 Chrome数据列:")
            for i, col in enumerate(matched_df.columns, 1):
                print(f"    {i:2d}. {col}")

            # 数据质量分析
            print(f"\n🔍 数据质量分析:")

            # 检查关键列的空值情况
            key_columns = ["evm_address", "evm_private_key", "dc_user", "x_user"]
            for col in key_columns:
                if col in matched_df.columns:
                    null_count = matched_df[col].isnull().sum()
                    null_pct = (null_count / len(matched_df)) * 100
                    print(f"  ├─ {col}: {null_count} 个空值 ({null_pct:.1f}%)")

            # 显示关键数据预览
            preview_cols = ["evm_address", "dc_user", "x_user"]
            available_preview_cols = [
                col for col in preview_cols if col in matched_df.columns
            ]

            if available_preview_cols and len(matched_df) > 0:
                print(f"\n👀 关键数据预览 (前5行):")
                print(matched_df[available_preview_cols].head().to_string(index=False))

        print("=" * 80)
        print("✅ 处理完成！可以使用输出文件进行后续分析。")
        print("=" * 80)

    def process_csv_files(
        self,
        satlayer_file: str,
        chrome_file: str,
        output_prefix: str = "filtered_matched_data",
    ) -> bool:
        """
        主处理函数

        Args:
            satlayer_file: satlayer CSV文件路径
            chrome_file: chrome CSV文件路径
            output_prefix: 输出文件前缀

        Returns:
            处理是否成功
        """
        start_time = datetime.now()
        logging.info("开始CSV文件处理流程")

        try:
            # 1. 验证文件存在
            if not self.validate_file_exists(satlayer_file):
                return False
            if not self.validate_file_exists(chrome_file):
                return False

            # 2. 加载数据文件
            satlayer_df = self.load_csv_with_validation(
                satlayer_file, required_columns=["address", "status"]
            )
            if satlayer_df is None:
                return False

            chrome_df = self.load_csv_with_validation(
                chrome_file, required_columns=["evm_address"]
            )
            if chrome_df is None:
                return False

            self.stats["original_count"] = len(satlayer_df)

            # 3. 过滤数据
            filtered_satlayer = self.filter_status_data(satlayer_df)
            if filtered_satlayer.empty:
                logging.warning("过滤后没有数据，处理结束")
                return False

            self.stats["filtered_count"] = len(filtered_satlayer)

            # 4. 匹配数据
            matched_data = self.match_wallet_addresses(filtered_satlayer, chrome_df)
            if matched_data.empty:
                logging.warning("没有匹配到数据")
                return False

            self.stats["matched_count"] = len(matched_data)

            # 5. 保存结果
            output_file = self.save_results(matched_data, output_prefix)
            if not output_file:
                return False

            # 6. 生成报告
            end_time = datetime.now()
            self.stats["processing_time"] = (end_time - start_time).total_seconds()

            self.generate_detailed_report(
                self.stats["original_count"],
                self.stats["filtered_count"],
                self.stats["matched_count"],
                output_file,
                matched_data,
            )

            logging.info(f"处理完成，耗时: {self.stats['processing_time']:.2f} 秒")
            return True

        except Exception as e:
            logging.error(f"处理过程中出现错误: {str(e)}")
            import traceback

            logging.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CSV过滤和匹配工具")
    parser.add_argument(
        "--satlayer",
        default="examples/okx/satlayer2_chrome.csv",
        help="Satlayer CSV文件路径",
    )
    parser.add_argument(
        "--chrome", default="data/chrome.csv", help="Chrome CSV文件路径"
    )
    parser.add_argument(
        "--output", default="filtered_matched_data", help="输出文件前缀"
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="日志级别",
    )

    args = parser.parse_args()

    # 创建处理器并执行
    processor = CSVProcessor(log_level=args.log_level)
    success = processor.process_csv_files(args.satlayer, args.chrome, args.output)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
