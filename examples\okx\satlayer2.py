import json
import os
import random
import shutil
from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from os import environ
from pathlib import Path
from time import sleep

import click
from dotenv import load_dotenv
from loguru import logger

from config import DEFAULT_BROWSER_TYPE, USER_DATA_PATH
from src.browsers import BROWSER_TYPES, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.thread_executor import ThreadExecutor

load_dotenv()

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/satlayer2.log", rotation="10MB", level="SUCCESS")

DEFAULT_HEADERS = [
    "index",
    "type",
    "address",
    "task_x",
    "task_tg",
    "task_dc",
    "task_stake",
    "task_balance",
    "status",
    "message",
]


class SatLayerConfig(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def get_proxy(self):
        return self.browser_config.proxy

    def set_proxy(self, proxy):
        self.browser_config.proxy = proxy
        self._repository.update(self.browser_config)


class SatLayer2:
    def __init__(self, browser_type: BrowserType, id: str, data_util: DataUtil):
        self.id = id
        self.browser_type = browser_type

        if browser_type == BrowserType.CHROME:
            self.init_user_data_dir()
        self.browser_controller = BrowserController(browser_type, id)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    @staticmethod
    def _is_login(tab):
        return get_element(tab, "x://div[@class='wallet-address-container']", 5)

    def _connect_wallet(self, tab):
        if not tab.wait.ele_displayed("x://div[@class='wallet-pc-connect-button connect-wallet-button']", timeout=20):
            logger.error(f"【{self.id}】查找connect-wallet-button超时, 请检查网络是否异常")
            return False
        connect_wallet_btn = get_element(tab, "x://div[@class='wallet-pc-connect-button connect-wallet-button']", 5)
        connect_wallet_btn.click()

        if not tab.wait.ele_displayed("x://button[@class='wallet wallet-btn btn-md btn-fill-highlight']", timeout=20):
            logger.error(f"【{self.id}】查找wallet-btn超时, 请检查网络是否异常")
            return False
        if not try_click(
            tab, "x://button[@class='wallet wallet-btn btn-md btn-fill-highlight']", timeout=10, id=self.id
        ):
            logger.error(f"【{self.id}】多次点击链接钱包无反应")
            return False

        if not self.browser_controller.okx_wallet_connect():
            logger.error(f"【{self.id}】钱包链接失败")
            return False
        sleep(10)
        if self._is_login(tab):
            logger.success(f"【{self.id}】登录成功")
            return True

        logger.error(f"【{self.id}】登录失败")
        return False

    def _task_execute(self, task_name, task_index):
        try:
            if self.data_util.get(self.address).get(f"task_{task_name}", "0") == "1":
                logger.success(f"【{self.id}】{task_name}任务已完成")
                return True

            logger.info(f"【{self.id}】开始执行 {task_name} 任务")

            tab = self.browser_controller.page.latest_tab
            task_div_x = get_element(tab, f"x://div[contains(@class, 'index_giveaway-task')][{task_index}]", 5)
            if get_element(task_div_x, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                logger.success(f"【{self.id}】{task_name}任务已完成")
                self.data_util.update(self.address, {f"task_{task_name}": "1"})
                return True

            tab.actions.move_to(task_div_x)
            tg_task_btn = get_element(task_div_x, "x://div[contains(@class, 'index_common-task')]/button", 5)
            if tg_task_btn:
                tg_task_btn.click()
                if self.browser_controller.page.wait.new_tab(timeout=10):
                    sleep(1)
                    self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                    sleep(1)
                # tab.refresh()
                sleep(5)

            if get_element(task_div_x, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                logger.success(f"【{self.id}】{task_name}任务完成")
                self.data_util.update(self.address, {f"task_{task_name}": "1"})
                return True

            refresh_btn = get_element(
                task_div_x, "x://i[contains(@class, 'okx-defi-nft-filter-refresh dc-a11y-button')]", 5
            )
            if not refresh_btn:
                logger.error(f"【{self.id}】{task_name}未找到任务同步按钮")
                return False

            tab.listen.start("https://web3.okx.com/priapi/v1/dapp/giveaway/task/check")
            refresh_btn.click()
            res = tab.listen.wait(timeout=15)
            if not res or not res.response.body:
                logger.error(f"【{self.id}】{task_name}验证任务未收到响应")
                return False

            code = res.response.body.get("code", "0")
            if code == 1:
                logger.success(f"【{self.id}】{task_name}任务完成")
                self.data_util.update(self.address, {f"task_{task_name}": "1"})
                return True
            logger.warning(f"【{self.id}】{task_name}任务未完成")
            self.data_util.update(self.address, {f"task_{task_name}": "0"})
            return False
        except Exception as e:
            logger.error(f"【{self.id}】执行任务 【{task_name}】异常, error={str(e)}")
            self.data_util.update(self.address, {f"task_{task_name}": "0"})
            return False

    def _task_x(self):
        tab = None
        if self.data_util.get(self.address).get("x_status", "") != "success":
            logger.error(f"【{self.id}】推特登录异常或未配置, 跳过推特任务")
            self.data_util.update(self.address, {"task_x": "0"})
            return False
        for _ in range(3):
            try:
                if self.data_util.get(self.address).get("task_x", "0") == "1":
                    logger.success(f"【{self.id}】推特已完成")
                    return True

                if (
                    not self.browser_controller.browser_config.x_token
                    and not self.browser_controller.browser_config.x_user
                ):
                    logger.warning(f"【{self.id}】推特未配置")
                    return False

                tab = self.browser_controller.page.new_tab("https://web3.okx.com/zh-hans/giveaway/satlayer2")
                sleep(5)

                task_div_x = get_element(tab, "x://div[contains(@class, 'index_giveaway-task')][1]", 5)
                if get_element(task_div_x, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                    logger.success(f"【{self.id}】推特任务已完成")
                    self.data_util.update(self.address, {"task_x": "1"})
                    return True

                tab.actions.move_to(task_div_x)
                start_btn = get_element(
                    task_div_x,
                    "x://button[contains(@class, 'dc dc-btn btn-xs btn-fill-primary index_start__')]",
                    5,
                )
                # 推特已连接
                if not start_btn:
                    x_connect_btn = get_element(
                        task_div_x,
                        "x://button[contains(@class, 'index_twitter-connect')]",
                        5,
                    )
                    if not x_connect_btn:
                        # return False
                        continue

                    x_connect_btn.click()
                    if not self.browser_controller.page.wait.new_tab(timeout=20, curr_tab=tab):
                        logger.error(f"【{self.id}】未打开推特授权页面")
                        continue
                        # return False
                    if "x.com/account/access" in self.browser_controller.page.latest_tab.url:
                        logger.error(f"【{self.id}】推特被锁定")
                        self.data_util.update(self.address, {"message": "x_is_locked"})
                        return False
                    auth_btn = self.browser_controller.page.latest_tab.ele(
                        "x://button[@data-testid='OAuth_Consent_Button']", timeout=10
                    )
                    if not auth_btn:
                        logger.error(f"【{self.id}】未找到授权按钮, 请检查推特是否登录")
                        if (
                            "x.com" in self.browser_controller.page.latest_tab.url
                            or "twitter.com" in self.browser_controller.page.latest_tab.url
                        ):
                            self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                        # return False
                        continue
                    auth_btn.click()
                    sleep(5)

                tab.actions.move_to(task_div_x)
                start_btn = get_element(
                    task_div_x, "x://button[contains(@class, 'dc dc-btn btn-xs btn-fill-primary index_start__')]", 5
                )
                start_btn.click()
                sleep(1)
                self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])

                if get_element(task_div_x, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                    logger.success(f"【{self.id}】推特任务完成")
                    self.data_util.update(self.address, {"task_x": "1"})
                    return True
                self.data_util.update(self.address, {"task_x": "0"})
                # return False
            except Exception as e:
                self.data_util.update(self.address, {"task_x": "0"})
                logger.error(f"【{self.id}】推特任务异常， error={str(e)}")
                # return False
            finally:
                if tab:
                    tab.close()

    def _task_tg(self):
        return self._task_execute("tg", 2)

    def _task_dc(self):
        return self._task_execute("dc", 3)

    def _verify_stake(self):
        return self._task_execute("stake", 4)

    def _verify_balance(self):
        return self._task_execute("balance", 5)

    def _verify_task(self):
        logger.info(f"【{self.id}】开始验证任务")
        tab = self.browser_controller.page.latest_tab
        verify_btn = get_element(
            tab, "x://button[contains(@class,'dc dc-btn btn-xl btn-fill-primary index_bottom-btn')]", 10
        )
        if not verify_btn:
            btn = get_element(
                tab,
                "x://button[contains(@class, 'dc dc-btn btn-xl btn-outline-primary btn-disabled index_bottom-btn')]",
                10
            )
            if btn:
                logger.success(f"【{self.id}】任务完成")
                self.data_util.update(
                    self.address,
                    {
                        "task_x": "1",
                        "task_tg": "1",
                        "task_dc": "1",
                        "task_stake": "1",
                        "task_balance": "1",
                        "status": "1",
                        "fail_count": "0",
                        "message": "",
                    },
                )
                return True
            logger.error(f"【{self.id}】任务验证失败,未找到验证按钮")
            return False

        tab.listen.start("https://web3.okx.com/priapi/v1/dapp/giveaway/claimFinished")
        verify_btn.click()
        res = tab.listen.wait(timeout=15)
        if not res or not res.response.body:
            logger.error(f"【{self.id}】任务验证失败,未收到网络响应")
            return False

        devid = res.request.headers.get("Devid", "")
        device_id = self._get_device_id(res.request.cookies)
        logger.info(f"【{self.id}】devid={devid}, deviceId={device_id}")
        data = res.response.body.get("data", {}).get("verifySucceed", False)
        if data:
            logger.success(f"【{self.id}】任务完成")
            self.data_util.update(
                self.address,
                {
                    "task_x": "1",
                    "task_tg": "1",
                    "task_dc": "1",
                    "task_stake": "1",
                    "task_balance": "1",
                    "status": "1",
                    "fail_count": "0",
                    "devid": devid,
                    "deviceId": device_id,
                    "message": "",
                },
            )
            return True
        logger.warning(f"【{self.id}】任务未完成")
        return False

    def _parse_cookies(self, cookie_string):
        cookie = SimpleCookie()
        try:
            cookie.load(cookie_string)
            return {key: cookie[key].value for key in cookie}
        except Exception as e:
            logger.warning(f"【{self.id}】解析cookie失败, error={str(e)}")
            return {}

    @staticmethod
    def _get_device_id(cookies: list[dict]):
        value = next((cookie["value"] for cookie in cookies if cookie["name"] == "_monitor_extras"), None)
        if value is None:
            return ""
        json_value = json.loads(value)
        return json_value.get("deviceId", "")

    def install_okx_wallet(self):
        try:
            # todo待优化
            self.browser_controller.page.new_tab(
                "https://chromewebstore.google.com/detail/okx-wallet/mcohilncbfahbmgdjkbpemcciiolgcge"
            )

            tab = self.browser_controller.page.latest_tab
            add_btn = get_element(
                tab, "x://*[@id='yDmH0d']/c-wiz/div/div/main/div/section[1]/section/div/div[1]/div[2]/div/button", 5
            )
            if not add_btn:
                logger.error(f"【{self.id}】安装OKX Wallet失败")
                return False

            add_btn.click()
            if not tab.handle_alert():
                logger.error(f"【{self.id}】安装OKX Wallet失败")
                return False

            sleep(5)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】OKX Wallet插件安装异常, error={str(e)}")
            return False

    def init_user_data_dir(self):
        """
        复制Chrome用户目录

        Raises
        ------
               FileNotFoundError: 如果主用户目录不存在
        """
        master_profile = environ.get("MASTER_USER_DATA_PATH")
        if not master_profile:
            raise FileNotFoundError("MASTER_USER_DATA_PATH未配置")

        master_path = Path(master_profile).resolve()  # 指向 Profile 2
        if not master_path.exists() or not master_path.is_dir():
            raise FileNotFoundError(f"主用户目录 {master_path} 不存在或不是目录")

        target_path = Path(USER_DATA_PATH) / str(self.id)
        target_path.mkdir(parents=True, exist_ok=True)
        if target_path.exists():
            shutil.rmtree(target_path)

        shutil.copytree(master_path, target_path, dirs_exist_ok=False)
        sleep(5)

    def clear_user_data(self) -> bool:
        """删除数据."""
        try:
            user_data_path = rf"{USER_DATA_PATH}/{self.id}"
            if os.path.exists(user_data_path):
                shutil.rmtree(user_data_path)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】清除用户数据目录失败, error={str(e)}")
            return False

    def setup(self):
        try:
            # 安装okx插件
            # if not self.install_okx_wallet():
            #     logger.error(f"【{self.id}】OKX Wallet插件安装失败")
            #     return False

            # 导入钱包
            if (
                not self.browser_controller.browser_config.mnemonic
                and not self.browser_controller.browser_config.evm_private_key
            ):
                logger.error(f"【{self.id}】未配置助记词或私钥")
                return False

            is_key = not self.browser_controller.browser_config.mnemonic
            if not self.browser_controller.okx_wallet_setup(is_key=is_key, wallet_name=self.id):
                fail_count_str = self.data_util.get(self.address).get("fail_count")
                fail_count = int("0" if fail_count_str in ["", None] else fail_count_str)
                fail_count = fail_count + 1
                self.data_util.update(self.address, {"fail_count": fail_count})
                logger.error(f"【{self.id}】钱包导入失败")
                self.reset_proxy()
                return False

            # 导入推特
            if self.browser_controller.browser_config.x_token or self.browser_controller.browser_config.x_user:
                if self.data_util.get(self.address).get("task_x", "0") == "1":
                    logger.success(f"【{self.id}】推特任务已完成")
                    return True

                if not self.browser_controller.login_x(False):
                    logger.error(f"【{self.id}】推特登录失败")
                    self.data_util.update(
                        self.address, {"x_name": self.browser_controller.browser_config.x_user, "x_status": "error"}
                    )
                else:
                    self.data_util.update(
                        self.address, {"x_name": self.browser_controller.browser_config.x_user, "x_status": "success"}
                    )
            else:
                logger.info(f"【{self.id}】推特未配置")

            return True
        except Exception as e:
            logger.error(f"【{self.id}】初始化浏览器异常, error={str(e)}")
            return False

    def login(self):
        for i in range(3):
            logger.info(f"【{self.id}】第 {i + 1} 次登录")
            try:
                self.browser_controller.window_max()
                self.browser_controller.okx_wallet_login()
                tab = self.browser_controller.page.new_tab("https://web3.okx.com/zh-hans/giveaway/satlayer2")
                if self._is_login(tab):
                    logger.success(f"【{self.id}】已登录")
                    return True

                result = self._connect_wallet(tab)
                if result:
                    return True

                fail_count_str = self.data_util.get(self.address).get("fail_count")
                fail_count = int("0" if fail_count_str in ["", None] else fail_count_str)
                fail_count = fail_count + 1
                self.data_util.update(self.address, {"fail_count": fail_count})
            except Exception as e:
                logger.error(f"【{self.id}】登录异常, error={str(e)}")

        logger.warning(f"【{self.id}】登录重试三次仍未成功, 切换代理")
        self.reset_proxy()
        return False

    def reset_proxy(self):
        try:
            if self.browser_type != BrowserType.CHROME:
                logger.info(f"【{self.id}】非Chrome浏览器,暂不支持切换代理")
                return
            current_proxy = self.browser_controller.browser_config.proxy
            fail_count = self.data_util.get(self.address).get("fail_count", 0)

            fail_proxy = self.data_util.get(self.address).get("fail_proxy", "")
            if fail_count >= 3 and fail_proxy != current_proxy:
                fail_proxy = current_proxy
            self.data_util.update(self.address, {"fail_count": fail_count, "fail_proxy": fail_proxy})

            indices = [data["index"] for data in self.data_util.list() if data["status"] == "1"]
            if len(indices) == 0:
                logger.warning(f"【{self.id}】切换代理失败,未找到可用代理")
                return

            index = random.choice(indices)
            config = SatLayerConfig(self.browser_type, index)
            new_proxy = config.get_proxy()
            # if fail_count >= 5:
            #     new_proxy = ""
            config.set_proxy(new_proxy)
            logger.warning(f"【{self.id}】登录失败 {fail_count} 次, 代理切换为 {new_proxy}")
        except Exception as e:
            logger.error(f"【{self.id}】切换代理失败, error={str(e)}")

    def task(self):
        try:
            if self.data_util.get(self.address).get("status", "0") == "1":
                logger.success(f"【{self.id}】任务已全部完成")
                return True

            if not self.login():
                logger.error(f"【{self.id}】登录失败")
                self.data_util.update(self.address, {"message": "login_error"})
                # self.reset_proxy()
                return False
            else:
                self.data_util.update(self.address, {"message": "", "fail_count": 0})

            self._task_x()
            for i in range(3):
                logger.info(f"【{self.id}】第 {i + 1} 次执行任务...")
                tab = self.browser_controller.page.new_tab("https://web3.okx.com/zh-hans/giveaway/satlayer2")
                self._task_tg()
                self._task_dc()
                result = self._verify_stake()
                if not result:
                    logger.error(f"【{self.id}】质押任务失败")
                    return False

                result = self._verify_balance()
                if not result:
                    logger.error(f"【{self.id}】余额任务失败")
                    return False

                self._verify_task()
                status = self.data_util.get(self.address).get("status", "0")
                if status == "1":
                    logger.success(f"【{self.id}】任务已全部完成")
                    return True
                tab.close()
            logger.warning(f"【{self.id}】执行三次后仍有未完成任务")
        except Exception as e:
            logger.error(f"【{self.id}】执行任务异常, error={str(e)}")
            return False


def _execute_task(browser_type, index, data_util):
    layer = None
    try:
        layer = SatLayer2(browser_type, str(index), data_util)
        if layer.data_util.get(layer.address).get("status", "0") == "1":
            return True

        if not layer.setup():
            logger.error(f"【{layer.id}】导入钱包失败")
            return False

        return layer.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False
    finally:
        try:
            if layer.browser_type != BrowserType.CHROME:
                # 重置钱包
                layer.browser_controller.okx_wallet_reset()

                # 清除x.com和twitter.com的缓存
                layer.browser_controller.clear_site_data("x.com")
                layer.browser_controller.clear_site_data("twitter.com")
                layer.browser_controller.clear_site_data("web3.okx.com")
        except Exception as e:
            logger.warning(f"【{index}】清除缓存数据异常， error={str(e)}")

        try:
            layer.browser_controller.close_page()
            sleep(5)
        except Exception as e:
            logger.warning(f"【{index}】关闭浏览器异常， error={str(e)}")

        try:
            if layer and layer.browser_type == BrowserType.CHROME:
                layer.clear_user_data()
        except Exception as e:
            logger.warning(f"【{index}】清除浏览器缓存数据异常， error={str(e)}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-c", "--count", type=int, default=3, help="重试次数")
def run(type, index, workers, count):
    try:
        indices = parse_indices(index)
        # random.shuffle(indices)

        data_dir = os.path.join(get_project_root_path(), "examples", "okx")
        csv_path = os.path.join(data_dir, f"satlayer2_{type.lower()}.csv")
        data_util = DataUtil(csv_path, DEFAULT_HEADERS)

        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        for i in range(count):
            separator = "=" * 20
            logger.info(f"{separator} 开始第 {i+1} 次执行任务 {separator}")
            # 已经执行成功的任务
            executed_indices = [int(data["index"]) for data in data_util.list() if data["status"] == "1"]

            # 从全部任务中去除已经执行成功的的任务
            execute_indices = [x for x in indices if x not in executed_indices]

            if len(execute_indices) == 0:
                logger.success("所有任务全部执行完成")
                return True

            def process_task(index):
                result = _execute_task(type, index, data_util)
                if result:
                    successful_indices.append(index)
                else:
                    failed_indices.append(index)
                return result

            # 创建线程执行器
            executor = ThreadExecutor(
                workers=workers,
                timeout=6 * 3600,  # 6小时超时
                retries=3,
                interval=10,
                task_name=f"satlayer2_{type}",
                raise_exception=False,
            )

            # 批量执行任务
            executor.run_batch(process_task, execute_indices)
            # 计算失败列表
            failed_indices = list(set(execute_indices) - set(successful_indices))  # 计算失败的索引
            # 如果需要保持列表格式
            failed_indices = [str(index) for index in failed_indices]
            logger.success(
                f"本次共执行 {len(execute_indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个"
            )
            if len(failed_indices) > 0:
                logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# python3 examples/okx/satlayer2.py run -t chrome -i 1-100
if __name__ == "__main__":
    # data_dir = os.path.join(get_project_root_path(), "examples", "okx")
    # csv_path = os.path.join(data_dir, "satlayer2_chrome.csv")
    # data_util = DataUtil(csv_path, DEFAULT_HEADERS)
    # _execute_task(BrowserType.CHROME, "5800", data_util)
    cli()
