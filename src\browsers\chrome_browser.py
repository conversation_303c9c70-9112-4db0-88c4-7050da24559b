import os
from typing import Dict, List, Optional  # noqa: UP035

from DrissionPage import Chromium
from loguru import logger

from src.controllers.exceptions import BrowserControllerError
from src.fingerprints.chrome import get_page_with_browser_id
from src.utils import get_project_root_path

from .base_browser import BaseBrowser, BrowserType


# 普通 Chrome 浏览器
class ChromeBrowser(BaseBrowser):

    def __init__(self, id: str, browser_id: str, browser_config: dict):
        super().__init__(id, browser_id, browser_config)

    def set_extensions(self, extensions: List[Dict[str, str]]):
        """设置浏览器插件(会覆盖默认插件)

        Args:
            extensions: 插件配置列表，每个插件需包含以下格式的字典:
                [
                    {
                        "path": "/path/to/extension1",  # 插件文件路径（必需）
                        "id": "extension_id"          # 插件（可选）
                    },
                    ...
                ]

        Example:
            browser.set_extensions([
                {
                    "path": "/extensions/metamask.crx",
                    "id": "metamask"
                },
                {
                    "path": "/extensions/ublock.crx",
                    "id": "ublock"
                }
            ])

        Raises:
            BrowserControllerError: 当浏览器配置为空时抛出
        """
        if not self.browser_config:
            raise BrowserControllerError(f"ID {self.id} 浏览器配置为空")

        self.extensions = extensions[:]

    def add_extension(self, extension: dict) -> None:
        """添加浏览器扩展

        Args:
            extension: 扩展配置字典
        """
        try:
            if not isinstance(extension, dict):
                raise ValueError("扩展配置必须是字典类型")

            self.extensions.append(extension)
        except Exception as e:
            logger.error(f"{self.id} 添加插件失败: {e}")
            raise

    def get_chromium_page(self) -> Optional[Chromium]:
        """获取浏览器页面实例"""
        try:
            if not self.browser_config:
                logger.warning(f"id [{self.id}] 获取对应的钱包信息为空, 请检查配置项")
                return

            self.page = get_page_with_browser_id(
                self.id, self.browser_config, self.extensions
            )
            return self.page
        except Exception as e:
            logger.error(f"{self.id} 获取浏览器页面实例失败: {str(e)}")
            return None

    def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.quit()
        except Exception as e:
            logger.error(f"{self.id} 关闭浏览器失败: {str(e)}")

    def wallet_config_path(self) -> str:
        """钱包配置路径"""
        return os.path.join(get_project_root_path(), "data/chrome.csv")

    @property
    def browser_type(self) -> BrowserType:
        return BrowserType.CHROME
