from loguru import logger
import time
from src.evm.web3_manager import Web3Manager
from web3 import Web3


class BaseContract:
    """基础合约类"""

    def __init__(
        self,
        id: str,
        rpc_url: str,
        private_key: str,
        proxy: str = None,
        user_agent: str = None,
    ):
        self.id = id
        self.rpc_url = rpc_url
        self.private_key = private_key
        self.proxy = proxy
        self.user_agent = user_agent
        self.web3_manager = Web3Manager(private_key=private_key)
        self._web3 = None
        self._account_address = None

    @property
    def web3(self) -> Web3:
        """获取或创建web3连接"""
        if not self._web3:
            self._web3 = self.web3_manager.connect_to_network(
                rpc_url=self.rpc_url, proxy=self.proxy, user_agent=self.user_agent
            )
            # 检查网络
            chain_id = self._web3.eth.chain_id
            logger.info(f"连接到网络: Chain ID = {chain_id}")
        return self._web3

    @property
    def account_address(self) -> str:
        if not self._account_address:
            self._account_address = self.web3.eth.account.from_key(
                self.private_key
            ).address
        return self._account_address

    def _execute_with_retry(self, func, max_retries=3, *args, **kwargs) -> bool:
        """通用重试机制"""
        last_error = None
        for attempt in range(max_retries):
            try:
                result = func(*args, **kwargs)
                if result:
                    return True

                logger.warning(f"第 {attempt + 1} 次尝试失败，等待重试...")
                time.sleep(2**attempt)  # 指数退避

            except Exception as e:
                last_error = e
                if "already known" in str(e):
                    logger.warning(f"{self.id} 交易已在交易池中，尝试使用新的 nonce...")
                    continue
                logger.error(f"{self.id} 交易执行异常: {str(e)}")
                if attempt == max_retries - 1:
                    break
                time.sleep(2**attempt)

        logger.error(f"{self.id} 重试{max_retries}次后失败，最后错误: {last_error}")
        return False

    def _get_token_balance(self, token_contract: str) -> float:
        """获取代币余额"""
        try:
            balance_info = self.web3_manager.check_token_balance(
                token_contract, self.account_address
            )
            return balance_info.get("balance", 0)
        except Exception as e:
            logger.error(f"{self.id} 获取代币余额失败: {str(e)}")
            return 0

    def _get_nft_balance(self, nft_contract: str) -> bool:
        """获取NFT余额"""
        try:
            balance = self.web3_manager.get_nft_balance(
                nft_contract, self.account_address
            )
            return balance > 0
        except Exception as e:
            logger.error(f"{self.id} 获取NFT余额失败: {str(e)}")
            return False

    def _get_token_balance_wei(self, token_contract: str) -> int:
        """获取代币余额(wei单位)"""
        try:
            balance_info = self.web3_manager.check_token_balance(
                token_contract, self.account_address
            )
            return balance_info.get("raw_balance", 0)  # 直接返回raw_balance
        except Exception as e:
            logger.error(f"{self.id} 获取代币余额失败: {str(e)}")
            return 0

    def get_balance(self) -> float:
        """获取账户ETH余额（以ETH为单位）"""
        try:
            balance_wei = self.web3.eth.get_balance(self.account_address)
            return round(float(self.web3.from_wei(balance_wei, "ether")), 2)
        except Exception as e:
            logger.error(f"{self.id} 获取账户余额失败: {str(e)}")
            return 0.0

    def check_balance(self, required_amount: float = 0) -> bool:
        """检查账户余额"""
        try:
            balance = self.web3.eth.get_balance(self.account_address)
            if required_amount:
                required_wei = self.web3.to_wei(required_amount, "ether")
                if balance < required_wei:
                    logger.error(
                        f"{self.id} 余额不足: 需要 {required_amount} ETH, 当前余额: {self.web3.from_wei(balance, 'ether')} ETH"
                    )
                    return False
            return True
        except Exception as e:
            logger.error(f"{self.id} 检查余额失败: {str(e)}")
            return False

    def _execute_contract_call(
        self,
        contract_address: str,
        input_data: str,
        value_in_wei: int = 0,
        transaction_nonce: int = None,
        gas_limit_multiplier: float = 1.21,
    ) -> bool:
        """执行合约调用"""

        try:
            from_address = self.web3.eth.account.from_key(self.private_key).address
            checksum_address = self.web3.to_checksum_address(contract_address)

            # 如果没有提供 nonce，获取当前 nonce
            if not transaction_nonce:
                transaction_nonce = self.web3.eth.get_transaction_count(
                    from_address, "pending"
                )

            # 使用 checksum 地址进行 gas 估算
            try:
                gas_estimate = self.web3.eth.estimate_gas(
                    {
                        "from": from_address,
                        "to": checksum_address,
                        "value": value_in_wei,
                        "data": input_data,
                        "nonce": transaction_nonce,
                    }
                )
            except Exception as e:
                logger.error(
                    f"{self.id} Gas估算失败: {str(e)}\n"
                    f"交易详情:\n"
                    f"- From: {from_address}\n"
                    f"- To: {checksum_address}\n"
                    f"- Value: {value_in_wei} wei\n"
                    f"- Data: {input_data[:100]}{'...' if len(input_data) > 100 else ''}\n"
                    f"- Nonce: {transaction_nonce}"
                )
                raise

            result = self.web3_manager.send_transaction(
                to_address=checksum_address,
                value_in_wei=value_in_wei,
                data=input_data,
                transaction_nonce=transaction_nonce,
                gas_limit=int(gas_estimate * gas_limit_multiplier),  # 添加预估的gas值
            )

            if not result:
                logger.error(f"{self.id} 交易发送失败")
                return False

            if result.get("status") == "failed":
                logger.error(
                    f"{self.id} 交易失败原因: {result.get('error_reason', '未知错误')}"
                )
                return False

            return result.get("status") == "success"

        except Exception as e:
            logger.error(f"{self.id} 合约调用失败: {str(e)}")
            return False
