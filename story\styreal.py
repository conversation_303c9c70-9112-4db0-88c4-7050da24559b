from loguru import logger
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from retry import retry
from typing import Optional
from config import MAX_GAS_PRICE


class StoryStyreal(StoryBase):
    """Styreal任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "styreal"
        self.home_url = "https://tuning.app.styreal.com/task/badge"
        self.wallet_address = self.browser.browser_config.evm_address
        self._nft_contract = None
        self.badge_contract_address = "******************************************"

    def _connect_wallet(self, page) -> bool:
        """连接钱包"""
        return True

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Styreal任务"""
        if self._check_task_completed():
            return False

        # 检查NFT是否已铸造
        if self._check_badge_minted():
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return False

        try:
            # # gas低了再启用
            # gas_prices = self.get_gas_price()
            # if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            #     logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            #     return False
            nft_contract = self.get_nft_contract()
            if nft_contract.get_balance() < 2:
                logger.warning(f"{self.browser_id} NFT余额不足，跳过铸造")
                return False

            if not self._login_wallet():
                return False

            self.page.get(self.home_url)

            # logger.success(f"{self.browser_id} Styreal任务完成")
            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Styreal任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Styreal任务失败")
