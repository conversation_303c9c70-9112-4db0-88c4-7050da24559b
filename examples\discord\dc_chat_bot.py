import os

from loguru import logger
from src.browsers import BrowserType
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance

PROXY_URL = os.getenv("PROXY_URL")


class DCChatBot(BrowserConfigInstance):
    def __init__(self, browser_type: BrowserType, browser_id: str):
        super().__init__(browser_type, browser_id)

    def send_message(self, channel_id: str, message: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not dc_token:
            logger.error(f"{self.browser_id} 没有设置dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(channel_id, message):
            raise Exception("发送消息失败")
        return True


if __name__ == "__main__":
    bot = DCChatBot(BrowserType.CHROME, "1")
    evm_address = bot.browser_config.evm_address
    bot.send_message("1342142136778489876", f"!faucet {evm_address}")
