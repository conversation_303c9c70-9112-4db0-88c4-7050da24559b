import os
import random
import sys
from time import sleep
from src.utils.thread_executor import ThreadExecutor
import click
from loguru import logger

# 使用完整的导入路径
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.element_util import get_element

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")


class FaucetWeb:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def _check_cf_shield(self, lasted_tab):
        for _ in range(6):
            # 1. 判断是否在CF盾页面
            div_ele = lasted_tab.ele("x://*[@id='getting-started']/div/div[3]/div/div/div/div/div[2]/div[2]/div")
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            if iframe:
                try:
                    success = iframe.ele("tag:body").sr('@id=success')
                    if success and success.states.is_displayed:
                        logger.success(f"【{self.id}】 过CF盾成功")
                        return True

                    logger.info(f"【{self.id}】 在CF盾页面")
                    checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                    if not checkbox:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                    checkbox.wait.has_rect(timeout=20)
                    checkbox.click()
                    sleep(3)

                    success = iframe.ele("tag:body").sr('@id=success')
                    if success and success.states.is_displayed:
                        logger.success(f"【{self.id}】 过CF盾成功")
                        return True
                    else:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                except Exception as e:
                    logger.error(f"【{self.id}】 过CF盾失败: {e}")
                    sleep(2)
                    continue


        return False

    def _is_claim(self, tab):
        claimed_selector = "x://div[contains(text(), 'Drip successful.')]"
        # 循环5次，总共10秒去判断返回值
        for attempt in range(20):
            if get_element(tab, claimed_selector, 1):
                return True
            sleep(1)
        return False

    def faucet(self):
        try:
            address = self.browser_controller.browser_config.evm_address

            url = "https://testnet.monad.xyz/"
            tab = self.page.new_tab(url)
            sleep(8)
            for index in range(10):
                logger.info(f"【{self.id}】第 {index+1}/10 次尝试领水")
                checkbox = get_element(tab, "x://*[@id='terms-accepted']", 5)
                if checkbox and not checkbox.states.is_checked:
                    checkbox.click()
                    sleep(2)

                continue_btn = get_element(tab, "x://*[@id='radix-:r0:']/div/div/div[2]/div[2]/button", 5)
                if continue_btn:
                    continue_btn.click()
                    sleep(2)

                tab.scroll.to_see("x://*[@id='getting-started']/div/div[3]/div/div/div/div/div[2]/div[1]/div/input")
                input_i = get_element(tab, "x://*[@id='getting-started']/div/div[3]/div/div/div/div/div[2]/div[1]/div/input", 3)
                if input_i:
                    input_i.clear(True)
                    input_i.input(address)
                    sleep(1)

                if not self._check_cf_shield(tab):
                    logger.error(f"【{self.id}】 过CF盾失败")
                    return False

                claim_btn = get_element(tab, "x://*[@id='getting-started']/div/div[3]/div/div/div/div/div[2]/button", 3)
                if claim_btn:
                    claim_btn.click()

                if self._is_claim(tab):
                    logger.success(f"【{self.id}】 领取成功")
                    return True

                logger.info(f"【{self.id}】第 {index + 1}/10 次领水失败, retry...")
                tab.refresh()
                sleep(3)
        except Exception as e:
            logger.error(f"【{id}】 领水发生异常，error={str(e)}")
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(e)
        return False


def _run_task(type, index):
    try:
        browser = FaucetWeb(type, str(index))
        browser.faucet()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                browser = FaucetWeb(type, str(index))
                return browser.faucet()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_web-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/faucet_web.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    #_run_task(BrowserType.BIT, 2)
