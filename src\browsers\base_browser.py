from abc import ABC, abstractmethod
from loguru import logger
from DrissionPage import Chromium
from typing import Optional


from src.enums.browsers_enums import BrowserType


class BaseBrowser(ABC):
    def __init__(self, id: str, browser_id: str, browser_config: dict):
        self.id = id
        self.browser_id = browser_id
        self.browser_config = browser_config
        self.extensions = []
        self._page: Optional[Chromium] = None  # 改为私有变量

    @abstractmethod
    def get_chromium_page(self) -> Optional[Chromium]:
        """获取浏览器页面实例"""
        pass

    @abstractmethod
    def wallet_config_path(self) -> str:
        """钱包配置路径"""
        pass

    @abstractmethod
    def close(self):
        """关闭浏览器"""
        pass

    @property
    @abstractmethod
    def browser_type(self) -> BrowserType:
        """浏览器类型"""
        pass

    @property
    def page(self) -> Optional[Chromium]:
        """获取浏览器页面实例（懒加载）"""
        try:
            if not self._page:
                self._page = self.get_chromium_page()
            return self._page
        except Exception as e:
            logger.error(f"{self.id} 获取页面实例失败: {e}")
            return None

    @page.setter
    def page(self, value):
        self._page = value
