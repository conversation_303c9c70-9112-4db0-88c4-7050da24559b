import csv
import datetime
import json
import os
import random
import re
import time
from threading import Lock
from time import sleep
from typing import Optional

import click
from faker import Faker
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.socials import X
from src.utils.common import generate_username, get_project_root_path, parse_indices
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.hhcsv import HHCSV
from src.utils.password import generate_pwd
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/metamask_faucet.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")


class GitHub:
    # 创建类级别的锁
    _csv_lock = Lock()

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            data_dir = os.path.join(get_project_root_path(), "examples", "github")
            self.csv_path = os.path.join(data_dir, "github.csv")
            with self._csv_lock:
                self.csv = HHCSV(self.csv_path, ["index", "type", "address", "email", "password", "status"])
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def _get_email_verify_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Your GitHub launch code",
                to=proxy_email or email,
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            # 匹配"go to "后面的sendgrid链接，直到遇到换行符
            pattern = r"Continue signing up for GitHub by entering the code below:\s*(\d+)\s*"

            # 查找匹配
            match = re.search(pattern, email["content"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证码失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=1):
        """统一处理输入框操作"""

        def _handle_input():
            input_element = get_element(tab, xpath, timeout=3)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True

        try:
            return _handle_input()
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=3, sleep_time=3):
        """统一处理点击操作"""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def _get_registration(self, address):
        try:
            with self._csv_lock:
                result = self.csv.query({"address": address})
                return result[0] if result else {}
        except Exception as e:
            logger.error(f"获取注册信息时发生错误: {e}")
            return {}

    def _save_registration(self, register):
        """保存注册信息到CSV"""
        try:
            address = register.get("address")
            with self._csv_lock:
                # 先查询是否存在记录
                result = self.csv.query({"address": address})
                if not result:
                    self.csv.add_row(register)
                else:
                    criteria = {"address": address}
                    self.csv.update_row(criteria, register)
        except Exception as e:
            logger.error(f"保存注册信息时发生错误: {e}")

    def login(self):
        """登录 github 开发者账号."""
        address = self.browser_controller.browser_config.evm_address
        register = self._get_registration(address)
        tab = self.page.new_tab("https://github.com/login")
        self.page.close_tabs(tab, others=True)
        self._input_field(tab, "x://input[@id='login_field']", register.get("email"))
        self._input_field(tab, "x://input[@id='password']", register.get("password"))
        self._click_element(tab, "x://input[@name='commit']")
        logger.info(f"【{self.id}】登录完成")
        return True

    @retry(tries=2, delay=3)
    def register(self, type: str):
        """注册 github 开发者账号."""
        # self.login()
        try:
            # 读取address并检查是否已注册
            address = self.browser_controller.browser_config.evm_address
            password = generate_pwd(10)
            register = self._get_registration(address)
            if register:
                if register.get("status") == "1":
                    logger.info(f"【{self.id}】钱包 {address} 已注册,跳过注册流程")
                    return True
            else:
                register["password"] = password

            email_name = self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email
            email = proxy_email or email_name

            tab = self.page.new_tab("https://github.com/")
            try_click(tab, "x://a[normalize-space()='Sign up']")

            # 生成随机用户信息
            username = generate_username()

            # 填写表单
            form_fields = {
                "x://input[@id='email']": email,
                "x://input[@id='password']": password,
                "x://input[@id='login']": username,
            }
            logger.info(f"【{self.id}】开始填写注册信息")
            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")
            try_click(tab, "x://input[contains(@id,'user_signup')]", timeout=5)

            register["index"] = self.id
            register["type"] = type.value
            register["address"] = address
            register["email"] = email
            register["status"] = 0
            logger.debug(f"【{self.id}】注册信息: {register}")

            # 提交表单
            try_click(tab, "x://button[.//span[contains(text(), 'Continue')]]", timeout=5)

            logger.info("点击提交按钮")

            sleep(5)
            try_click(tab, "x://button[.//span[contains(text(), 'Continue')]]", timeout=5)
            # sub_ele.click.multi(2)

            if get_element(tab, "x://h2[contains(text(),'Verify your account')]", timeout=5):
                logger.info(f"{self.id} 开始认证")
                iframe = get_frame(
                    tab,
                    "x://iframe[contains(@src, 'octocaptcha.com')]",
                    timeout=10,
                )
                sleep(5)
                btn_ele = iframe.ele("x://button[normalize-space()='Visual puzzle']")
                if btn_ele:
                    btn_ele.click()
                self._save_registration(register)
                sleep(4)
                if tab.wait.url_change("account_verifications", timeout=300):
                    verify_code = self._get_email_verify_code(email_name, email_pwd, proxy_email)

                    # 将验证码的每一位分别输入到对应的输入框
                    for i in range(8):
                        input_text(tab, f"x://input[@id='launch-code-{i}']", verify_code[i], timeout=10)
                    try_click(tab, "x://span[contains(text(),'Continue')]", timeout=5)
                elif tab.wait.url_change("https://github.com/signup", timeout=30):
                    logger.error(f"【{self.id}】 过验证码失败,请稍后再试")
                    raise Exception(f"【{self.id}】 过CF盾失败")
                if tab.wait.url_change("https://github.com/login", timeout=300):
                    if get_element(tab, "x://div[contains(text(),'Your account was created successfully')]", 10):
                        register["status"] = 1
                        self._save_registration(register)
                        logger.success(f"【{self.id}】 账号注册成功")
                        self._input_field(tab, "x://input[@id='login_field']", register.get("email"))
                        self._input_field(tab, "x://input[@id='password']", register.get("password"))
                        self._click_element(tab, "x://input[@name='commit']")
                        logger.info(f"【{self.id}】登录完成")
                        return True
                else:
                    logger.error(f"【{self.id}】 过验证码失败盾")
                    raise Exception(f"【{self.id}】 过CF盾失败")

        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            return False

    def _faucet(self, tab):
        try:
            max_retries = 3
            eles = get_elements(tab, "x://ul[@role='tablist']//li", 3)
            for index in range(len(eles)):
                retry = 1
                while retry <= max_retries:
                    eles[index].click()
                    sleep(3)
                    chain = eles[index].text
                    logger.info(f"【{self.id}】开始领取 {chain}")
                    try:
                        tab.listen.start("developer.metamask.io/api/faucets/")
                        address = self.browser_controller.browser_config.evm_address
                        input_i = get_element(tab, f"x:(//input[@type='text'])[{index + 1}]", 3)
                        input_i.clear(True)
                        input_i.input(address)
                        sleep(3)

                        get_element(tab, f"x:(//button[@data-test-id='hero-cta-request-eth'])[{index + 1}]", 5).click()
                        res = tab.listen.wait(timeout=10)
                        if res:
                            response_data = res.response.body
                            if isinstance(response_data, dict):
                                if "error" in response_data:
                                    error_msg = response_data["error"].get("message", "未知错误")
                                    logger.warning(f"【{self.id}】领取 {chain} 失败: {error_msg}")
                                elif "txnHash" in response_data:
                                    logger.success(
                                        f"【{self.id}】领取 {chain} 成功，获得 {response_data.get('value', '0')}ETH"
                                    )
                            break
                        else:
                            logger.error(f"【{self.id}】未收到响应")
                    except Exception as e:
                        logger.error(f"【{self.id}】 领取 {eles[index].text} 异常: {str(e)}")
                    retry += 1
                sleep(3)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】领水异常: {str(e)}")
            return False

    def task(self, type: str):
        try:
            self.browser_controller.window_max()
            if not self.register(type):
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False
            logger.info(f"【{self.id}】已有账号，开始养成")
            result = True
            # result = self.login()
            if result:
                try:
                    self.page.quit()
                    return True
                except Exception as e:
                    logger.error(f"【{self.id}】关闭页面失败: {e}")
            else:
                logger.error(f"【{self.id}】任务失败")
                self.page.quit()
                return False
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
            self.page.quit()


def _run_task(type, index):
    try:
        faucet = GitHub(type, str(index))
        faucet.task(type)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                faucet = GitHub(type, str(index))
                result = faucet.task(type)
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    faucet.page.quit()
                    return True
                return False
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                faucet.page.quit()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/github/github.py run -t bit -i 1-10


if __name__ == "__main__":
    # cli()
    #
    _run_task(BrowserType.CHROME, 1)
