class LocalStorage:

    def __init__(self, page, index):
        self.page = page
        self.index = index

    def __len__(self):
        return self.page.run_js("return window.localStorage.length;")

    def items(self):
        return self.page.run_js(
            "var ls = window.localStorage, items = {}; "
            "for (var i = 0, k; i < ls.length; ++i) "
            "  items[k = ls.key(i)] = ls.getItem(k); "
            "return items; "
        )

    def keys(self):
        return self.page.run_js(
            "var ls = window.localStorage, keys = []; "
            "for (var i = 0; i < ls.length; ++i) "
            "  keys[i] = ls.key(i); "
            "return keys; "
        )

    def get(self, key):
        return self.page.run_js(
            "return window.localStorage.getItem(arguments[0]);", key
        )

    def set(self, key, value):
        self.page.run_js(
            "window.localStorage.setItem(arguments[0], arguments[1]);", key, value
        )

    def has(self, key):
        return key in self.keys()

    def remove(self, key):
        self.page.run_js("window.localStorage.removeItem(arguments[0]);", key)

    def clear(self):
        self.page.run_js("window.localStorage.clear();")

    def __getitem__(self, key):
        value = self.get(key)
        if value is None:
            raise KeyError(key)
        return value

    def __setitem__(self, key, value):
        self.set(key, value)

    def __contains__(self, key):
        return key in self.keys()

    def __iter__(self):
        return self.items().__iter__()

    def __repr__(self):
        return self.items().__str__()
