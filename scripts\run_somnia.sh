#!/bin/bash

# 添加错误处理
set -e

# 检查参数是否有效
if [ $# -eq 1 ] && [ "$1" != "ads" ] && [ "$1" != "bit" ] && [ "$1" != "chrome" ] && [ "$1" != "more" ]; then
    echo "用法: $0 [ads|bit|chrome]"
    echo "示例: $0 ads"
    exit 1
fi

# 记录开始时间
echo "开始执行时间: $(date)"

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（脚本目录的上一级）
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." && pwd )"

# 切换到项目目录
cd "$PROJECT_DIR"

# 检查虚拟环境目录
if [ -d "$PROJECT_DIR/.venv" ]; then
    VENV_DIR="$PROJECT_DIR/.venv"
elif [ -d "$PROJECT_DIR/venv" ]; then
    VENV_DIR="$PROJECT_DIR/venv"
else
    echo "错误：未找到虚拟环境目录，请确保已创建虚拟环境（.venv 或 venv）"
    exit 1
fi

# 检查操作系统类型
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    # Windows 系统，使用 Scripts/activate
    ACTIVATE_PATH="$VENV_DIR/Scripts/activate"
else
    # Unix 系统，使用 bin/activate
    ACTIVATE_PATH="$VENV_DIR/bin/activate"
fi

# 激活虚拟环境
if [ -f "$ACTIVATE_PATH" ]; then
    source "$ACTIVATE_PATH"
else
    echo "错误：未找到激活脚本 $ACTIVATE_PATH"
    exit 1
fi



# # 根据是否有参数构建命令
if [ $# -eq 1 ]; then
    echo "开始执行 somnia 任务... 类型: $1"
    if [ "$1" == "ads" ]; then
        python3 "${PROJECT_DIR}/examples/somnia/index.py" gm -i 1-100 -w 2 -t "$1"
    elif [ "$1" == "bit" ]; then
        python3 "${PROJECT_DIR}/examples/somnia/index.py" gm -i 1-100 -w 2 -t "$1"
    elif [ "$1" == "chrome" ]; then
        python3 "${PROJECT_DIR}/examples/somnia/index.py" gm -i 401-500 -w 2 -t "$1"
    fi
else
    echo "开始执行 somnia 任务..."
    python3 "${PROJECT_DIR}/examples/somnia/index.py" gm -i 1-100 -w 2
fi

# 退出虚拟环境
deactivate

# 记录结束时间
echo "结束执行时间: $(date)"
