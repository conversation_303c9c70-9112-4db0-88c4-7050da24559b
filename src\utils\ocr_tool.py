import ddddocr

class OcrTool:
    """
    封装了 ddddocr 的 ONNX 推理分类功能。
    初始化时传入模型文件和字符集文件路径，调用 classify(image_path) 返回分类结果。
    """
    def __init__(self,
                 onnx_model_path: str,
                 charsets_path: str,
                 show_ad: bool = False):
        # det=False, ocr=False 表示只做 classification
        self.ocr = ddddocr.DdddOcr(
            det=False,
            ocr=False,
            show_ad=show_ad,
            import_onnx_path=onnx_model_path,
            charsets_path=charsets_path
        )

    def classify(self, image_path: str):
        """
        对一张本地图片做分类，返回分类结果（通常是一个字符串或标签列表）。
        """
        with open(image_path, 'rb') as f:
            image_bytes = f.read()
        return self.ocr.classification(image_bytes) 