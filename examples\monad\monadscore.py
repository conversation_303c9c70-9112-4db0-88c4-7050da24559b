import os
import random
import sys
from time import sleep
from src.utils.thread_executor import ThreadExecutor
import click
from loguru import logger
import time
from retry import retry
# 使用完整的导入路径
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.element_util import get_element

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")


class MonadScore:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    
    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for i in range(try_count):
            connect_wallet_button = lasted_tab.ele(
                "x://button[contains(., 'Connect Wallet') or contains(., '连接钱包')]",
                timeout=5,
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele(
                    "x://button[contains(., 'OKX Wallet') ]"
                ).click()
                sleep(2)
                self.browser_controller.okx_wallet_connect()
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False
    def _check_connect_wallet(self, lasted_tab):
        if lasted_tab.ele(
            "x://button[contains(., 'Connect Wallet') or contains(., '连接钱包')]",
            timeout=5,
        ):
            self._connect_wallet(lasted_tab)
            return True
        return False
    def _is_run(self, tab):
        for i in range(5):  # 最多等待10次，每次0.5秒

            # 检查是否已经运行过，这样就不做任务
            epoch_ele = get_element(tab, "x://span[contains(., 'Next Epoch') ]", 1)
            if epoch_ele:
                logger.success(f"【{self.id}】 节点已经运行")
                return True

            time.sleep(1)
        else:
            logger.error(f"【{self.id}】 节点未运行")
            return False
    def _is_login(self, tab):
            for i in range(5):  # 最多等待10次，每次0.5秒
                login_ele = get_element(tab, "x://span[contains(., 'Connect Wallet to continue to dashboard') ]", 1)
                if login_ele:
                    logger.success(f"【{self.id}】 无法登陆成功")
                    return False
            return True
 
    def _is_claim(self, tab):
            for i in range(180):  # 最多等待10次，每次0.5秒
                # 检查是否有交易历史不足的提示
                epoch_ele = get_element(tab, "x://span[contains(., 'Next Epoch') ]", 1)
                if epoch_ele:
                    logger.success(f"【{self.id}】 节点运行成功")
                    return True
       
                time.sleep(1)
            else:
                logger.error(f"【{self.id}】 Neither error message nor button appeared within timeout")
                return False
    def _check_task_complete(self, lasted_tab):

        try:
            logger.info(f"【{self.id}】开始完成任务")
            # 检查是否存在领取按钮
            task_eles = lasted_tab.eles("x://button[contains(text(),'Do Task')]", timeout=5)
            i=0
            for ele in task_eles:
                print(i)
                tab = ele.click.for_new_tab()
                sleep(10)
                tab.close()
                ele.click()
                sleep(3)
                i+=1
                sleep(15)
            logger.info(f"【{self.id}】开始Claim")
            claim_eles = lasted_tab.eles("x://button[contains(text(),'Claim')]", timeout=5)
            for ele in claim_eles:
                tab = ele.click()
                sleep(3)
            for i in range(3):
                claim_eles = lasted_tab.eles("x://button[contains(text(),'Claim')]", timeout=5)
                if  claim_eles:
                    lasted_tab.refresh()
                    for ele in claim_eles:
                        sleep(3)
                        tab = ele.click()
                        sleep(3)
                else:
                    return True
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 完成任务异常，error={str(e)}")
            
        return True
    @retry(tries=3, delay=1)
    def monadscore(self):
        try:
            self.browser_controller.okx_wallet_login()
            address = self.browser_controller.browser_config.evm_address
            logger.info(f"Executing task for {self.id} with browser type {self.browser_type} with address  {address}")
            sleep(3)
            url = "http://monadscore.xyz/signup/r/Q101yMCI"
            tab = self.page.new_tab(url)
            sleep(3)
            self._check_connect_wallet(tab)
            sleep(3)
            logger.info(f"【{self.id}】 检查是否已经运行过")
            if not self._is_login(tab):
                return False
            if  self._is_run(tab):
                return True
            score_ele = get_element(tab, "x://strong", 5)
            if score_ele:
                if float(score_ele.raw_text)<=0:
                    logger.info(f"【{self.id}】 开始完成任务")
                    task_tab = get_element(tab, "x://span[contains(., 'Tasks') ]", 5)
                    if task_tab :   
                        task_tab.click()
                        self._check_task_complete(tab)
                        sleep(2)

            dashboard_tab = get_element(tab, "x://span[contains(., 'Dashboard') ]", 5)
            if dashboard_tab :
                dashboard_tab.click()
                sleep(2)
            run_node_btn = get_element(tab, "x://button[normalize-space()='Run Node']", 5)
            if run_node_btn :
                logger.info(f"【{self.id}】 运行节点 ")
                run_node_btn.click()
                sleep(2)
                self.browser_controller.okx_wallet_connect()
                sleep(20)

          
            # 如果执行到这里，claim_btn已经找到了
            if self._is_claim(tab):
                return True

            return False
        except Exception as e:
            logger.error(f"【{id}】 节点运行异常，error={str(e)}")
            raise Exception((f"【{id}】 发生异常,重试"))
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(e)
        return False


def _run_task(type, index):
    try:
        browser = MonadScore(type, str(index))
        browser.monadscore()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        # 新增成功列表
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
            
                browser = MonadScore(type, str(index))
                result = browser.monadscore()  # 只调用一次
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    sleep(10)
                    browser.page.quit()
                return result  # 返回结果
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_quicknode-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/monadscore.py run -t ads -i 1-10
# python3 examples/monad/monadscore.py run -t bit -i 1-10

if __name__ == "__main__":
    cli()
    #_run_task(BrowserType.BIT, 6)
