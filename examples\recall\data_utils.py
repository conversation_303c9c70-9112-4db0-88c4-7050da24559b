import os
from threading import Lock

from loguru import logger

from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

RECALL_DATA_PROPS = [
    "index",
    "type",
    "address",
    "owner_referral_code",
    "referral_code",
    "linked_twitter",
    "linked_discord",
]


class DataUtil:
    def __init__(self):
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            data_dir = os.path.join(get_project_root_path(), "examples", "recall")
            self._csv_path = os.path.join(data_dir, "recall.csv")
            self._csv = HHCSV(self._csv_path, RECALL_DATA_PROPS)
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def list(self):
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部数据失败: {str(e)}")
            return []

    def get(self, address):
        try:
            result = self._csv.query({"address": address})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 {address} 数据失败: {str(e)}")
            return {}

    def add(self, data):
        try:
            self._csv.add_row(data)
            # self._csv.load()
        except Exception as e:
            logger.error(f"新增数据失败, data={data}, error={str(e)}")

    def update(self, address, data):
        try:
            criteria = {"address": address}
            self._csv.update_row(criteria, data)
            # self._csv.load()
        except Exception as e:
            logger.error(
                f"更新数据失败, address={address}, data={data}, error={str(e)}"
            )

    def flush(self):
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新数据失败, error={str(e)}")
