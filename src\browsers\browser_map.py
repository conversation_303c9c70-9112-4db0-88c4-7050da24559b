from ..enums.browsers_enums import BrowserType
from .ads_browser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .bit_browser import <PERSON><PERSON>rows<PERSON>
from .brave_browser import Brave<PERSON>rows<PERSON>
from .chrome_browser import Chrome<PERSON>rowser
from .local_browser import LocalBrowser
from .morelogin_browser import MoreLoginBrowser

BROWSER_MAP = {
    BrowserType.ADS: AdsBrowser,
    BrowserType.BIT: BitBrowser,
    BrowserType.CHROME: ChromeBrowser,
    BrowserType.BRAVE: BraveBrowser,
    BrowserType.MORE: MoreLoginBrowser,
    BrowserType.LOCAL: LocalBrowser,
}

BROWSER_TYPES = [BrowserType.ADS, BrowserType.BIT, BrowserType.CHROME, BrowserType.BRAVE, BrowserType.MORE, BrowserType.LOCAL]
