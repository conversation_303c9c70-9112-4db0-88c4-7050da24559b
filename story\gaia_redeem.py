import json
from loguru import logger
from time import sleep
from retry import retry
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
import traceback
import requests
from src.evm import ChainInfo
from src.evm.erc20_utils import get_web3
from src.evm.erc20_utils import send_transaction
import time
class StoryGaiaRedeem(StoryBase):
    """Gaia信用分Redeem任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "gaia_redeem"
        self.home_url = "https://www.gaianet.ai/reward-summary"
        self.api_url = "https://api.gaianet.ai/api/v1"
        self.wallet_address = self.browser.browser_config.evm_address
        self.private_key = self.browser.browser_config.evm_private_key
        self.proxy = self.browser.browser_config.proxy
        self.redeem_contract_address = "******************************************"
        self.redeem_contract_abi = [
            {
                "inputs": [
                    {"internalType": "string", "name": "orderId", "type": "string"},
                    {"internalType": "uint256", "name": "amount", "type": "uint256"},
                    {"internalType": "uint256", "name": "timestamp", "type": "uint256"},
                    {"internalType": "bytes", "name": "signature", "type": "bytes"}
                ],
                "name": "redeem",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]
        

    def _check_wallet_connected(self, tab) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = tab.ele(
                "x://span[contains(text(),'...')]",
                timeout=30,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            tab = page.latest_tab
            if self._check_wallet_connected(tab):
                return True

            connect_button = page.latest_tab.ele("x://button[text()='Connect']", timeout=30)
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect 按钮失败"
                )

            connect_button.click()

            self._connect_okx_wallet()

            # # 检查连接状态
            if self._check_wallet_connected(tab):
                return True

            return False

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")
        
    def _wait_for_x(
        self, page, title, timeout: int = 5
    ) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == title:
                return True
            sleep(1)
        return False
        
    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Gaia信用分Redeem任务"""
        
        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url, new_tab=True)

             # 可能需要同意条款
            terms = self.browser.page.latest_tab.ele("x://span[text()='Terms of Service']", timeout=5)
            if terms:
                self.browser.page.latest_tab.ele("x://button[text()='Accept']", timeout=5).click()
                sleep(1)
                if self._wait_for_x(self.browser.page, "OKX Wallet", timeout=15):
                    self._sign_okx_wallet()
            sleep(3)

            tab = self.browser.page.latest_tab

            # network error
            tab = self.browser.page.latest_tab
            network_error_ele = tab.ele("x://button[.='Network Error']", timeout=5)
            if network_error_ele:
                tab.ele("x://span[.='Base']", timeout=5).click()
                sleep(1)

            tab.listen.start('api/v1/users/profile')
            tab.get(self.home_url)
            response = tab.listen.wait(timeout=10, count=1)
            if response:
                # logger.info(f"{self.browser_id} 获取token: {response.request.headers}")
                token = response.request.headers['Authorization']
                headers = {
                    'accept': 'application/json, text/plain, */*',
                    'authorization': token
                }
                redeem_data = requests.get(
                    url=f"{self.api_url}/reward/points-redeem/preview/",
                    headers=headers
                )
                if redeem_data.status_code == 200:
                    # logger.info(f"{self.browser_id} 获取redeem数据: {redeem_data.json()}")
                    if redeem_data.json()['data']['redeemable_credits'] < 450:
                        logger.warning(f"{self.browser_id} redeem数量{redeem_data.json()['data']['redeemable_credits']}小于450")
                        return True
                    #获取redeem数据: {'code': 0, 'msg': 'OK', 'data': {'total_redeemable_points': 1900.8283, 'today_redeemable_points': 1000.0, 'points_credits_exchange_rate': 0.5, 'redeemable_credits': 500, 'daily_redeem_limit': 1000}, 'ts': 1741107296, 'meta': {}}
                else:
                    logger.error(f"{self.browser_id} 获取redeem数据失败 {redeem_data.status_code} {redeem_data.text}")
                    if redeem_data.status_code == 401:
                        raise Exception(f"{self.browser_id} token过期")
                    return False
                # 先获取web3实例，因为create redeem order 15分钟才能执行一次
                rpc_url = ChainInfo.BASE["rpc"]
                logger.info(f"[INFO] 使用代理: {self.proxy}")
                self.web3 = get_web3(rpc_url, self.proxy, user_agent=self.browser.browser_config.user_agent)
                if self.web3 is None:
                    return False
                logger.info(f"[INFO] 获取web3实例成功")
                # 创建redeem订单
                headers = {
                    'accept': 'application/json, text/plain, */*',
                    'content-type': 'application/json',
                    'authorization': token
                }
                redeem_order = requests.post(
                    url=f"{self.api_url}/credit/create-redeem-order/",
                    headers=headers,
                    json={
                        "points_to_redeem": redeem_data.json()['data']['today_redeemable_points']
                    }
                )
                if redeem_order.status_code != 200:
                    # {
                    # "code": 0,
                    # "msg": "OK",
                    # "data": {
                    #     "id": "5bd5ad43-d226-491b-a398-ccaa4f83f5bf",
                    #     "user_id": "cc200f99-0d98-46a7-a3cd-718429f7089f",
                    #     "user_address": "0x7B807d2994803e6a55627Ee3e8e543BDB1337ee7",
                    #     "order_type": "REDEEM",
                    #     "status": "PENDING",
                    #     "exchange_rate": "0.50",
                    #     "credits_amount_scaled": 5000000,
                    #     "txid": null,
                    #     "created_at": "2025-03-05T02:18:39.849764Z",
                    #     "updated_at": "2025-03-05T02:18:39.857746Z",
                    #     "points_amount_scaled": 10000000,
                    #     "timestamp": 1741141089,
                    #     "signature": "0xd6a12bae3dca57fd712cb466acbf52557f72b431a5b698695bd8456b99023b1a6d910e4928b4f5bd05208f8502ff7da1fe8097c64079154174813c96c57a9e961c",
                    #     "credits_amount": 500,
                    #     "points_amount": 1000
                    # },
                    # "ts": 1741141119,
                    # "meta": {}
                    # }
                    logger.error(f"{self.browser_id} 创建redeem订单失败 {redeem_order.status_code} {redeem_order.text}")
                    return False
                # 构建交易
                tx_params = {
                    'from': self.wallet_address,
                    'nonce': self.web3.eth.get_transaction_count(self.wallet_address),
                }
                # 执行合约函数
                redeem_contract = self.web3.eth.contract(address=self.redeem_contract_address, abi=self.redeem_contract_abi)
                redeem_function = redeem_contract.functions.redeem(redeem_order.json()['data']['id'], redeem_order.json()['data']['credits_amount_scaled'], redeem_order.json()['data']['timestamp'], redeem_order.json()['data']['signature'])
                transaction = redeem_function.build_transaction(tx_params)
                if not transaction:
                    logger.error(f"[ERROR] 构建交易失败")
                    return False
                # 执行交易
                result = send_transaction(self.web3, transaction, self.private_key)
                if result:
                    logger.success(f"[SUCCESS] redeem 交易成功")
            else:
                raise Exception(f"{self.browser_id} 获取token失败")
            
            logger.success(f"{self.browser_id} Gaia信用分Redeem任务完成")   
            return True

        # except WalletConnectionError as e:
        #     logger.error(f"{self.browser_id} 钱包连接失败: {e}")
        #     # 输出traceback
        #     logger.error(traceback.format_exc())
        #     # raise
        # except Exception as e:
        #     logger.error(f"{self.browser_id} Gaia信用分Redeem任务失败: {e}")
        #     # 输出traceback
        #     logger.error(traceback.format_exc())
        finally:
            self.browser.page.latest_tab.close()
            
