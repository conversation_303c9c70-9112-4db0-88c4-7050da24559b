import requests
from typing import Optional, Dict
from loguru import logger
from retry import retry


class Proxies:
    def __init__(self, proxy_url: str):
        self.proxy_url = proxy_url

    def _format_proxy_url(self, proxy_url: str) -> str:
        """
        格式化代理地址

        Args:
            proxy_url: 原始代理地址

        Returns:
            str: 格式化后的代理地址
        """
        try:
            if not proxy_url.startswith(("http://", "https://", "socks5://")):
                return f"http://{proxy_url}"
            return proxy_url
        except Exception as e:
            logger.error(f"代理地址格式化失败: {str(e)}")
            return proxy_url

    def get_proxies(self) -> Dict[str, str]:
        """
        获取代理配置
        """
        proxies = None
        try:
            if "@" in self.proxy_url:
                # 处理带认证的代理
                proxies = {"http": self.proxy_url, "https": self.proxy_url}
            else:
                # 处理普通代理
                formatted_proxy = self._format_proxy_url(self.proxy_url)
                proxies = {"http": formatted_proxy, "https": formatted_proxy}
        except Exception as e:
            logger.error(f"代理设置失败: {str(e)}")
            proxies = None

        return proxies

    @retry(tries=3, delay=1)
    def verify(self) -> bool:
        """验证当前IP地址

        Returns:
            验证是否成功
        """
        try:
            proxies = self.get_proxies()
            response = requests.get(
                url="https://myip.ipip.net",
                proxies=proxies,
                verify=False,
            )
            logger.info(f"验证成功, IP信息: {response.text}")
            return True
        except Exception as e:
            # logger.error(f"IP验证失败: {str(e)}")
            return False

    def get_real_ip(self) -> str:
        """获取真实IP

        Returns:
            str: 真实IP
        """
        try:
            check_url: str = "https://myip.ipip.net/json"
            proxies = self.get_proxies()
            response = requests.get(
                url=check_url,
                proxies=proxies,
                verify=False,
            )
            return response.json()["data"]["ip"]
        except Exception as e:
            logger.error(f"获取真实IP失败: {str(e)}")
            return ""
