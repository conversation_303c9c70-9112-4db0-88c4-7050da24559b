import random
import re
from time import sleep

from loguru import logger
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from retry import retry
from typing import Optional
from src.utils.common import generate_username
from config import ICLOUD_EMAIL, ICLOUD_EMAIL_PASSWORD, MAX_GAS_PRICE


class StoryBlockbook(StoryBase):
    """Blockbook任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "blockbook"
        self.home_url = "https://badge-claim.blockbook.app/badge-claim"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"

    def _connect_wallet(self, page) -> bool:
        """连接钱包"""
        return True

    def _request_get_signature(self, address: str) -> Optional[str]:
        """获取签名"""
        try:
            url = "https://badge-claim.blockbook.app/api/signature"
            data = {"address": address}

            headers = {
                "content-type": "application/json",
                "origin": "https://badge-claim.blockbook.app",
                "referer": "https://badge-claim.blockbook.app/badge-claim",
            }
            success, response_data = self._make_post_request(
                url=url, data=data, headers=headers
            )
            if "signature" in response_data:
                signature = response_data["signature"]
                return signature

            if not success or "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )

            return None

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return None

    def mint_nft(self, signature: str) -> bool:
        """铸造NFT"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                self.badge_contract_address,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False

    def _add_address(self, page):
        """添加地址"""
        try:
            address_input = page.ele("x://input[@placeholder='0xYourWalletAddress...']")
            if address_input:
                address_input.input(self.browser.browser_config.evm_address)
                page.ele("x://button[text()='Submit Wallet Address']").click()

                verify_button = page.ele("x://button[text()='Verify']")
                if verify_button:
                    verify_button.click()
                    page.latest_tab.close()

                verify_button = page.ele("x://button[text()='Verify']")
                if verify_button:
                    verify_button.click()
                    page.latest_tab.close()

            return True
        except Exception as e:
            logger.error(f"{self.browser_id} 添加地址异常: {str(e)}")
            return False

    def _register(self, page):
        """注册"""
        try:
            name_input = page.ele(
                "x://input[@placeholder='Choose a unique username']", timeout=20
            )
            if not name_input:
                logger.error(f"{self.browser_id} 注册页面未找到用户名输入框")
                return False

            name_input.input(generate_username())
            page.ele("x://button[@type='submit']").click()
            sleep(3)

            eles = page.eles("x://div[@role='radiogroup']/div/button")
            if not eles:
                logger.error(f"{self.browser_id} 注册页面未找到性别选择")
                return False

            # 随机选择0-1
            eles[random.randint(0, 1)].click()

            checkbox = page.ele("x://button[@role='checkbox']")
            if checkbox and checkbox.attr("aria-checked") == "false":
                checkbox.click()
            sleep(1)
            page.ele("x://button[@type='submit']").click()

            return True
        except Exception as e:
            logger.error(f"{self.browser_id} 注册异常: {str(e)}")
            return False

    def _submit_content(self, page):
        """提交内容"""
        try:

            claim_badge = page.ele("x://span[text()='Claim Badge']/..", timeout=5)
            if claim_badge:
                return True

            urls = [
                "https://www.blockbook.app/p/architect/spark/nicole-smith",
                "https://www.blockbook.app/p/architect/spark/samuel-jones",
                "https://www.blockbook.app/p/architect/spark/didi-lewis",
                "https://www.blockbook.app/p/architect/spark/samantha-sol",
                "https://www.blockbook.app/p/architect/spark/pete-ringer-",
            ]

            # 随机获取三个
            random_urls = random.sample(urls, 3)
            success_count = 0
            for url in random_urls:
                try:
                    latest_tab = page.new_tab(url)
                    latest_tab.ele("Submit your Content").click()

                    # 点击文本
                    latest_tab.ele(
                        "x://div[@role='dialog']//button/span[text()='Text']/.."
                    ).click()

                    sleep(5)

                    # 输入内容
                    latest_tab.ele("x://div[@class='ql-editor ql-blank']").input(
                        self.generate_sample_contents()
                    )

                    # 点击发布
                    latest_tab.ele("x://span[text()='Submit Block']/../..").click()

                    # CONGRATS!

                    if latest_tab.ele(
                        "x://*[contains(@id,'radix')]/div[2]/div/div[1]", timeout=30
                    ):
                        latest_tab.close()
                        success_count += 1
                        logger.success(f"{self.browser_id} 提交内容成功")
                        latest_tab.close()
                        continue

                    latest_tab.close()

                except Exception as e:
                    logger.error(f"{self.browser_id} 提交内容异常: {str(e)}")
                    continue

            if success_count == 3:
                page.ele("x://button[text()='Verify']").click()

                connect_wallet = page.ele("x://button[text()='Connect Wallet']")
                if connect_wallet:
                    connect_wallet.click()
                    page.ele(
                        "x://button[@data-testid='rk-wallet-option-com.okex.wallet']"
                    ).click()
                    self._connect_okx_wallet()
                    return True

        except Exception as e:
            logger.error(f"{self.browser_id} 提交内容异常: {str(e)}")
            return False

    def get_verification_code(self) -> Optional[str]:
        """从iCloud获取验证码"""
        from src.emails import ICloudClient, SearchCriteria

        email = ICLOUD_EMAIL
        password = ICLOUD_EMAIL_PASSWORD
        if not email or not password:
            raise TaskExecutionError(
                f"{self.browser_id} 邮箱或密码为空, 请检查.env配置"
            )

        try:
            with ICloudClient(email, password).connect() as client:
                # 依次在收件箱和垃圾邮件文件夹中搜索
                found_folder = None
                for folder in ["INBOX", "Junk"]:
                    emails = client.search_emails_with_retry(
                        SearchCriteria(
                            folder=folder,
                            subject="is your verification code",
                            to=self.browser.browser_config.email,
                        )
                    )
                    if emails:
                        found_folder = folder
                        break

                if not emails:
                    logger.error(f"{self.browser_id} 未找到验证邮件")
                    return None

                # 检查收件人是否匹配
                email_info = emails[0]
                # to_email = email_info["to"]
                # hidden_email = self.browser.browser_config.email.lower()
                # if hidden_email not in to_email.lower():
                #     logger.error(
                #         f"{self.browser_id} 验证邮件收件人不匹配: {to_email} != {hidden_email}"
                #     )
                #     return None

                # 从邮件内容中提取验证码
                subject = email_info["subject"]

                match = re.search(r"\b(\d{6})\b", subject)
                verify_code = match.group(1) if match else None
                if not verify_code:
                    logger.error(f"{self.browser_id} 提取验证码失败")
                    return None

                logger.success(f"{self.browser_id} 匹配到验证码: {verify_code}")

                # 删除邮件
                try:
                    if email_info["id"] and found_folder:
                        client.delete_emails([email_info["id"]], folder=found_folder)
                        logger.debug(
                            f"{self.browser_id} 成功删除验证码邮件 (folder: {found_folder})"
                        )
                except Exception as e:
                    logger.warning(f"{self.browser_id} 删除验证码邮件失败: {e}")

                return verify_code

        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
            return None

    def _sign_up(self, page):
        """注册"""
        email = self.browser.browser_config.email
        password = self.browser.browser_config.email_password
        if not email or not password:
            logger.error(f"{self.browser_id} 邮箱或密码为空")
            return False

        ele = page.ele("x://input[@type='email']", timeout=10)
        ele.clear(True)
        ele.input(email)
        sleep(1)
        pwd_ele = page.ele("x://input[@type='password']", timeout=10)
        pwd_ele.clear(True)
        pwd_ele.input(password)
        sleep(1)

        page.ele("x://button[text()='Continue']").click()

        # 输入验证码
        ele = page.ele("x://p[text()='Verification code']", timeout=30)
        if not ele:
            logger.error(f"{self.browser_id} 注册页面未找到验证码输入框")
            return False

        # 获取验证码
        code = self.get_verification_code()
        if not code:
            logger.error(f"{self.browser_id} 获取验证码失败")
            return False

        sleep(1)
        inputs = page.eles("x://input[@inputmode='numeric']")
        if not inputs:
            logger.error(f"{self.browser_id} 注册页面未找到验证码输入框")
            return False

        for i, digit in enumerate(code):
            if i < len(inputs):
                inputs[i].input(digit)
                sleep(0.2)  # 添加短暂延迟模拟人工输入

        # page.ele("x://button[text()='Continue']").click()
        sleep(5)
        if not self._register(page):
            return False

        if not self._add_address(page):
            return False

        return True

    def _sign_in(self, page):
        """登录"""
        email_input = page.ele("x://input[@type='email']", timeout=10)
        if email_input:
            email = self.browser.browser_config.email
            if "icloud" in email:
                page.ele("x://a[text()='Sign up']", timeout=5).click()
                sleep(5)
                if not self._sign_up(page):
                    return False
            else:
                page.ele(
                    "x://span[@data-localization-key='socialButtonsBlockButton']",
                    timeout=5,
                ).click()
                sleep(5)
                page.ele("x://div[@data-authuser='0']", timeout=5).click()

                ele = page.ele(
                    "x://div[contains(text(), 'By continuing, Google will share your name')]"
                )
                if not ele:
                    logger.error(f"{self.browser_id} 登录页面未找到同意按钮")
                    return False

                page.eles("x://button", timeout=5)[1].click()

                if not self._register(page):
                    return False

                # 添加地址&verify x dc
                sleep(5)
                if not self._add_address(page):
                    return False

        return True

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Blockbook任务"""
        if self._check_task_completed():
            return False

        # self.get_verification_code()
        # return False

        # 检查NFT是否已铸造
        nft_contract = self.get_nft_contract()
        if nft_contract.is_minted(self.badge_contract_address):
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            # 返回false不会打开页面
            return False

        try:

            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            result = self._sign_in(self.page)
            if not result:
                logger.error(f"{self.browser_id} 登录失败")
                return False

            # 提交内容
            if not self._submit_content(self.page):
                return False

            # gas低了再启用
            gas_prices = self.get_gas_price()
            if gas_prices and gas_prices.average > MAX_GAS_PRICE:
                logger.warning(
                    f"{self.browser_id} gas价格高于{MAX_GAS_PRICE}, 跳过铸造"
                )
                return False

            # 获取签名
            signature = self._request_get_signature(
                self.browser.browser_config.evm_address
            )
            if not signature:
                logger.error(f"{self.browser_id} 获取签名失败")
                return False

            # 铸造NFT
            if not self.mint_nft(signature):
                return False

            logger.success(f"{self.browser_id} Blockbook任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Blockbook任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Blockbook任务失败")

    def generate_sample_contents(
        self,
    ) -> str:
        return generate_username(digits=0)
