import os

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from data_utils import DataUtil
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import Browser<PERSON>ontroller
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import parse_indices

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/somnia_discord.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")


class DCChatBot(BrowserConfigInstance):

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def send_message(self, channel_id: str, message: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(channel_id, message):
            raise Exception("发送消息失败")
        return True


def _join_dc_server(type: BrowserType, index: str, need_wait_captcha: bool = True):
    browser = BrowserController(type, str(index))
    address = browser.browser_config.evm_address
    data_util = DataUtil()
    record = data_util.get(address) or {}
    servers = [
        {
            "name": "Recall",
            "server_id": "1321243373226561600",
            "invite_code": "recallnet",
        },
    ]
    fail_servers = []
    for server in servers:
        invite_code = server.get("invite_code")
        server_id = server.get("server_id")
        name = server.get("name")
        try:
            if record.get(f"Discord {name}", "") == "1":
                logger.success(f"【{index}】已加入 {name} 服务器")
                continue
            result = browser.join_dc_server(
                invite_code, need_wait_captcha, server_id, name
            )
            if result:
                data_util.update(address, {f"Discord {name}": "1"})
            if not result:
                fail_servers.append(invite_code)
        except Exception as e:
            logger.error(f"{index} 加群 {name} 失败: {e}")
            fail_servers.append(invite_code)

    browser.close_page()
    return fail_servers


@click.group()
def cli():
    pass


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--wait", is_flag=True, help="是否等待验证码")
@click.option("-r", "--retry", is_flag=True, help="是否重新打开")
def join_server(index, type, retry, wait):
    indices = parse_indices(index)
    fail_indices = {}
    for _index in indices:
        try:
            fail_servers = _join_dc_server(type, _index, wait)
            if fail_servers and len(fail_servers) > 0:
                fail_indices[_index] = fail_servers
        except Exception as e:
            logger.error(f"{_index} 加群失败: {e}")
            fail_indices[_index] = []
    if fail_indices:
        logger.error(f"加群失败：{fail_indices}")
    if retry and fail_indices:
        logger.info(
            f"加群执行完毕, 失败 {len(fail_indices)} 个, 成功 {len(indices) - len(fail_indices)} 个"
        )
        for id, invite_codes in fail_indices.items():
            browser = BrowserController(type, str(id))
            browser.page
            for invite_code in invite_codes:
                url = f"https://discord.com/invite/{invite_code}"
                browser.open_url(url, True)


# 加群
# python3 examples/recall/discord.py join -t chrome -w -r -i

if __name__ == "__main__":
    cli()

