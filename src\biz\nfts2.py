import os
from time import sleep

from faker import Faker
from loguru import logger

from src.biz.w3_manager import Web3Manager
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.utils.element_util import get_element
from src.utils.proxies import Proxies
from src.utils.secure_encryption import SecureEncryption

AVATAR_PATH = os.getenv("AVATAR_PATH")
SOMNIA_RPC = os.getenv("SOMNIA_RPC")
MONAD_RPC = os.getenv("MONAD_RPC")
PROXY_URL = os.getenv("PROXY_URL")


class NFTs2:
    def __init__(self, browser_controller: BrowserController):
        self.browser_controller = browser_controller
        self.page = self.browser_controller.page
        self.id = self.browser_controller.id
        self.evm_private_key = self.get_private_key()
        self._setting_proxy()
        self.user_agent = self.browser_controller.browser_config.user_agent
        self.address = self.browser_controller.browser_config.evm_address

    def _setting_proxy(self):
        proxies = Proxies(self.browser_controller.browser_config.proxy)
        if proxies.verify():
            self.proxy = self.browser_controller.browser_config.proxy
        else:
            self.proxy = PROXY_URL
        logger.info(f"【{self.id}】使用代理: {self.proxy}")

    def get_private_key(self) -> str | None:
        pk = self.browser_controller.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def _connect_wallet(self, tab):
        account_ele = get_element(tab, "x://button[@data-testid='rk-account-button']", 5)
        if account_ele:
            logger.success(f"账号 {self.browser_controller.id} 钱包已登录...")
            return True

        for _ in range(3):
            # 点击连接钱包
            connect_button = get_element(tab, "x://button[@data-testid='rk-connect-button']", 3)
            if connect_button:
                connect_button.click()
                result = try_click(tab, "x://button[.='OKX Wallet']")
                if result:
                    connect_result = self.browser_controller.okx_wallet_connect()
                    if not connect_result:
                        logger.error(f"账号 {self.browser_controller.id} 连接钱包失败")
                        continue

                # sign message
                sign_msg = tab.ele("x://button[@data-testid='rk-auth-message-button']", timeout=3)
                if sign_msg:
                    sign_msg.click()
                    sleep(1)
                    result = self.browser_controller.okx_wallet_sign()
                    if not result:
                        logger.error(f"账号 {self.browser_controller.id} 签名失败")
                        continue

            # 检查钱包是否登录成功
            account_ele = get_element(tab, "x://button[@data-testid='rk-account-button']", 5)
            if account_ele:
                logger.success(f"账号 {self.browser_controller.id} 钱包已登录...")
                return True

        return False

    def _select_avatar(self, tab):
        logger.info(f"{self.id} 准备上传图片...")
        file_ele = tab.ele("tag:input@type=file")

        avatar_path = AVATAR_PATH
        if not avatar_path:
            logger.error(f"{self.id} 图片路径未配置")
            return False

        _path = avatar_path.replace("*", self.id)

        if not os.path.exists(_path):
            logger.warning(f"{self.id} 图片文件不存在,跳过上传: {_path}")
            return False

        file_ele.input(rf"{_path}")
        logger.success(f"{self.id} 图片上传成功: {_path}")
        return True

    def _change_network(self, tab, network: str):
        try:
            # 检测网络
            network_ele = tab.ele(f"x://div[@class='mr-2']//button[contains(.,'{network}')]", timeout=3)
            if network_ele:
                logger.success(f"{self.id} 已经是{network}网络, 无需切换")
                return True

            result = self.browser_controller.okx_wallet_change_connect_site(network)
            if not result:
                logger.error(f"{self.id} 切换站点失败")
                return False

            # change_network_button = tab.ele("x://div[text()='Network']/..//button", timeout=3)
            # if not change_network_button:
            #     logger.error(f"{self.id} 网络切换按钮查找失败")
            #     return False

            # change_network_button.click()

            # search_input = tab.ele("x://input[@type='search']", timeout=3)
            # if not search_input:
            #     logger.error(f"{self.id} 搜索输入框查找失败")
            #     return False

            # search_input.input(network)
            # sleep(1)
            # network_ele = tab.ele(f"x://div[@class='font-semibold' and contains(text(), '{network}')]", timeout=3)
            # if not network_ele:
            #     logger.error(f"{self.id} 网络选择失败")
            #     return False

            # network_ele.click()
            # sleep(1)

            network_ele = tab.ele(f"x://div[@class='mr-2']//button[contains(.,'{network}')]", timeout=3)
            if not network_ele:
                logger.error(f"{self.id} 网络选择失败")
                return False

            logger.success(f"{self.id} 网络切换成功: {network}")
            return True
        except Exception as e:
            logger.error(f"{self.id} 网络切换失败: {e}")
            return False

    def task_create_edition(self, network: str = "Somnia"):
        if network == "Somnia":
            rpc = SOMNIA_RPC
        elif network == "Monad":
            rpc = MONAD_RPC
        else:
            raise ValueError(f"不支持的网络: {network}")

        if not rpc:
            logger.error(f"【{self.id}】{self.address} 没有配置{network}网络的RPC")
            return False

        w3_manager = Web3Manager(self.id, self.evm_private_key, rpc, self.proxy, self.user_agent)
        balance = w3_manager.get_balance()
        if balance < 0.1:
            logger.warning(f"【{self.id}】{self.address} 余额{balance} 不足交互")
            return False

        tab = None
        try:
            result = self.browser_controller.okx_wallet_login()
            if not result:
                logger.error(f"okx钱包 {self.browser_controller.id} 登录失败")
                return False

            tab = self.page.new_tab("https://nfts2me.com/create/edition/")
            result = self._connect_wallet(tab)
            if not result:
                logger.error(f"账号 {self.browser_controller.id} 连接钱包失败")
                return False

            result = self._change_network(tab, network)
            if not result:
                logger.error(f"{self.id} 网络切换失败")
                return False

            result = self._select_avatar(tab)
            if not result:
                logger.error(f"{self.id} 上传图片失败")
                return False

            # 点击创建
            name = Faker().first_name()
            input_text(tab, "x://input[@name='contract-name']", name)
            # input_text(tab, "x://input[@name='token-symbol']", name)
            input_text(tab, "x://textarea[@name='project']", name)

            result = try_click(tab, "x://button[@type='submit']")
            if not result:
                logger.error(f"{self.id} 点击提交按钮失败, 创建失败")
                return False

            # 判断图片是否已经上传完成
            ele = tab.ele("Uploading artwork to IPFS: 100%", timeout=90)
            if not ele:
                logger.error(f"{self.id} 图片上传失败")
                return False

            result = self.browser_controller.okx_wallet_sign()
            if not result:
                logger.error(f"{self.id} 签名失败")
                return False

            # 点击完成
            ele = tab.ele("Congrats! You've successfully deployed your NFT Project", timeout=30)
            if not ele:
                logger.error(f"{self.id} 创建失败")
                return False

            logger.success(f"{self.id} 创建成功")
            return True
        except Exception as e:
            logger.error(f"{self.id} 创建失败: {e}")
            return False
        finally:
            if tab:
                tab.close()


if __name__ == "__main__":
    from src.browsers import BrowserType

    type = BrowserType.CHROME
    index = "0"
    browser_controller = BrowserController(type, index)
    nfts2 = NFTs2(browser_controller)
    nfts2.task_create_edition(network="Monad")
