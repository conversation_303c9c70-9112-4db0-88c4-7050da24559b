from examples.sentient.index import AI
import time
import os
from functools import wraps

def measure_time(api_name):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            print(f"\n=== {api_name} 测试 ===")
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            print(f"{api_name} 响应时间: {end_time - start_time:.2f} 秒")
            print("响应结果:", result)
            return result
        return wrapper
    return decorator

@measure_time("百度千帆 API")
def test_baidu():
    api_key = os.getenv("BAIDU_API_KEY")
    base_url = 'https://qianfan.baidubce.com/v2'
    model = 'deepseek-v3'
    ai = AI(api_key=api_key, base_url=base_url, model=model)
    return ai.generate_question()

@measure_time("深度求索 API")
def test_deepseek():
    ai = AI()
    return ai.generate_question()

@measure_time("硅流 API")
def test_siliconflow():
    api_key = os.getenv("SILICONFLOW_API_KEY")
    base_url = "https://api.siliconflow.cn/v1"
    model = "deepseek-ai/DeepSeek-V3"
    ai = AI(api_key=api_key, base_url=base_url, model=model)
    return ai.generate_question()

@measure_time("火山引擎 API")
def test_volces():
    api_key = os.getenv("VOLCES_API_KEY")
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    model = "ep-20250217110611-cmj89"
    ai = AI(api_key=api_key, base_url=base_url, model=model)
    return ai.generate_question()

# 运行测试
test_baidu()
test_deepseek()
test_siliconflow()
test_volces()