import random

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3_with_proxy
from src.evm.erc20_utils import send_transaction
from src.evm import ERC20
from src.utils.secure_encryption import SecureEncryption
import time


class StoryD3xDaily(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "d3x_daily"
        self.wallet_address = self.browser_config.evm_address

        pk = self.browser_config.evm_private_key
        if SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        self.private_key = pk

        self.d3x_contract_address = "******************************************"
        self.d3x_contract_abi = [
            {
                "inputs": [
                    {"internalType": "address", "name": "testUsdt", "type": "address"}
                ],
                "name": "faucet",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            }
        ]
        self.test_usdt_contract_address = "******************************************"
        self.ptest_usdt_contract_address = "******************************************"
        self.ptest_usdt_contract_abi = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amount", "type": "uint256"},
                    {"internalType": "address", "name": "owner", "type": "address"},
                    {"internalType": "address", "name": "to", "type": "address"},
                ],
                "name": "redeem",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amount", "type": "uint256"},
                    {"internalType": "address", "name": "to", "type": "address"},
                ],
                "name": "deposit",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]

    def _faucet(self) -> bool:
        logger.info("[INFO] 执行faucet")

        try:
            # 构建交易
            tx_params = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }
            # 执行合约函数
            d3x_contract = self.web3.eth.contract(
                address=self.d3x_contract_address, abi=self.d3x_contract_abi
            )
            faucet_function = d3x_contract.functions.faucet(
                self.test_usdt_contract_address
            )
            transaction = faucet_function.build_transaction(tx_params)
            if not transaction:
                logger.error(f"[ERROR] 构建交易失败")
                return False
            # 执行交易
            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] faucet 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] faucet 异常: {e}")
            return False

    def _get_balance(self, contract_address: str) -> int:
        logger.info(f"[INFO] 检查{contract_address}余额")
        try:
            contract = ERC20(contract_address, self.web3)
            balance = contract.balance_of(self.wallet_address)
            logger.info(f"[INFO] {contract_address}余额: {balance}")
            return balance
        except Exception as e:
            logger.error(f"[ERROR] 检查{contract_address}余额异常: {e}")
            return 0

    def _test_usdt_approval(self, amount: int) -> bool:
        contract = ERC20(self.test_usdt_contract_address, self.web3)
        if not contract.approval(
            self.wallet_address,
            self.ptest_usdt_contract_address,
            self.private_key,
            amount,
        ):
            logger.error(f"[ERROR] {self.browser_id} test_usdt授权失败")
            return False
        return True

    def _d3x_vault(self) -> bool:
        logger.info("[INFO] 执行d3x vault")
        try:
            # 1. 检查ptest_usdt余额
            ptest_usdt_balance = self._get_balance(self.ptest_usdt_contract_address)
            if ptest_usdt_balance < 1:
                # 2. 执行deposit,先获取test_usdt余额
                test_usdt_balance = self._get_balance(self.test_usdt_contract_address)
                if test_usdt_balance > 0:
                    # 检查approval
                    if not self._test_usdt_approval(
                        self.web3.to_wei(int(test_usdt_balance), "ether")
                    ):
                        logger.error(
                            f"[ERROR] {self.browser_id} d3x daily vault 任务失败"
                        )
                        return False

                    # 如果余额小于1，直接使用余额，否则随机取整
                    amount = (
                        test_usdt_balance
                        if test_usdt_balance < 1
                        else random.randint(1, int(test_usdt_balance))
                    )
                    amount_in_wei = self.web3.to_wei(amount, "ether")
                    # 构建deposit交易
                    tx_params = {
                        "from": self.wallet_address,
                        "nonce": self.web3.eth.get_transaction_count(
                            self.wallet_address
                        ),
                    }
                    ptest_usdt_contract = self.web3.eth.contract(
                        address=self.ptest_usdt_contract_address,
                        abi=self.ptest_usdt_contract_abi,
                    )
                    deposit_function = ptest_usdt_contract.functions.deposit(
                        amount_in_wei, self.wallet_address
                    )
                    transaction = deposit_function.build_transaction(tx_params)
                    if not transaction:
                        logger.error(f"[ERROR] 构建交易失败")
                        return False
                    # 执行交易
                    result = send_transaction(self.web3, transaction, self.private_key)
                    if result:
                        logger.success(f"[SUCCESS] deposit 交易成功")
                    return result
                else:
                    logger.error(f"[ERROR] test_usdt余额不足")
                    return False
            else:
                # 3. 执行redeem
                # 构建redeem交易
                tx_params = {
                    "from": self.wallet_address,
                    "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                }
                ptest_usdt_contract = self.web3.eth.contract(
                    address=self.ptest_usdt_contract_address,
                    abi=self.ptest_usdt_contract_abi,
                )
                redeem_function = ptest_usdt_contract.functions.redeem(
                    self.web3.to_wei(int(ptest_usdt_balance), "ether"),
                    self.wallet_address,
                    self.wallet_address,
                )
                transaction = redeem_function.build_transaction(tx_params)
                if not transaction:
                    logger.error(f"[ERROR] 构建交易失败")
                    return False
                # 执行交易
                result = send_transaction(self.web3, transaction, self.private_key)
                if result:
                    logger.success(f"[SUCCESS] redeem 交易成功")
                return result
        except Exception as e:
            logger.error(f"[ERROR] d3x vault 执行失败: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 d3x daily 任务"""
        try:
            logger.info(f"[INFO] 开始执行 {self.browser_id} 的 d3x daily 任务")
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            user_agent = self.browser_config.user_agent
            self.web3 = get_web3_with_proxy(rpc_url, proxy, user_agent)

            # 1. faucet 逻辑，先检查test_usdt余额
            if self._get_balance(self.test_usdt_contract_address) == 0:
                if not self._faucet():
                    logger.error(f"[ERROR] {self.browser_id} d3x daily faucet 任务失败")
                    return False
                # 等待test_usdt余额，最长1分钟，如果1分钟后test_usdt余额仍然为0，则认为faucet失败
                for i in range(12):
                    if self._get_balance(self.test_usdt_contract_address) > 0:
                        break
                    time.sleep(5)
                if self._get_balance(self.test_usdt_contract_address) == 0:
                    logger.error(f"[ERROR] {self.browser_id} d3x daily faucet 任务失败")
                    return False
                time.sleep(15)

            # 执行d3x vault 逻辑
            if not self._d3x_vault():
                logger.error(f"[ERROR] {self.browser_id} d3x daily vault 任务失败")
                return False

            logger.success(f"[SUCCESS] {self.browser_id} d3x daily 任务执行完成")
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False

    def close(self):
        pass
