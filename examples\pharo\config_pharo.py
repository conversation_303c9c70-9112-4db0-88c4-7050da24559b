import os
from enum import Enum

# 默认配置
DEFAULT_BROWSER_TYPE = "BRAVE"
DEFAULT_TIMEOUT = 30
DEFAULT_RETRY_COUNT = 3
DEFAULT_RETRY_DELAY = 3

# 代理检测配置
PROXY_CHECK = False  # 默认不开启代理检测

# 文件路径配置
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(PROJECT_ROOT, "examples", "pharo")
CSV_PATH = os.path.join(DATA_DIR, "pharo.csv")

# 日志配置
LOG_DIR = os.path.join(PROJECT_ROOT, "examples")
LOG_FILE = os.path.join(LOG_DIR, "pharo", "pharo.log")
LOG_ROTATION = "20 MB"  # 日志文件大小限制
LOG_RETENTION = "30 days"  # 日志保留时间
LOG_COMPRESSION = "zip"  # 日志压缩格式
LOG_LEVEL = "INFO"  # 日志级别
LOG_FORMAT = (
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
    "<level>{level: <8}</level> | "
    "{extra[task_id]} | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
    "<level>{message}</level>\n"
    "{exception}"
)

# 任务配置
TASK_TIMEOUT = 6 * 3600  # 6小时
TASK_RETRIES = 3
TASK_INTERVAL = 10
