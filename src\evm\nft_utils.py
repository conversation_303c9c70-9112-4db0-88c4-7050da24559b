from loguru import logger
from typing import Optional, Dict, Any
from web3 import Web3
from fake_useragent import UserAgent
from src.utils.proxies import Proxies

# ERC1155 标准的 ABI
nft_abi_erc1155 = [
    {
        "inputs": [
            {"internalType": "address", "name": "account", "type": "address"},
            {"internalType": "uint256", "name": "id", "type": "uint256"},
        ],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    }
]


# ERC721 标准的 ABI
nft_abi_erc721 = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
]


class NFTUtils:
    def __init__(
        self,
        rpc_url: str,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
        timeout: int = 60,
    ):
        """
        初始化 NFT 工具类

        Args:
            rpc_url: RPC节点URL
        """
        self._web3 = None
        self.rpc_url = rpc_url
        self.proxy = proxy
        self.user_agent = user_agent
        self.timeout = timeout

        # 确保初始化 Web3
        if self.web3 is None or not self.web3.is_connected():
            raise Exception(f"无法连接到RPC节点: {rpc_url}")

    def _get_request_kwargs(
        self,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
        timeout: int = 60,
    ) -> Dict[str, Any]:
        """
        设置请求配置

        Args:
            proxy: 代理地址
            user_agent: 自定义User-Agent
            timeout: 超时时间(秒)

        Returns:
            Dict: 请求配置参数
        """
        request_kwargs = {
            "timeout": timeout,
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": user_agent or UserAgent().chrome,
            },
        }

        if proxy:
            try:
                request_kwargs["proxies"] = Proxies(proxy).get_proxies()
            except Exception as e:
                logger.error(f"代理设置失败: {str(e)}")

        return request_kwargs

    @property
    def web3(self):
        if not self._web3:
            request_kwargs = self._get_request_kwargs(self.proxy, self.user_agent)
            web3 = Web3(Web3.HTTPProvider(self.rpc_url, request_kwargs=request_kwargs))
            # 检查连接是否成功
            if web3.is_connected():
                self._web3 = web3
        return self._web3

    def balance_erc721(self, nft_contract_address: str, wallet_address: str) -> int:
        """
        获取ERC721合约的NFT余额
        """
        try:
            checksum_contract = self.web3.to_checksum_address(nft_contract_address)
            checksum_wallet = self.web3.to_checksum_address(wallet_address)

            contract = self.web3.eth.contract(
                address=checksum_contract, abi=nft_abi_erc721
            )
            balance = contract.functions.balanceOf(checksum_wallet).call()
            return balance
        except Exception as e:
            logger.error(f"获取NFT余额失败: {str(e)}, 合约地址: {nft_contract_address}")
            return 0

    def balance_erc1155(
        self, nft_contract_address: str, wallet_address: str, token_id: int
    ) -> int:
        """
        获取ERC1155合约的NFT余额

        Args:
            nft_contract_address: NFT合约地址
            wallet_address: 钱包地址
            token_id: NFT的token_id
        Returns:
            int: NFT持有数量，发生错误时返回0
        """
        try:
            checksum_contract = self.web3.to_checksum_address(nft_contract_address)
            checksum_wallet = self.web3.to_checksum_address(wallet_address)

            # 验证合约是否存在
            code = self.web3.eth.get_code(checksum_contract)
            if code == b"" or code == "0x":
                logger.error(f"地址 {nft_contract_address} 不是有效的合约地址")
                return 0

            contract = self.web3.eth.contract(
                address=checksum_contract, abi=nft_abi_erc1155
            )

            balance = contract.functions.balanceOf(checksum_wallet, token_id).call()
            return balance

        except Exception as e:
            logger.error(f"获取NFT余额失败: {str(e)}, 合约地址: {nft_contract_address}")
            return 0

    def check_nft_type(self, nft_contract_address: str) -> str:
        """
        检查NFT合约类型

        Args:
            nft_contract_address: NFT合约地址

        Returns:
            str: 'ERC721' 或 'ERC1155' 或 'UNKNOWN'
        """
        try:
            # ERC165 接口ID
            ERC165_ID = "0x01ffc9a7"
            # ERC721 接口ID
            ERC721_ID = "0x80ac58cd"
            # ERC1155 接口ID
            ERC1155_ID = "0xd9b67a26"

            checksum_contract = self.web3.to_checksum_address(nft_contract_address)

            # 使用最基础的 supportsInterface ABI
            interface_abi = [
                {
                    "inputs": [
                        {
                            "internalType": "bytes4",
                            "name": "interfaceId",
                            "type": "bytes4",
                        }
                    ],
                    "name": "supportsInterface",
                    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
                    "stateMutability": "view",
                    "type": "function",
                }
            ]

            contract = self.web3.eth.contract(
                address=checksum_contract, abi=interface_abi
            )

            # 首先检查是否支持 ERC165
            try:
                if not contract.functions.supportsInterface(ERC165_ID).call():
                    logger.warning(f"合约不支持 ERC165 接口")
                    return "UNKNOWN"

                # 检查是否为 ERC721
                if contract.functions.supportsInterface(ERC721_ID).call():
                    logger.info(f"合约是 ERC721 标准")
                    return "ERC721"

                # 检查是否为 ERC1155
                if contract.functions.supportsInterface(ERC1155_ID).call():
                    logger.info(f"合约是 ERC1155 标准")
                    return "ERC1155"

                return "UNKNOWN"

            except Exception as e:
                logger.warning(f"检查接口支持失败: {str(e)}")
                return "UNKNOWN"

        except Exception as e:
            logger.error(f"检查NFT类型失败: {str(e)}")
            return "UNKNOWN"
