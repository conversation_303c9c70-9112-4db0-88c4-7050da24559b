import os
import random
from datetime import datetime
from time import sleep

import click
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import Browser<PERSON>ontroller
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import parse_indices
from src.utils.element_util import get_element
from src.utils.hhcsv import HHCSV
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

PROXY_URL = os.getenv("PROXY_URL")


class BlockVision:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def join_discord(self):
        url = "https://discord.gg/blockvision"
        try:
            tab = self.page.new_tab(url)

            # 检查是否登录
            if get_element(tab, "x://div[text()='Already have an account?']", 3):
                logger.warning(f"{self.id} dc已经登出, 请登录dc")
                return False

            # 点击加群按钮
            join = get_element(tab, "x://button[@type='button']", 5)
            if join:
                join.click()
            else:
                logger.error(f"{self.id} 加群失败, 未找到加入按钮")
                return False
            agree_rull = get_element(tab, "x://input[@type='checkbox']", 5)
            print(agree_rull)
            # 点击同意role按钮
            if agree_rull:
                agree_rull.click()
                sleep(1)
                get_element(tab, "x://button[@type='submit']", 5).click()
            # 判断是否跳转到进群页面
            if not tab.wait.url_change("onboarding", timeout=10):
                # 检查是否需要处理验证码
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", 3):
                    logger.warning(f"{self.id} 需要处理验证码，请手动处理")
                    return False
                ele = get_element(tab, "x://div[text()='Continue to Discord']/..", timeout=3)
                if ele:
                    ele.click()
                    sleep(2)

            if tab.wait.url_change("932954288584527932", timeout=10):
                sleep(2)

                if get_element(tab, "monad-faucet", 3):
                    logger.success(f"{self.id} 已经加过群并且验证成功")
                    self.browser_controller.close_page()
                    return True

                # 点击验证
                verify = get_element(tab, "x://div[contains(@class, 'reactionInner')]", 5)
                if verify:
                    verify.click()
                    sleep(2)

                    # 点击加入服务器
                    if get_element(tab, "monad-faucet", 10):
                        logger.success(f"{self.id} 加群并且验证成功")
                        self.browser_controller.close_page()
                        return True

                logger.error(f"{self.id} 加群验证失败")
                return False
            else:
                logger.warning(f"{self.id} 加群失败")
                return False
        except Exception as e:
            logger.error(f"{self.id} 加入 Discord 服务器时发生错误: {str(e)}")
            return False

    def faucet(self):
        pass
        # # 通过页面发送消息，mac端一直失效
        # from DrissionPage.common import Keys
        # lasted_tab = self.page.new_tab("https://discord.com/channels/932954288584527932/1342142136778489876")

        # # 点击领取
        # ele = lasted_tab.ele("No Text Channels", timeout=5)
        # if ele:
        #     logger.warning(f"{self.id} 无法进入频道")
        #     return False

        # # 输入领取
        # text = f'!faucet {evm_address}'
        # lasted_tab.actions.click("@role=textbox").type(text).key_down(Keys.ENTER)


class Faucet(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.channel_id = "1342142136778489876"

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        evm_address = self.browser_config.evm_address
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not evm_address or not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(self.channel_id, f"!faucet {evm_address}"):
            raise Exception("发送faucet消息失败")
        return True


@click.group()
def cli():
    pass


@cli.command("dc")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def join_dc(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                BlockVision(type, index).join_discord()
                return True
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Monad_DC-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("f")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def faucet(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(id):
            try:
                return Faucet(type, id).task()
            except Exception as e:
                logger.error(f"账号 {id} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=120,  # 2分超时
            retries=3,
            interval=10,
            task_name=f"Monad-FAUCET-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    cli()
    # faucet(BrowserType.ADS, "20", 1)
