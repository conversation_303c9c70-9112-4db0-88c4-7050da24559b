from time import sleep, time
from typing import Optional
import re

from DrissionPage import Chromium
from loguru import logger
from .base import BaseWebMailClient
from src.utils.element_util import get_element


class MixMailWebClient(BaseWebMailClient):

    def __init__(self, page: Chromium, id: str):
        super().__init__(page, id)
        self._login_url = "https://firstmail.ltd/ru-RU/webmail/login"

    def login(self, email: str, password: str) -> None:
        """登录邮箱"""
        tab = self.page.new_tab(self._login_url)
        sleep(5)
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:

            if not tab.wait.ele_displayed("x://input[@id='email']", timeout=10):
                logger.error(
                    f"【{self.id}】 打开{self._login_url}失败，请检查网络是否正常"
                )
                return False

            try:
                email_i = get_element(tab, "x://input[@id='email']", 3)
                email_i.clear(True)
                email_i.input(email)
                sleep(3)
            except Exception as e:
                logger.warning(f"【{self.id}】 登录出错，未找到邮箱输入框，retry...")

            try:
                password_i = get_element(tab, "x://input[@id='password']", 3)
                password_i.clear(True)
                password_i.input(password)
                sleep(2)
            except Exception as e:
                logger.warning(f"【{self.id}】 登录出错，未找到密码输入框，retry...")

            try:
                get_element(
                    tab,
                    "x://button[@class='form-control input-captcha cursor-pointer']",
                    timeout=5,
                ).click()
            except Exception as e:
                logger.warning(f"【{self.id}】 登录出错，未找到验证按钮，retry...")

            try:
                if tab.wait.ele_displayed(
                    "x://button[@class='form-control input-captcha-success disabled cursor-default']",
                    timeout=10,
                ):
                    get_element(tab, "x://button[@type='submit']", timeout=3).click()
            except Exception as e:
                logger.warning(f"【{self.id}】 登录出错，未找到验证按钮，retry...")

            if tab.wait.ele_displayed(
                "x://div[@class='btn-compost-wrapper row']", timeout=15
            ):
                logger.success(f"【{self.id}】 登录成功")
                tab.close()
                return True

            logger.warning(f"【{self.id}】 第 {retry_count} 次登录失败，retry...")
            retry_count += 1

            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

        return False

    def logout(self) -> None:
        """登出邮箱"""
        pass

    def update_password(self, password: str, new_password: str) -> bool:
        """更新密码
        Args:
            password: 当前密码
            new_password: 新密码

        Returns:
            bool: 更新是否成功

        Raises:
            MailAuthError: 认证失败
            MailConnectionError: 连接失败
        """
        pass

    def update_bind_email(self, email: str) -> bool:
        """更新绑定邮箱

        Args:
            email: 新的绑定邮箱

        Returns:
            bool: 更新是否成功

        Raises:
            MailAuthError: 认证失败
            MailConnectionError: 连接失败
        """
        pass

    @property
    def home_url(self) -> str:
        """邮箱首页URL"""
        return "https://firstmail.ltd/ru-RU/webmail/inbox"

    def get_discord_email_verification_code(self) -> Optional[str]:
        tab = self.page.new_tab(self.home_url)
        if not tab.wait.ele_displayed(
            "x://div[@class='btn-compost-wrapper row']", timeout=15
        ):
            logger.error(
                f"【{self.id}】 打开{self._home_url}失败，请检查账号密码是否正确或网络是否正常"
            )
            return None

        start_time = time()
        timeout = 30
        code = None
        while time() - start_time <= timeout:
            span = get_element(
                tab,
                "x://span[contains(text(), 'Your Discord email verification code is')]",
                5,
            )
            if not span:
                tab.refresh()
                tab.reconnect(3)
                continue
            pattern = r"Your Discord email verification code is ([A-Za-z0-9]{6})"
            match = re.search(pattern, span.text)
            if match:
                code = match.group(1)
                logger.success(f"【{self.id}】 找到验证码{code}")
                tab.close()
                return code
        return code
