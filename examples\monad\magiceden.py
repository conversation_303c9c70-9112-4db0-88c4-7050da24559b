import os
import random
from time import sleep

import click
from loguru import logger
from web3 import Web3

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.common import parse_indices
from src.browsers.operations import try_click
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/emmy.log", rotation="10MB", level="SUCCESS")

MONAD_RPC = os.getenv("MONAD_RPC")

ERC721_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    }
]

ERC1155_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "account", "type": "address"},
            {"internalType": "uint256", "name": "id", "type": "uint256"},
        ],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    }
]


def w3_get_balance(
    address: str, rpc: str = MONAD_RPC, token_contract: str = None
) -> float:
    """
    查询 Monad 链上某个地址的余额（原生代币或 ERC-20 代币）

    :param address: 需要查询的地址
    :param rpc: Monad RPC URL
    :param token_contract: 代币合约地址（可选，不传则查询原生代币）
    :return: 余额（十进制格式）
    """
    web3 = Web3(Web3.HTTPProvider(rpc))

    if not web3.is_connected():
        raise ConnectionError("无法连接到 Monad RPC，请检查 RPC URL")

    if token_contract:
        # 查询 ERC-20 代币余额
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function",
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function",
            },
        ]
        contract = web3.eth.contract(
            address=web3.to_checksum_address(token_contract), abi=erc20_abi
        )

        # 获取代币余额
        balance_wei = contract.functions.balanceOf(
            web3.to_checksum_address(address)
        ).call()
        # 获取代币小数位数
        decimals = contract.functions.decimals().call()
        # 转换成十进制
        balance = balance_wei / (10**decimals)
    else:
        # 查询原生代币余额
        balance_wei = web3.eth.get_balance(web3.to_checksum_address(address))
        balance = web3.from_wei(balance_wei, "ether")
    return float(balance)


def get_nft_balance(
    address: str, token_contract: str, token_type: str, token_id: str = None
):
    web3 = Web3(Web3.HTTPProvider(MONAD_RPC))

    if not web3.is_connected():
        raise ConnectionError("无法连接到 Monad RPC，请检查 RPC URL")

    if not address or not token_contract or not token_type:
        logger.error("地址、合约地址和代币类型不能为空")
        return 0

    if token_type == "ERC1155" and token_id is None:
        logger.error("ERC1155 代币必须提供 token_id")
        return 0

    try:
        balance = None
        if token_type == "ERC1155":
            contract = web3.eth.contract(
                address=web3.to_checksum_address(token_contract), abi=ERC1155_ABI
            )
            balance = contract.functions.balanceOf(
                web3.to_checksum_address(address), int(token_id)
            ).call()
        elif token_type == "ERC721":
            contract = web3.eth.contract(
                address=web3.to_checksum_address(token_contract), abi=ERC721_ABI
            )
            balance = contract.functions.balanceOf(
                web3.to_checksum_address(address)
            ).call()
        else:
            logger.error(f"不支持的代币类型: {token_type}")
            return 0

        return balance
    except Exception as e:
        logger.error(f"获取 NFT 余额失败: {str(e)}")
        return 0


class Magiceden:

    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.url = "https://magiceden.io/monad-testnet"

    def _is_login(self, lasted_tab):
        return (
            True
            if get_element(lasted_tab, "x://div[@data-test-id='wallet-balance']", 5)
            else False
        )

    def _connect_wallet(self, lasted_tab):
        try:
            dialog = get_element(lasted_tab, "x://*[@data-test-id='modal-close']", 5)
            if dialog:
                try_click(lasted_tab, "x://*[@data-test-id='modal-close']")

            try_count = 5
            for i in range(try_count):
                logger.info(f"【{self.id}】钱包进行第 {i + 1}/{try_count}次连接")

                if self._is_login(lasted_tab):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

                connect_btn = get_element(
                    lasted_tab, "x://button[@data-test-id='wallet-connect-button']", 5
                )
                if connect_btn:
                    connect_btn.click()
                    sleep(2)

                dynamic_modal = lasted_tab("@id=dynamic-modal").child(1).sr("t:div")
                view_all = dynamic_modal.ele(
                    "x://button[@data-testid='ListTile']//following-sibling::button"
                )
                if view_all:
                    view_all.click()
                    sleep(2)

                okx_wallet = dynamic_modal.ele(
                    "x://img[@data-testid='wallet-icon-okxwallet']/parent::button"
                )
                if okx_wallet:
                    okx_wallet.click()
                    sleep(2)

                lasted_tab.listen.start("api-mainnet.magiceden.io/v4/fungible/balance")
                self.browser_controller.okx_wallet_connect()
                res = lasted_tab.listen.wait(timeout=90)
                if not res or res.response.status not in [200, 201, 204]:
                    logger.error(f"【{self.id}】网络异常，链接钱包失败")
                    return False

                if self._is_login(lasted_tab):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

                sleep(1)
            logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】钱包连接失败,error={str(e)}")
            return False

    def _login(self):
        try:
            self.browser_controller.window_max()
            if not self.browser_controller.okx_wallet_login():
                logger.error(f"【{self.id}】OKX钱包登录失败")
                return False

            self.browser_controller.open_url(self.url)
            sleep(8)

            if not self.browser_controller.okx_wallet_change_connect_site(
                "Monad Testnet"
            ):
                logger.error(f"【{self.id}】切换站点失败")
                return False

            return self._connect_wallet(self.page.latest_tab)
        except Exception as e:
            logger.error(f"【{self.id}】登录失败，error={str(e)}")
            return False

    def _parse_price(self, price_text: str) -> float:
        """
        解析价格文本，提取数值
        例如: '8KMON\n1.9%' -> 8000
              '0.0001MON' -> 0.0001
              '56MON' -> 56
              '8.5KMON\n1.9%' -> 8500
        """
        try:
            # 移除换行符和MON后缀
            price = (
                price_text.split("\n")[0].replace("MON", "").replace("<", "").strip()
            )

            # 处理包含K的情况
            if "K" in price.upper():
                price = float(price.upper().replace("K", "")) * 1000
            else:
                price = float(price)

            return price
        except Exception as e:
            logger.error(f"【{self.id}】解析价格失败: {price_text}, error={str(e)}")
            return 0

    def _find_lowest_price(self, lasted_tab, times) -> tuple[bool, float]:
        """
        查找当前最低价格的NFT
        返回: (成功标志, 价格)
        """
        try:
            for i in range(times):
                logger.info(f"【{self.id}】第 {i + 1}/{times} 次尝试获取低价NFT")
                get_element(lasted_tab, "x:(//thead//th)[3]", 5).click()
                sleep(2)
                get_element(lasted_tab, "x:(//thead//th)[3]", 5).click()
                sleep(2)

                price_text = get_element(lasted_tab, "x:(//tbody//td)[3]", 5)
                if not price_text:
                    continue

                price = self._parse_price(price_text.text)
                if price <= 0.001:
                    logger.info(f"【{self.id}】当前最低价格: {price} MON")
                    return True, price
            logger.warning(f"【{self.id}尝试{times}次以后仍未找到低于0.001MON的NFT】")
            return False, 0
        except Exception as e:
            logger.error(f"【{self.id}】查找最低价格失败: {str(e)}")
            return False, 0

    def _buy(self):
        lasted_tab = self.page.latest_tab
        address = self.browser_controller.browser_config.evm_address
        success, price = self._find_lowest_price(lasted_tab, 4)
        if not success:
            logger.warning(f"【{self.id}】未找到价格低于0.01 MON的NFT")
            return False

        get_element(lasted_tab, "x:(//tbody//td)[2]", 5).click()
        sleep(5)

        get_element(
            lasted_tab, "x://div[@data-testid='virtuoso-item-list']/div[2]", 5
        ).click()
        sleep(10)

        contract = lasted_tab.url.split("/")[-1]
        price = get_element(
            lasted_tab, "x://div[text()='Best Price']/following-sibling::div[1]", 10
        ).text
        token_id = get_element(
            lasted_tab, "x://div[text()='Token ID']/following-sibling::div", 10
        ).text
        token_type = get_element(
            lasted_tab, "x://div[text()='Token Standard']/following-sibling::div", 10
        ).text
        token_type = token_type.replace("-", "").strip()

        if token_type == "ERC1155":
            balance = get_nft_balance(address, contract, token_type, token_id)
            if balance > 0:
                logger.warning(f"【{self.id}】已拥有该 NFT，跳过购买")
                return True
        elif token_type == "ERC721":
            balance = get_nft_balance(address, contract, token_type)
            if balance > 0:
                logger.warning(f"【{self.id}】已拥有该 NFT，跳过购买")
                return True

        buy_btn = get_element(
            lasted_tab,
            "x://div[@class='tw-w-full' and @data-state='closed']/button",
            10,
        )
        if not buy_btn:
            logger.warning(f"【{self.id}】购买按钮未找到，请检查网络是否正常")
            return False
        if "MON" not in buy_btn.text:
            logger.warning(f"【{self.id}】请切换到Monad Test网络")
            return False

        buy_btn.click()
        sleep(2)
        lasted_tab.listen.start(
            "api-mainnet.magiceden.io/v3/rtp/monad-testnet/execute/status/v1"
        )
        self.browser_controller.okx_wallet_connect()
        for packet in lasted_tab.listen.steps(count=3, timeout=90):
            if (
                packet
                and packet.response.body
                and packet.response.body["status"] == "success"
            ):
                logger.success(f"【{self.id}】购买NFT {contract} {price} 成功")
                return True
        return False

    def _sell(self):
        pass

    def task(self):
        try:
            evm_address = self.browser_controller.browser_config.evm_address
            balance_evm = w3_get_balance(evm_address)
            if balance_evm < 0.05:
                logger.info(f"{evm_address} 余额不足0.05 MON")
                return True

            if not self._login():
                return False

            self._buy()

            # self._sell()

            return True
        except Exception as e:
            logger.error(e)
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(e)


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def register(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(id):
            try:
                magiceden = Magiceden(type, str(id))
                return magiceden.task()
            except Exception as e:
                logger.error(f"账号 {id} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"MagicEden-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# python3 examples/monad/magiceden.py r -t ads -i 1-10
if __name__ == "__main__":
    cli()
    # magiceden = Magiceden(BrowserType.ADS, "5")
    # magiceden.task()
