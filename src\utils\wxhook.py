import json
import os
import urllib.parse

import requests
from dotenv import load_dotenv

load_dotenv()


class WebhookUtil:
    def __init__(self):
        self.webhook_url = os.getenv("webhook_url")
        self.corpid = os.getenv("corpid")  # 企业微信企业ID
        self.corpsecret = os.getenv("corpsecret")  # 企业微信应用Secret
        self.agentid = os.getenv("agentid")  # 企业微信应用AgentID

        # 验证环境变量是否正确配置
        if not all([self.corpid, self.corpsecret, self.agentid]):
            raise ValueError("企业微信相关的环境变量未正确配置 (corpid, corpsecret, agentid)。")

    def send_text_message_to_webhook(self, content, mentioned_list=None):
        """
        Sends a text message to the webhook.

        :param content: The content of the text message
        :param mentioned_list: List of users to mention (e.g., ["UserID1", "UserID2"])
        :return: Response JSON
        """
        if mentioned_list is None:
            mentioned_list = []  # 默认不提及任何人

        payload = {
            "msgtype": "text",
            "text": {
                "content": content,
                "mentioned_list": mentioned_list,
            },
        }
        headers = {"Content-Type": "application/json"}

        response = requests.post(self.webhook_url, data=json.dumps(payload), headers=headers)

        if response.status_code != 200:
            raise Exception(f"Failed to send message, status code: {response.status_code}, response: {response.text}")

        return response.json()

    def send_text_message_to_user(self, content, touser):
        """
        Sends a text message to a specific user via 企业微信 API.

        :param content: The content of the text message
        :param touser: User ID of the recipient
        :return: Response JSON
        """
        # Step 1: 获取 access-token
        base_url = "https://qyapi.weixin.qq.com"
        access_token_api = urllib.parse.urljoin(base_url, "/cgi-bin/gettoken")
        params = {"corpid": self.corpid, "corpsecret": self.corpsecret}

        # 获取 access-token
        response = requests.get(url=access_token_api, params=params).json()
        if response.get("errcode") != 0:
            raise Exception(f"Failed to get access token: {response}")

        access_token = response["access_token"]

        # Step 2: 构造发送消息的请求
        message_send_api = urllib.parse.urljoin(base_url, f"/cgi-bin/message/send?access_token={access_token}")
        data = {
            "touser": touser,
            "msgtype": "text",
            "agentid": self.agentid,
            "text": {"content": content},
        }

        # 发送消息
        response = requests.post(url=message_send_api, data=json.dumps(data)).json()

        if response.get("errcode") != 0:
            raise Exception(f"Failed to send message: {response}")

        return response


# Example usage:
if __name__ == "__main__":
    webhook = WebhookUtil()

    # Example 1: 使用 Webhook @用户
    try:
        result = webhook.send_text_message_to_webhook("这是一个测试消息", mentioned_list=["UserID1", "UserID2"])
        print("Webhook 发送结果:", result)
    except Exception as e:
        print("Webhook 发送失败:", e)

    # Example 2: 使用 企业微信 API 发送消息给指定用户
    try:
        result = webhook.send_text_message_to_user("测试数据：hello world!", "YeHongHao")
        print("企业微信 API 发送结果:", result)
    except Exception as e:
        print("企业微信 API 发送失败:", e)
