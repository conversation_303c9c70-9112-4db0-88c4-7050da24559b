import os
import time
from src.utils.common import get_project_root_path
from threading import Lock
from dotenv import load_dotenv
from loguru import logger
from w3_manager import Web3Manager
from bigint_quest_minter import BigintQuestMinter

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BrowserType
from src.controllers import Browser<PERSON>ontroller
from src.utils.data_utils import DataUtil
from src.utils.proxies import Proxies
from src.utils.secure_encryption import SecureEncryption

load_dotenv()

# 环境变量配置
SOMNIA_RPC = os.getenv("SOMNIA_RPC", "https://dream-rpc.somnia.network")
PROXY_URL = os.getenv("PROXY_URL")

# Somnia 链合约地址配置
PART1_CONTRACT = "0x0cB6931251e1eC264Ce8611A918950BEa9c750E7"  # Part 1: Transaction Maker
PART2_CONTRACT = "0xC09ae6287d05Fe2302Ca50E417990B91cd9e4ed1"  # Part 2: Early Mover

# 链配置
SOMNIA_CHAIN_ID = 50312

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/bigint_quest.log", rotation="10MB", level="SUCCESS")


class BigintQuestExample:
    """Bigint Quest NFT Mint 示例类

    基于现有的 Quest 类模式，集成 Web3Manager 和 BigintQuestMinter
    支持 Part 1 (Transaction Maker) 和 Part 2 (Early Mover)
    """

    _file_lock = Lock()

    def __init__(self, browser_controller: BrowserController):
        """初始化

        Args:
            browser_controller: 浏览器控制器
            data: 数据工具
        """
        self.id = browser_controller.id
        self.browser_controller = browser_controller
        self.address = self.browser_controller.browser_config.evm_address
        self.evm_private_key = self.get_private_key()
        self._setting_proxy()
        self.user_agent = self.browser_controller.browser_config.user_agent

        # 初始化 Web3Manager
        self.w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

        # 初始化 BigintQuestMinter - Part 1 (Transaction Maker)
        self.quest_minter_part1 = BigintQuestMinter(
            contract_address=PART1_CONTRACT,
            rpc_url=SOMNIA_RPC,
            private_key=self.evm_private_key,
            proxy=self.proxy,
            user_agent=self.user_agent,
        )

        # 初始化 BigintQuestMinter - Part 2 (Early Mover)
        self.quest_minter_part2 = BigintQuestMinter(
            contract_address=PART2_CONTRACT,
            rpc_url=SOMNIA_RPC,
            private_key=self.evm_private_key,
            proxy=self.proxy,
            user_agent=self.user_agent,
        )

        logger.info(f"【{self.id}】BigintQuestExample 初始化完成")
        logger.info(f"【{self.id}】钱包地址: {self.address}")
        logger.info(f"【{self.id}】Part 1 合约地址: {PART1_CONTRACT}")
        logger.info(f"【{self.id}】Part 2 合约地址: {PART2_CONTRACT}")

    def _setting_proxy(self):
        """设置代理"""
        proxies = Proxies(self.browser_controller.browser_config.proxy)
        if proxies.verify():
            self.proxy = self.browser_controller.browser_config.proxy
        else:
            self.proxy = PROXY_URL
        logger.info(f"【{self.id}】使用代理: {self.proxy}")

    def get_private_key(self) -> str | None:
        """获取私钥"""
        pk = self.browser_controller.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def check_balance(self) -> float:
        """检查钱包余额"""
        try:
            balance = self.quest_minter_part1.get_balance()
            logger.info(f"【{self.id}】当前余额: {balance} ETH")
            return balance
        except Exception as e:
            logger.error(f"【{self.id}】检查余额失败: {str(e)}")
            return 0.0

    def check_mint_status_part1(self) -> dict:
        """检查 Part 1 所有类型的 mint 状态"""
        try:
            status = self.quest_minter_part1.check_all_mint_status(self.address)
            logger.info(f"【{self.id}】Part 1 当前 mint 状态: {status}")
            return status
        except Exception as e:
            logger.error(f"【{self.id}】检查 Part 1 mint 状态失败: {str(e)}")
            return {}

    def check_mint_status_part2(self) -> dict:
        """检查 Part 2 所有类型的 mint 状态"""
        try:
            status = self.quest_minter_part2.check_all_mint_status(self.address)
            logger.info(f"【{self.id}】Part 2 当前 mint 状态: {status}")
            return status
        except Exception as e:
            logger.error(f"【{self.id}】检查 Part 2 mint 状态失败: {str(e)}")
            return {}

    def check_all_mint_status(self) -> dict:
        """检查所有 Part 的 mint 状态"""
        try:
            part1_status = self.check_mint_status_part1()
            part2_status = self.check_mint_status_part2()

            all_status = {"Part1": part1_status, "Part2": part2_status}

            logger.info(f"【{self.id}】所有 mint 状态: {all_status}")
            return all_status
        except Exception as e:
            logger.error(f"【{self.id}】检查所有 mint 状态失败: {str(e)}")
            return {}

    def mint_single_type(self, part: int, mint_type: int, batch_size: int = 1, value_in_wei: int = 0) -> bool:
        """Mint 单个类型的 NFT

        Args:
            part: Part 编号 (1 或 2)
            mint_type: mint 类型 (1-4)
            batch_size: 批量大小
            value_in_wei: 支付金额 (wei)

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"【{self.id}】开始 mint Part {part} Type {mint_type}")

            # 检查余额
            balance = self.check_balance()
            if balance < 0.001:  # 至少需要 0.001 ETH 作为 gas
                logger.error(f"【{self.id}】余额不足，无法进行 mint")
                return False

            # 选择对应的 minter
            if part == 1:
                minter = self.quest_minter_part1
            elif part == 2:
                minter = self.quest_minter_part2
            else:
                logger.error(f"【{self.id}】无效的 Part: {part}")
                return False

            # 根据类型调用对应的 mint 方法
            mint_methods = {1: minter.mint_type1, 2: minter.mint_type2, 3: minter.mint_type3, 4: minter.mint_type4}

            if mint_type not in mint_methods:
                logger.error(f"【{self.id}】无效的 mint 类型: {mint_type}")
                return False

            success = mint_methods[mint_type](batch_size, value_in_wei)

            if success:
                logger.success(f"【{self.id}】Part {part} Type {mint_type} mint 成功")
            else:
                logger.error(f"【{self.id}】Part {part} Type {mint_type} mint 失败")

            return success

        except Exception as e:
            logger.error(f"【{self.id}】mint Part {part} Type {mint_type} 异常: {str(e)}")
            return False

    def mint_part_all_types(
        self, part: int, batch_size: int = 1, value_per_mint: int = 0, delay_between_mints: float = 3.0
    ) -> dict:
        """Mint 指定 Part 的所有类型 NFT

        Args:
            part: Part 编号 (1 或 2)
            batch_size: 每种类型的批量大小
            value_per_mint: 每次 mint 的支付金额 (wei)
            delay_between_mints: mint 之间的延迟时间 (秒)

        Returns:
            dict: mint 结果
        """
        try:
            logger.info(f"【{self.id}】开始批量 mint Part {part} 所有类型")

            # 检查余额
            balance = self.check_balance()
            if balance < 0.01:  # 至少需要 0.01 ETH
                logger.error(f"【{self.id}】余额不足，无法进行批量 mint")
                return {}

            # 选择对应的 minter
            if part == 1:
                minter = self.quest_minter_part1
            elif part == 2:
                minter = self.quest_minter_part2
            else:
                logger.error(f"【{self.id}】无效的 Part: {part}")
                return {}

            results = minter.mint_all_types(
                batch_size=batch_size, value_per_mint=value_per_mint, delay_between_mints=delay_between_mints
            )

            logger.info(f"【{self.id}】Part {part} 批量 mint 完成，结果: {results}")
            return results

        except Exception as e:
            logger.error(f"【{self.id}】Part {part} 批量 mint 异常: {str(e)}")
            return {}

    def smart_mint_part(
        self, part: int, batch_size: int = 1, value_per_mint: int = 0, delay_between_mints: float = 3.0
    ) -> dict:
        """智能 mint 指定 Part - 只 mint 未 mint 的类型

        Args:
            part: Part 编号 (1 或 2)
            batch_size: 批量大小
            value_per_mint: 每次 mint 的支付金额 (wei)
            delay_between_mints: mint 之间的延迟时间 (秒)

        Returns:
            dict: mint 结果
        """
        try:
            logger.info(f"【{self.id}】开始智能 mint Part {part}")

            # 检查余额
            balance = self.check_balance()
            if balance < 0.001:
                logger.error(f"【{self.id}】余额不足，无法进行 mint")
                return {}

            # 选择对应的 minter
            if part == 1:
                minter = self.quest_minter_part1
                current_status = self.check_mint_status_part1()
            elif part == 2:
                minter = self.quest_minter_part2
                current_status = self.check_mint_status_part2()
            else:
                logger.error(f"【{self.id}】无效的 Part: {part}")
                return {}

            # 检查是否所有类型都已经 mint 过
            if current_status and all(current_status.values()):
                logger.info(f"【{self.id}】Part {part} 所有类型已 mint 完成，无需再次 mint")
                return current_status

            results = minter.mint_unminted_types(
                user_address=self.address,
                batch_size=batch_size,
                value_per_mint=value_per_mint,
                delay_between_mints=delay_between_mints,
            )

            logger.success(f"【{self.id}】Part {part} 智能 mint 完成，结果: {results}")
            return results

        except Exception as e:
            logger.error(f"【{self.id}】Part {part} 智能 mint 异常: {str(e)}")
            return {}

    def run_quest(self, parts: list = [1, 2]) -> dict:
        """执行完整的 quest 流程

        Args:
            parts: 要执行的 Part 列表，默认 [1, 2]

        Returns:
            dict: 执行结果
        """
        try:
            logger.info(f"【{self.id}】开始执行 Bigint Quest，Parts: {parts}")

            # 1. 检查余额
            balance = self.check_balance()
            if balance < 0.001:
                logger.error(f"【{self.id}】余额不足，跳过执行")
                return {}

            # 2. 对每个 Part 执行智能 mint（smart_mint_part 内部会自动检查状态）
            results = {}
            for part in parts:
                if part in [1, 2]:
                    part_name = f"Part{part}"

                    logger.info(f"【{self.id}】开始执行 {part_name}")
                    part_results = self.smart_mint_part(
                        part=part, batch_size=1, value_per_mint=0, delay_between_mints=3.0
                    )
                    results[part_name] = part_results

                    # Part 之间添加延迟
                    if part != parts[-1]:
                        logger.info(f"【{self.id}】等待 5 秒后执行下一个 Part...")
                        time.sleep(5.0)
                else:
                    logger.warning(f"【{self.id}】跳过无效的 Part: {part}")

            # 4. 统计结果
            total_success = 0
            total_count = 0
            for part_name, part_results in results.items():
                success_count = sum(1 for result in part_results.values() if result)
                total_success += success_count
                total_count += len(part_results)
                logger.info(f"【{self.id}】{part_name} 成功 {success_count}/{len(part_results)}")

            logger.info(f"【{self.id}】Quest 执行完成，总计成功 {total_success}/{total_count}")

            return results

        except Exception as e:
            logger.error(f"【{self.id}】执行 Quest 异常: {str(e)}")
            return {}


def run_bigint_quest(controller, index):
    """运行"""
    try:
        quest_example = BigintQuestExample(controller)

        # 执行完整流程（两个 Part）
        results = quest_example.run_quest()

        # 或者只执行某个 Part
        # results = quest_example.run_quest(parts=[1])  # 只执行 Part 1

        # 或者单独操作
        # quest_example.check_balance()
        # quest_example.check_all_mint_status()
        # quest_example.smart_mint_part(part=1)
        # quest_example.mint_single_type(part=1, mint_type=1)

        logger.info(f"{index} 执行完成，结果: {results}")
        return results

    except Exception as e:
        logger.error(f"示例执行失败: {str(e)}")


# 使用示例
def example_usage():
    """使用示例"""
    try:
        # 这里需要根据实际情况创建 browser_controller 和 data

        type = BrowserType.CHROME
        index = "1"
        controller = BrowserController(type, index)
        quest_example = BigintQuestExample(controller)

        # 执行完整流程（两个 Part）
        results = quest_example.run_quest()

        # 或者只执行某个 Part
        # results = quest_example.run_quest(parts=[1])  # 只执行 Part 1

        # 或者单独操作
        # quest_example.check_balance()
        # quest_example.check_all_mint_status()
        # quest_example.smart_mint_part(part=1)
        # quest_example.mint_single_type(part=1, mint_type=1)

        logger.info(f"Quest 执行完成，结果: {results}")

    except Exception as e:
        logger.error(f"示例执行失败: {str(e)}")


def test_status_check():
    """测试状态检查逻辑"""
    try:
        from src.utils.common import get_project_root_path

        _type = BrowserType.CHROME
        index = "1"
        controller = BrowserController(_type, index)
        quest_example = BigintQuestExample(controller)

        # 只检查状态，不执行 mint
        logger.info("=== 检查当前状态 ===")
        balance = quest_example.check_balance()
        all_status = quest_example.check_all_mint_status()

        # 分析状态
        for part_name, part_status in all_status.items():
            if part_status and all(part_status.values()):
                logger.info(f"{part_name} 所有类型已完成 mint: {part_status}")
            else:
                logger.info(f"{part_name} 还有未完成的 mint: {part_status}")

    except Exception as e:
        logger.error(f"状态检查失败: {str(e)}")


if __name__ == "__main__":
    # 可以选择运行哪个函数
    example_usage()  # 完整执行
    # test_status_check()  # 只检查状态
