from typing import Optional, Dict
from web3 import Web3
from requests import Request, Session


class ERC721:

    # ERC721 标准 ABI - 只包含 balanceOf 方法
    ABI = [
        {
            "constant": True,
            "inputs": [{"name": "owner", "type": "address"}],
            "name": "balanceOf",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function",
        }
    ]

    def __init__(
        self,
        contract_address: str,
        provider_url: str,
        abi: Optional[list] = None,
        proxy: Optional[Dict[str, str]] = None,
    ):
        """
        初始化 ERC721 合约实例

        Args:
            contract_address: 合约地址
            provider_url: RPC节点URL
            abi: 可选的自定义 ABI
            proxy: 代理配置,格式如 {"http": "http://host:port", "https": "https://host:port"}
        """
        if proxy:
            # 使用代理创建session
            session = Session()
            session.proxies = proxy
            provider = Web3.HTTPProvider(provider_url, session=session)
        else:
            provider = Web3.HTTPProvider(provider_url)

        self.web3 = Web3(provider)
        self.contract_address = Web3.to_checksum_address(contract_address)
        self.contract = self.web3.eth.contract(
            address=self.contract_address, abi=abi if abi else self.ABI
        )

    def balance_of(self, address: str) -> int:
        address = Web3.to_checksum_address(address)
        return self.contract.functions.balanceOf(address).call()
