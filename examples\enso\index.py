import contextvars
import logging
import os
import random
import sys
import time

import click
import requests
from config_enso import (
    DEFAULT_BROWSER_TYPE,
    LOG_DIR,
    LOG_FILE,
    LOG_LEVEL,
    LOG_ROTATION,
    TASK_INTERVAL,
    TASK_RETRIES,
    TASK_TIMEOUT,
)
from data_manager import DataManager
from enso_task import EnsoTask
from loguru import logger

from src.browsers import BROWSER_TYPES
from src.enums.browsers_enums import BrowserType
from src.utils.common import parse_indices
from src.utils.thread_executor import ThreadExecutor
from utils import measure_time

# 定义上下文变量存储 TaskID
current_task_id = contextvars.ContextVar("task_id", default="main")


# 自定义日志格式（安全处理 TaskID）
def format_log(record):
    task_id = current_task_id.get()  # 从上下文变量获取动态值
    return (
        f"<green>{record['time']:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        f"<level>{record['level']: <8}</level> | "
        f"<cyan>{task_id}</cyan> | "
        f"{record['message']}\n"
    )


# 拦截标准库日志并转发到 Loguru（关键步骤！）
class InterceptHandler(logging.Handler):
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        logger.opt(depth=6, exception=record.exc_info).log(level, record.getMessage())


# 配置日志处理器
def configure_logger():
    # 移除默认配置
    logger.remove()

    # 拦截标准库日志（如第三方库）
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # 控制台输出（带颜色）
    logger.add(sys.stdout, enqueue=True, colorize=True, format=format_log, level="INFO")

    # 文件输出（带轮转）
    logger.add(
        "app.log",
        rotation="500 MB",
        retention="7 days",
        compression="zip",
        format=format_log,
        level="INFO",
        enqueue=True,
    )


# 初始化日志配置
configure_logger()

# logger.add(LOG_FILE, rotation=LOG_ROTATION, level=LOG_LEVEL)

# 配置日志
# logger.add(LOG_FILE, rotation=LOG_ROTATION, level=LOG_LEVEL)


def run_task(type: str, index: str) -> bool:
    """运行单个任务

    Args:
        type: 浏览器类型
        index: 任务索引

    Returns
    -------
        bool: 是否执行成功
    """
    try:
        enso_task = EnsoTask(type, str(index))
        if not check_and_update_proxy_by_id(type, index):
            enso_task.logger.error("网络问题，请检查")
            return False
        # 先注册
        if enso_task.register(type):
            # 注册成功后执行任务
            return enso_task.execute_tasks()
        else:
            enso_task.logger.error("注册失败")
            return False
    except Exception as e:
        enso_task.logger.error(f"执行任务异常: {e}")
        return False


def test_proxy(proxy: str) -> bool:
    proxy = proxy.replace("socks5", "http")
    try:
        response = requests.get(
            "https://www.okx.com/",
            proxies={"http": proxy, "https": proxy},
            timeout=10,
        )

        return response.status_code == 200
    except:
        return False


def check_and_update_proxy_by_id(type: str, id: str) -> None:
    """
    根据ID检查代理是否可用，如果不可用则更新代理

    Args:
        id: 配置ID
    """
    from src.browsers.config import get_browser_data_path

    path = get_browser_data_path(type)
    filename = os.path.basename(path)

    data_manager = DataManager(project_name=filename, base_path="data")
    configs = data_manager.get_by_id(str(id))

    if configs and not test_proxy(proxy=configs["proxy"]):
        print("代理不可用，已更新")
        return False
    return True


@click.group()
def cli():
    """命令行工具"""
    pass


@cli.command("run")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=DEFAULT_BROWSER_TYPE, help="浏览器类型")
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type: str, index: str, workers: int):
    """运行任务

    Args:
        type: 浏览器类型
        index: 浏览器序号
        workers: 并发数量
    """
    try:
        # 解析索引
        indices = parse_indices(index)
        random.shuffle(indices)

        # 记录成功和失败的索引
        successful_indices = []
        failed_indices = []
        failed_proxy_ids = []
        failed_discord_ids = []  # 记录绑定失败的Discord账号
        failed_twitter_ids = []  # 记录绑定失败的Twitter账号
        enso_tasks = {}  # 存储所有任务管理器实例
        active_tasks = set()  # 记录正在执行的任务

        @measure_time
        def process_task(index: int) -> bool:
            """处理单个任务

            Args:
                index: 任务索引

            Returns
            -------
                bool: 是否成功
            """
            enso_task = None
            try:
                active_tasks.add(index)
                enso_task = EnsoTask(type, str(index))
                enso_tasks[index] = enso_task  # 保存任务管理器实例
                if not check_and_update_proxy_by_id(type, index):
                    enso_task.logger.error("网络问题，请检查")
                    failed_proxy_ids.append(index)
                    return False
                # 先执行注册
                if not enso_task.register(type):
                    enso_task.logger.error("注册失败")
                    return False
                result = enso_task.execute_tasks()
                if result:
                    successful_indices.append(index)
                else:
                    failed_indices.append(index)
                    # 记录绑定失败的账号
                    if enso_task.discord_bind_failed:
                        failed_discord_ids.append(index)
                    if enso_task.twitter_bind_failed:
                        failed_twitter_ids.append(index)
                return result
            except Exception as e:
                if enso_task:
                    enso_task.logger.error(f"处理任务失败: {str(e)}")
                else:
                    logger.error(f"处理任务 {index} 失败: {str(e)}")
                failed_indices.append(index)
                return False
            finally:
                active_tasks.remove(index)
                # 确保资源被释放
                if enso_task:
                    try:
                        enso_task.browser_controller.close_page()
                    except Exception as e:
                        enso_task.logger.debug(f"关闭浏览器失败: {str(e)}")

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=TASK_TIMEOUT,
            retries=TASK_RETRIES,
            interval=TASK_INTERVAL,
            task_name=f"enso-{type}",
            raise_exception=False,
        )

        try:
            # 批量执行任务
            executor.run_batch(process_task, indices)
        except Exception as e:
            logger.error(f"执行任务过程出错: {e}")
        finally:
            # 等待所有活动任务完成或超时
            start_time = time.time()
            while active_tasks and time.time() - start_time < 30:  # 最多等待30秒
                time.sleep(1)

            # 强制关闭所有未完成的任务
            for index in list(active_tasks):
                logger.warning(f"强制关闭任务 {index}")
                if index in enso_tasks:
                    try:
                        enso_tasks[index].browser_controller.close_page()
                    except Exception as e:
                        logger.debug(f"关闭浏览器失败: {str(e)}")

        # 计算失败的索引
        failed_indices = list(set(indices) - set(successful_indices))
        failed_indices = [str(index) for index in failed_indices]

        # 输出执行结果
        logger.info(f"成功索引: {','.join(map(str, successful_indices))}")
        logger.info(f"失败索引: {','.join(failed_indices)}")

        # 过滤掉特定异常的失败索引

        # 从过滤后的失败索引中移除特定类型的失败
        filtered_failed = [
            idx
            for idx in failed_indices
            if idx not in map(str, failed_proxy_ids + failed_discord_ids + failed_twitter_ids)
        ]
        logger.info(f"可重试失败索引（不包含下列三种异常）: {','.join(filtered_failed)}")

        if failed_proxy_ids:
            logger.error(f"代理异常索引: {','.join(map(str, failed_proxy_ids))}")
        if failed_discord_ids:
            logger.error(f"Discord绑定失败索引: {','.join(map(str, failed_discord_ids))}")
        if failed_twitter_ids:
            logger.error(f"Twitter绑定失败索引: {','.join(map(str, failed_twitter_ids))}")

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


def filter_failed_indices(failed_indices, exceptions):
    """过滤掉包含特定异常的失败索引

    Args:
        failed_indices: 失败索引列表
        exceptions: 需要排除的异常列表

    Returns
    -------
        list: 过滤后的失败索引列表
    """
    filtered_indices = []
    for index in failed_indices:
        if not any(exc in str(index) for exc in exceptions):
            filtered_indices.append(index)
    return filtered_indices


# python3 examples/enso/index.py run -t bit -i 1-10
# python3 examples/enso/index.py run   -t brave -i 1-100
if __name__ == "__main__":
    cli()
#  run_task(BrowserType.CHROME, 1)
