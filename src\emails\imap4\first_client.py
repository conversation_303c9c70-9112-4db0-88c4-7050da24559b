import imaplib
import socket
import ssl
from typing import Optional

from loguru import logger

from .imap_client import IMAPClient, MailConfig, SearchCriteria


class FirstClient(IMAPClient):
    """Rambler 邮箱客户端"""

    SUPPORTED_DOMAINS = ["first.mail"]
    IMAP_SERVER = "imap.firstmail.ltd"
    IMAP_PORT = 993

    def __init__(self, email: str, password: str):
        """初始化 Rambler 邮箱客户端

        Args:
            email: Rambler 邮箱地址
            password: 密码
        """
        config = MailConfig(
            email=email,
            password=password,
            imap_server=self.IMAP_SERVER,
            imap_port=self.IMAP_PORT,
        )
        super().__init__(config)
        # 自动连接并登录
        self.connect()

    def search_emails(self, criteria: SearchCriteria) -> list:
        """搜索邮件

        Args:
            criteria: 搜索条件

        Returns
        -------
            list: 符合条件的邮件列表
        """
        return super().search_emails(criteria)

    def get_latest_email(self) -> dict | None:
        """获取最新邮件

        Returns
        -------
            Optional[dict]: 最新邮件信息，如果没有则返回 None
        """
        return super().get_latest_email()


def main():
    """主函数，用于测试邮箱客户端的搜索功能"""
    import json
    import os
    import re
    from datetime import datetime, timedelta

    # 从环境变量或配置文件读取邮箱和密码
    # 为了安全，最好从环境变量读取而不是硬编码
    email = "<EMAIL>"
    password = "qmtoeatvS5575"

    try:
        # 创建 Rambler 邮箱客户端
        client = FirstClient(email, password)

        # 构建搜索条件：搜索过去7天内主题包含 "Verify" 的邮件
        yesterday = (datetime.now() - timedelta(days=7)).strftime("%d-%b-%Y")
        search_criteria = SearchCriteria(
            subject="access all of X's features",
            from_addr="<EMAIL>",
            # is_read=False
        )
        # 搜索邮件
        logger.info(f"正在搜索符合条件的邮件: {search_criteria}")
        emails = client.search_emails(search_criteria)

        # 输出搜索结果
        logger.info(f"找到 {len(emails)} 封符合条件的邮件")

        # 处理找到的邮件
        for i, email in enumerate(emails):
            logger.info(f"=== 邮件 {i + 1} ===")

            # 提取验证链接示例 (假设验证链接包含在邮件内容中)
            content = email.get("content", "")
            # 尝试不同的链接匹配模式
            verification_patterns = [
                r"^(\d{6})\s+is your login code for Wallet",  # 匹配 "数字 is your login code for Wallet" 格式
                r"\b(\d{6})\b",
                r"^(\d{6})",  # 匹配开头的6位数字
                r"Email verification code:\s*(\d+)",  # 匹配 Email verification code 格式
                r"Your one-time code is\s*:\s*(\d+)",  # 匹配 one-time code 格式
                r"Your one-time code is\s*:\s*(\d+)",  # 匹配其他格式
                r"Your verification code is:\s*(\d{4})",
            ]

            for pattern in verification_patterns:
                match = re.search(pattern, content)
                if match:
                    verification_link = match.group(1)
                    logger.info(f"找到验证码: {verification_link}")
                    break
            else:
                logger.warning("未找到验证链接")

        # 获取最新邮件
        latest = client.get_latest_email()
        if latest:
            logger.info("=== 最新邮件 ===")
            logger.info(f"主题: {latest.get('subject', 'N/A')}")
            logger.info(f"发件人: {latest.get('from', 'N/A')}")
            logger.info(f"日期: {latest.get('date', 'N/A')}")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
    finally:
        # 确保关闭连接
        if "client" in locals() and hasattr(client, "_disconnect"):
            client._disconnect()
            logger.info("已断开邮箱连接")


if __name__ == "__main__":
    # 配置日志
    logger.add("email_test_{time}.log", rotation="1 day", level="INFO")
    main()
