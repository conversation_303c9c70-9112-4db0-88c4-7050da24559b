from DrissionPage import Chromium
from loguru import logger
from typing import Optional
import csv
import os
from time import sleep
from src.utils.proxies import Proxies
from .base_browser import BaseBrowser, BrowserType
from src.fingerprints import (
    get_address_with_bit,
    close_browser_with_bit_id,
    getBrowserList,
    updateBrowserProxy,
    checkagent,
)
from src.utils import get_project_root_path


class BitBrowser(BaseBrowser):

    def get_chromium_page(self) -> Optional[Chromium]:
        """获取浏览器页面实例"""
        try:
            if not self.browser_id:
                logger.error(f"{self.id} browser_id 不存在")
                return None

            debugger_address = get_address_with_bit(self.browser_id)
            if not debugger_address:
                logger.error(f"{self.id} 获取调试地址失败")
                return None

            return Chromium(addr_or_opts=debugger_address)
        except Exception as e:
            logger.error(f"{self.id} 获取浏览器页面实例失败: {str(e)}")
            return None

    def close(self):
        """关闭浏览器"""
        try:
            if self.browser_id:
                close_browser_with_bit_id(self.browser_id)
                self.page = None  # 清理页面实例
                # 调用完不要立即删除窗口或者重新打开，等待 5 秒后进程彻底退出再操作
                sleep(5)
            else:
                logger.warning(f"{self.index} 没有可关闭的浏览器ID")
        except Exception as e:
            logger.error(f"{self.index} 关闭浏览器失败: {str(e)}")

    def wallet_config_path(self) -> str:
        """钱包配置路径"""
        return os.path.join(get_project_root_path(), "data/bit.csv")

    def _verify_proxy(self, proxy):
        """验证代理有效性"""
        try:
            if not proxy:
                return False
            return Proxies(proxy).verify()
        except Exception as e:
            logger.error(f"{self.browser_id} 代理验证失败: {str(e)}")
            return False

    def valid_and_set_proxy(self):
        """获取有效代理"""

        port = self.browser_config.get("proxy").split(":")[-1]
        if checkagent(port=port):
            return True
        else:
            # 配置可用proxy,同国家
            bit_bro_info = getBrowserList()
            last_ips = [item["lastIp"] for item in bit_bro_info]

            # 根据 browser_id 获取对应的 lastIp 和 country
            target_country = None
            for item in bit_bro_info:
                if item["id"] == self.browser_config.get("browser_id"):
                    target_ip = item["lastIp"]
                    target_country = item["country"]
                    break

            with open("data/ip_pools.txt", mode="r", encoding="utf-8") as csvfile:
                reader = csv.DictReader(csvfile)  # 使用 DictReader 读取 CSV 文件
                # 不使用已经在指纹中出现过的
                for row in reader:
                    ip, port = row.get("proxy").rsplit(":", 1)
                    region = row.get("region")  # 获取 region

                    # 检查当前 IP 是否在使用中，并且国家匹配
                    if ip not in last_ips and row["region"] == target_country:
                        # 更新代理
                        updateBrowserProxy(
                            self.browser_config.get("browser_id"), port, region
                        )
                        return True
                # 如果已经没有可用，使用已经在指纹中出现过的
                for row in reader:
                    ip, port = row.get("proxy").rsplit(":", 1)
                    region = row.get("region")  # 获取 region

                    # 检查当前 IP 是否在使用中，并且国家匹配
                    if str(ip) != str(target_ip) and row["region"] == target_country:
                        # 更新代理
                        updateBrowserProxy(
                            self.browser_config.get("browser_id"), port, region
                        )
                        return True
        return None

    @property
    def browser_type(self) -> BrowserType:
        return BrowserType.BIT
