import os
import random
import sys
from time import sleep
from src.utils.thread_executor import ThreadExecutor
import click
from loguru import logger

# 使用完整的导入路径
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.element_util import get_element, get_elements

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")


class FaucetMonAI:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.url = "https://monai.gg/faucet"
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def _check_cf_shield(self, lasted_tab):
        for _ in range(6):
            # 1. 判断是否在CF盾页面
            div_ele = lasted_tab.ele("x://div[@class='mx-auto']/div")
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            if iframe:
                try:
                    success = iframe.ele("tag:body").sr('@id=success')
                    if success and success.states.is_displayed:
                        logger.success(f"【{self.id}】 过CF盾成功")
                        return True

                    logger.info(f"【{self.id}】 在CF盾页面")
                    checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                    if not checkbox:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                    checkbox.wait.has_rect(timeout=20)
                    checkbox.click()
                    sleep(3)

                    success = iframe.ele("tag:body").sr('@id=success')
                    if success and success.states.is_displayed:
                        logger.success(f"【{self.id}】 过CF盾成功")
                        return True
                    else:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                except Exception as e:
                    logger.error(f"【{self.id}】 过CF盾失败: {e}")
                    sleep(2)
                    continue
        return False


    def _is_login(self, tab, address):
        short_address = address[:4] + '…' + address[-4:]
        if get_element(tab, f"x:(//button[normalize-space(text())='{short_address}'])[1]", 3):
            return True
        else:
            return False

    def _connect_wallet(self, lasted_tab, address):
        if self._is_login(lasted_tab, address):
            logger.success(f"【{self.id}】 钱包已连接")
            return True

        try_count = 5
        for i in range(try_count):
            logger.info(f"【{self.id}】钱包进行第 {i+1}/{try_count}次连接")

            connect_btn = get_element(lasted_tab,"x://button[text()='Connect Wallet']",3)
            if connect_btn:
                connect_btn.click()
                sleep(2)

            okx_wallet = get_element(lasted_tab, "x://button[.//div[contains(text(), 'OKX Wallet')]]", 3)
            if okx_wallet:
                okx_wallet.click()
                sleep(2)

            self.browser_controller.okx_wallet_connect()
            sleep(2)

            if self._is_login(lasted_tab, address):
                logger.success(f"【{self.id}】 钱包已连接")
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _task(self, lasted_tab):
        try:

            complete_task = set()
            for i in range(20):
                tasks = get_elements(
                    lasted_tab,
                    "x://button[contains(@class, 'border flex items-center justify-between')]",
                    5
                )
                for task in tasks:
                    if task.ele("t:svg").attr("viewBox") == '0 0 32 32':
                        complete_task.add(task.text)
                        continue
                    tab = task.click.for_new_tab()
                    sleep(3)
                    tab.close()

                if len(complete_task) == 4:
                    return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】 完成任务异常， error={str(e)}")
            return False


    def _bind_x(self, lasted_tab):
        try:
            bind_x = get_element(lasted_tab, "x:(//button[contains(@class, 'border flex items-center justify-between')])[1]", 5)
            if not bind_x:
                return False

            if bind_x.ele("t:svg").attr("viewBox") == '0 0 32 32':
                return True

            tab = bind_x.click.for_new_tab()
            tab_id = tab.tab_id

            tab.wait.ele_displayed("x://input[@id='allow']", timeout=10)
            allow_btn = get_element(tab, "x://input[@id='allow']", 3)
            if allow_btn:
                allow_btn.click()
                sleep(2)

            for i in range(10):
                if tab_id not in self.page.tab_ids:
                    logger.success(f"【{self.id}】 推特授权成功")
                    return True
                sleep(1)

            logger.error(f"【{self.id}】 推特授权失败，无法领水")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】 推特授权异常，error={str(e)}")
            return False

    def is_claim(self, lasted_tab):
        retry_count = 20
        for index in range(retry_count):
            try:
                logger.info(f"【{self.id}】第 {index + 1}/{retry_count} 次尝试领水")

                get_element(
                    lasted_tab,
                    "x:/html/body/div/div[2]/main/div[2]/div[2]/button",
                    5
                ).click()
                sleep(2)

                if get_element(lasted_tab, "x://div[contains(., 'successfully')]", 3):
                    logger.success(f"【{self.id}】第 {index + 1}/{retry_count} 次领水成功")
                    return True
                if get_element(lasted_tab, "x://div[contains(., 'address was used recently')]", 3):
                    logger.success(f"【{self.id}】今日已经领取过")
                    return True
                logger.info(f"【{self.id}】第 {index + 1}/{retry_count} 次领水失败, retry...")
                get_element(lasted_tab, "x://button[contains(normalize-space(text()),'Try Again')]", 3).click()

                self._check_cf_shield(lasted_tab)
            except Exception as e:
                logger.info(f"【{self.id}】第 {index + 1}/{retry_count} 次领水失败, retry...")
            sleep(1)
        return False

    def faucet(self):
        try:
            address = self.browser_controller.browser_config.evm_address

            # 1. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            lasted_tab = self.page.new_tab(self.url)
            sleep(8)

            result = self._connect_wallet(lasted_tab, address)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return False

            if not self._bind_x(lasted_tab):
                logger.error(f"【{self.id}】 绑定推特失败")
                return False

            if not self._task(lasted_tab):
                logger.error(f"【{self.id}】 任务未完成")
                return False

            input_i = get_element(lasted_tab, "x://div[@class='relative']//input", 3)
            input_i.clear(True)
            input_i.input(address)
            sleep(1)

            if not self._check_cf_shield(lasted_tab):
                logger.error(f"【{self.id}】 过CF盾失败")
                return False

            return self.is_claim(lasted_tab)

        except Exception as e:
            logger.error(f"【{id}】 领水发生异常，error={str(e)}")
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(e)
        return False


def _run_task(type, index):
    try:
        browser = FaucetMonAI(type, str(index))
        browser.faucet()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=2, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                browser = FaucetMonAI(type, str(index))
                return browser.faucet()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/faucet_monai.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.ADS, 50)
