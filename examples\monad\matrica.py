"""
Matrica自动化脚本

该模块提供了Matrica网站的自动化操作功能，包括登录、任务执行等。
支持多浏览器类型和并发执行。
"""

import os
import random
import re
from time import sleep

import click
from DrissionPage.errors import PageDisconnectedError
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.emails.imap4.email_client import EmailClient, SearchCriteria
from src.utils.common import generate_username, get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor

MATRICA_LOGIN_URL = "https://matrica.io/login"
LOG_FILE = "logs/matrica.log"
DATA_DIR_NAME = "monad"
CSV_FILE_NAME = "matrica.csv"
DEFAULT_TIMEOUT = 10
PAGE_LOAD_WAIT = 10
CLICK_WAIT = 1
WALLET_CONNECTION_WAIT = 3
XPATHS = {
    "wallet_connect_button": (
        "x://div[@class='max-w-sm m-auto']//div[@class='divider']/following-sibling::div//div[@role='button']"
    ),
}
MAX_RETRIES = 3
TASK_TIMEOUT = 6 * 3600
DEFAULT_WORKERS = 1
RETRY_INTERVAL = 10
# 配置常量
DEFAULT_BROWSER_TYPE_ENUM = BrowserType[DEFAULT_BROWSER_TYPE]

# 配置日志
logger.add(LOG_FILE, rotation="10MB", level="SUCCESS")


class Matrica:
    """
    Matrica自动化操作类

    提供Matrica网站的自动化操作功能，包括登录、钱包连接等。

    Attributes:
        index (str): 浏览器索引
        browser_type (BrowserType): 浏览器类型
        browser_controller (BrowserController): 浏览器控制器
        address (str): EVM地址
        data_util (DataUtil): 数据工具类
    """

    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil) -> None:
        """
        初始化Matrica实例

        Args:
            browser_type: 浏览器类型
            index: 浏览器索引
            data_util: 数据工具类实例
        """
        self.index = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util

        # 初始化数据
        self._init_data()

    def _init_data(self) -> None:
        """
        初始化数据记录

        如果数据不存在则创建新的数据记录，包含索引、浏览器类型和地址信息。
        """
        try:
            # 检查是否已存在数据
            existing_data = self.data_util.get(self.address)
            if existing_data:
                logger.debug(f"【{self.index}】{self.address} 数据已存在，跳过初始化")
                return

            # 创建新的数据记录
            new_data = {"index": self.index, "type": self.browser_type.value, "address": self.address}
            self.data_util.add(new_data)
            logger.success(f"【{self.index}】{self.address} 数据初始化成功")

        except Exception as e:
            logger.error(f"【{self.index}】{self.address} 初始化数据失败: {e}")

    def _wait_for_page_load(self, tab, xpath, timeout: int = None) -> bool:
        """
        等待页面加载完成

        Args:
            tab: 浏览器标签页
            xpath: xpath表达式
            timeout: 超时时间（秒），默认使用配置值

        Returns:
            bool: 页面是否加载完成
        """
        try:
            # 可以根据具体页面特征来判断加载完成
            # 这里简单使用固定等待时间
            wait_time = PAGE_LOAD_WAIT if timeout is None else timeout
            tab.wait.ele_displayed(xpath, timeout=wait_time)
            sleep(1)
            return True
        except Exception as e:
            logger.error(f"【{self.index}】等待页面加载元素:{xpath} 失败: {str(e)}")
            return False

    def login(self) -> bool:
        """
        登录Matrica网站

        执行完整的登录流程，包括浏览器初始化、钱包登录和网站连接。

        Returns:
            bool: 登录是否成功
        """
        try:
            logger.info(f"【{self.index}】开始登录Matrica网站")

            # 步骤1: 初始化浏览器和钱包
            if not self._setup_browser():
                return False

            # 步骤2: 打开Matrica登录页面
            tab = self.browser_controller.page.new_tab(MATRICA_LOGIN_URL)
            sleep(2)
            if "https://matrica.io/home" in tab.url:
                logger.info(f"【{self.index}】已登录，跳过登录流程")
                self.data_util.update(self.address, {"register": "1"})
                return True

            if self._is_login(tab):
                logger.info(f"【{self.index}】已登录，跳过登录流程")
                return True

            # 步骤3: 点击钱包连接按钮
            if not self._connect_wallet():
                return False

            logger.success(f"【{self.index}】Matrica登录流程完成")
            return True
        except Exception as e:
            logger.error(f"【{self.index}】登录失败: {e}")
            return False

    def _is_login(self, tab) -> bool:
        logger.info(f"【{self.index}】检查是否登录")
        label = get_element(tab, "x://div[@class='justify-end']//label", 5)
        return label is not None

    def _setup_browser(self) -> bool:
        """
        设置浏览器环境

        Returns:
            bool: 设置是否成功
        """
        try:
            self.browser_controller.window_max()
            self.browser_controller.okx_wallet_login()
            logger.debug(f"【{self.index}】浏览器环境设置完成")
            return True
        except Exception as e:
            logger.error(f"【{self.index}】浏览器环境设置失败: {e}")
            return False

    def _connect_wallet(self) -> bool:
        """
        连接钱包

        Returns:
            bool: 连接是否成功
        """
        retry_count = 3
        for i in range(retry_count):
            tab = None
            try:
                tab = self.browser_controller.page.new_tab(MATRICA_LOGIN_URL)
                tab.close(others=True)
                sleep(2)

                # 点击Select a NetWork
                select_network_xpath = (
                    "x://div[@class='max-w-sm m-auto']//div["
                    "@class='divider']/following-sibling::div//div[@role='button']"
                )
                select_network_btn = get_element(tab, select_network_xpath, 5)
                if not select_network_btn:
                    continue
                select_network_btn.click()
                sleep(2)

                # 选择EVM链
                evm_network_xpath = (
                    "x://div[@class='max-w-sm m-auto']//div["
                    "@class='divider']/following-sibling::div//ul/li[contains(., 'EVM')]"
                )
                evm_network_btn = get_element(tab, evm_network_xpath, 5)
                if not evm_network_btn:
                    continue
                evm_network_btn.click()
                sleep(2)

                # 选择Okx Wallet
                okx_wallet_xpath = "x://span[@class='text-base font-medium' and text()='OKX Wallet']/ancestor::button"
                okx_wallet_btn = get_element(tab, okx_wallet_xpath, 5)
                if not okx_wallet_btn:
                    continue

                okx_wallet_btn.click()

                if self.browser_controller.page.wait.new_tab(timeout=5, curr_tab=tab):
                    if not self.browser_controller.okx_wallet_connect():
                        logger.warning(f"【{self.index}】第 {i+1} 次登录失败, retry...")
                        continue

                sign_message_xpath = "x://span[text()='Sign Message']/parent::button"
                sign_message_btn = get_element(tab, sign_message_xpath, 5)
                if not sign_message_btn:
                    continue

                tab.listen.start(targets="https://api\\.matrica\\.io/api/login/wallet/.*", is_regex=True)
                sign_message_btn.click()
                res = tab.listen.wait(timeout=90)
                if res and res.response and res.response.body:
                    registered = res.response.body.get("user", {}).get("registered", False)
                    username = res.response.body.get("user", {}).get("username", "")
                    if registered:
                        self.data_util.update(self.address, {"register": "1", "username": username})
                        logger.success(f"【{self.index}】已注册")

                if self.browser_controller.okx_wallet_sign():
                    logger.success(f"【{self.index}】登录成功")
                    return True

                logger.warning(f"【{self.index}】第 {i+1} 次登录失败, retry...")
                tab.close()
            except Exception as e:
                logger.error(f"【{self.index}】钱包连接失败: {e}")
                if tab:
                    tab.close()
        logger.warning(f"【{self.index}】重试 {retry_count} 次后, 仍登录失败...")
        return False

    def register(self, tab) -> bool:
        """
        注册Matrica账户

        业务逻辑：
        1. 找到所有的chains，循环点击
        2. 点击continue按钮
        3. 输入用户名
        4. 监听网络请求
        5. 如果用户名已存在，重新生成用户名继续输入
        6. 直到注册成功，将注册状态和用户名写入文件

        Args:
            tab: 浏览器标签页

        Returns:
            bool: 注册是否成功
        """
        try:
            logger.info(f"【{self.index}】开始注册流程")

            existing_data = self.data_util.get(self.address)
            if existing_data.get("register") == "1":
                logger.info(f"【{self.index}】已注册，跳过注册流程")
                return True

            # 步骤1: 选择所有chains
            if not self._select_all_chains(tab):
                logger.error(f"【{self.index}】选择chains失败")
                return False

            # 步骤2: 点击继续按钮
            if not self._click_continue_button(tab):
                logger.error(f"【{self.index}】点击继续按钮失败")
                return False

            # 检测CF盾
            root_element = get_element(tab, "x://div[@class='flex justify-center']/div/div", 5)
            if not self._click_cloudflare(tab, root_element):
                logger.error(f"【{self.index}】注册过CF盾失败")
                return False

            # 步骤3: 注册用户名（包含重试逻辑）
            if not self._register_username_with_retry(tab):
                logger.error(f"【{self.index}】注册用户名失败")
                return False

            logger.success(f"【{self.index}】注册流程完成")
            return True

        except Exception as e:
            logger.error(f"【{self.index}】注册过程发生异常: {e}")
            return False

    def _select_all_chains(self, tab) -> bool:
        """
        选择所有可用的chains

        Args:
            tab: 浏览器标签页

        Returns:
            bool: 是否成功选择chains
        """
        try:
            chains = get_elements(tab, "x://input[@class='toggle toggle-primary' and @type='checkbox']", timeout=10)
            if not chains:
                logger.warning(f"【{self.index}】未找到可选择的chains")
                return False

            logger.info(f"【{self.index}】找到 {len(chains)} 个chains，开始选择")

            for i, chain in enumerate(chains):
                try:
                    # 获取chain名称
                    chain_name = "未知"
                    try:
                        chain_name = chain.prev().child(2).child().text or f"Chain-{i+1}"
                    except:
                        chain_name = f"Chain-{i+1}"

                    # 点击选择chain
                    chain.click()
                    sleep(1)  # 减少等待时间
                    logger.info(f"【{self.index}】已选择 {chain_name} 网络")

                except Exception as e:
                    logger.warning(f"【{self.index}】选择第 {i+1} 个chain失败: {e}")
                    continue

            return True

        except Exception as e:
            logger.error(f"【{self.index}】选择chains过程发生异常: {e}")
            return False

    def _click_continue_button(self, tab) -> bool:
        """
        点击继续按钮

        Args:
            tab: 浏览器标签页

        Returns:
            bool: 是否成功点击
        """
        try:
            self.browser_controller.window_max()
            btn = get_element(tab, "x://button[@aria-label='Continue with selected chains']", timeout=10)
            if not btn:
                logger.error(f"【{self.index}】未找到继续按钮")
                return False

            btn.click()
            sleep(2)
            logger.info(f"【{self.index}】已点击继续按钮")
            return True

        except Exception as e:
            logger.error(f"【{self.index}】点击继续按钮失败: {e}")
            return False

    def _register_username_with_retry(self, tab, max_retries: int = 5) -> bool:
        """
        注册用户名，包含重试逻辑

        Args:
            tab: 浏览器标签页
            max_retries: 最大重试次数

        Returns:
            bool: 是否注册成功
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"【{self.index}】开始第 {attempt + 1} 次用户名注册尝试")

                # 生成新的用户名
                username = generate_username()
                logger.info(f"【{self.index}】生成用户名: {username}")

                # 输入用户名
                if not self._input_username(tab, username):
                    logger.warning(f"【{self.index}】输入用户名失败，尝试下一个")
                    continue

                # 开始监听网络请求
                self._start_registration_listener(tab, username)

                # 点击注册按钮
                if not self._click_signup_button(tab):
                    logger.warning(f"【{self.index}】点击注册按钮失败，尝试下一个")
                    continue

                # 处理注册响应
                result = self._handle_registration_response(tab, username)
                if result == "success":
                    # 保存注册信息
                    self.data_util.update(self.address, {"register": "1", "username": username})
                    logger.success(f"【{self.index}】注册成功，用户名: {username}")
                    return True
                elif result == "username_exists":
                    logger.warning(f"【{self.index}】用户名 {username} 已存在，生成新用户名重试")
                    # 清空输入框，准备下次重试
                    self._clear_username_input(tab)
                    continue
                else:
                    logger.warning(f"【{self.index}】注册失败，原因: {result}")
                    continue

            except Exception as e:
                logger.error(f"【{self.index}】第 {attempt + 1} 次注册尝试异常: {e}")
                continue

        logger.error(f"【{self.index}】经过 {max_retries} 次尝试后注册失败")
        return False

    def _input_username(self, tab, username: str) -> bool:
        """
        输入用户名

        Args:
            tab: 浏览器标签页
            username: 用户名

        Returns:
            bool: 是否输入成功
        """
        try:
            username_input = get_element(
                tab, "x://span[text()='Username']/parent::label/following-sibling::input", timeout=10
            )
            if not username_input:
                logger.error(f"【{self.index}】未找到用户名输入框")
                return False

            # 清空输入框并输入新用户名
            username_input.clear()
            sleep(0.5)
            username_input.input(username)
            sleep(1)

            logger.debug(f"【{self.index}】已输入用户名: {username}")
            return True

        except Exception as e:
            logger.error(f"【{self.index}】输入用户名失败: {e}")
            return False

    def _clear_username_input(self, tab) -> bool:
        """
        清空用户名输入框

        Args:
            tab: 浏览器标签页

        Returns:
            bool: 是否清空成功
        """
        try:
            username_input = get_element(
                tab, "x://span[text()='Username']/parent::label/following-sibling::input", timeout=5
            )
            if username_input:
                username_input.clear()
                sleep(0.5)
                return True
            return False
        except Exception as e:
            logger.warning(f"【{self.index}】清空用户名输入框失败: {e}")
            return False

    def _start_registration_listener(self, tab, username: str) -> None:
        """
        开始监听注册相关的网络请求

        Args:
            tab: 浏览器标签页
            username: 当前尝试的用户名
        """
        try:
            # 监听用户名检查和注册接口
            targets = [
                f"https://api.matrica.io/api/user/check?username={username}",
                "https://api.matrica.io/api/user/register",
            ]
            tab.listen.start(targets=targets)
            logger.debug(f"【{self.index}】开始监听注册请求: {targets}")

        except Exception as e:
            logger.error(f"【{self.index}】启动网络监听失败: {e}")

    def _click_signup_button(self, tab) -> bool:
        """
        点击注册按钮

        Args:
            tab: 浏览器标签页

        Returns:
            bool: 是否点击成功
        """
        try:
            signup_btn = get_element(tab, "x://button[text()='Sign Up']", timeout=10)
            if not signup_btn:
                logger.error(f"【{self.index}】未找到注册按钮")
                return False

            signup_btn.click()
            logger.debug(f"【{self.index}】已点击注册按钮")
            return True

        except Exception as e:
            logger.error(f"【{self.index}】点击注册按钮失败: {e}")
            return False

    def _handle_registration_response(self, tab, username: str, timeout: int = 30) -> str:
        """
        处理注册响应

        Args:
            tab: 浏览器标签页
            username: 当前尝试的用户名
            timeout: 超时时间（秒）

        Returns:
            str: 处理结果 ("success", "username_exists", "error", "timeout")
        """
        try:
            logger.debug(f"【{self.index}】等待注册响应，超时时间: {timeout}秒")

            # 监听网络响应
            for packet in tab.listen.steps(timeout=timeout):
                try:
                    request_url = packet.request.url
                    response_status = packet.response.status

                    logger.debug(f"【{self.index}】收到响应: URL={request_url}, Status={response_status}")

                    # 处理用户名检查响应
                    if "user/check" in request_url:
                        if response_status == 200:
                            # 用户名可用，继续等待注册响应
                            logger.debug(f"【{self.index}】用户名 {username} 可用")
                            continue
                        elif response_status in [400, 409]:
                            # 用户名已存在
                            logger.warning(f"【{self.index}】用户名 {username} 已存在 (状态码: {response_status})")
                            return "username_exists"
                        else:
                            logger.warning(f"【{self.index}】用户名检查异常，状态码: {response_status}")
                            continue

                    # 处理注册响应
                    elif "user/register" in request_url:
                        if response_status in [200, 201]:
                            logger.success(f"【{self.index}】注册成功，状态码: {response_status}")
                            return "success"
                        elif response_status in [400, 409]:
                            logger.warning(f"【{self.index}】注册失败，用户名可能已存在，状态码: {response_status}")
                            return "username_exists"
                        else:
                            logger.error(f"【{self.index}】注册失败，状态码: {response_status}")
                            return "error"

                except Exception as e:
                    logger.warning(f"【{self.index}】处理网络响应异常: {e}")
                    continue

            # 超时未收到响应
            logger.warning(f"【{self.index}】等待注册响应超时")
            return "timeout"

        except Exception as e:
            logger.error(f"【{self.index}】处理注册响应异常: {e}")
            return "error"

    def link_dc(self, tab) -> bool:
        """
        绑定Discord账户

        Args:
            tab: 浏览器标签页或表格元素

        Returns:
            bool: 绑定是否成功
        """
        try:
            # 检查是否已经绑定
            existing_data = self.data_util.get(self.address)
            if existing_data.get("link_dc") == "1":
                logger.info(f"【{self.index}】Discord已绑定，跳过")
                return True

            logger.info(f"【{self.index}】开始绑定Discord")

            # 找到Discord行中的Link按钮
            # 查找包含discord文本且状态为"No account linked"的行中的Link按钮
            discord_row_xpath = "x://tr[.//span[text()='discord'] and .//span[text()='No account linked']]"
            discord_row = get_element(tab, discord_row_xpath, timeout=5)

            if not discord_row:
                # 可能已经绑定了，检查是否有已绑定的状态
                discord_unlink_xpath = "x://tr[.//span[text()='discord'] and .//label[text()='Unlink']]"
                discord_unlink = get_element(tab, discord_unlink_xpath, timeout=3)
                if discord_unlink:
                    logger.success(f"【{self.index}】Discord已绑定")
                    self.data_util.update(self.address, {"link_dc": "1"})
                    return True
                else:
                    logger.error(f"【{self.index}】绑定Discord失败，网络异常")
                    return False

            # 找到Link按钮并点击
            link_button = get_element(discord_row, "x://button[text()='Link']", timeout=5)
            if not link_button:
                logger.error(f"【{self.index}】未找到Discord Link按钮")
                return False

            # 点击Link按钮
            # tab.reconnect()
            # self.browser_controller.page.reconnect()
            link_button.click()
            # tab.wait.url_change(text="discord.com/oauth2/authorize", timeout=10)
            tab.wait.doc_loaded(timeout=10)
            # sleep(2)
            logger.info(f"【{self.index}】已点击Discord Link按钮")

            # self.browser_controller.page.reconnect()
            # sleep(2)

            # 等待跳转到Discord授权页面
            current_tab = self.browser_controller.page.latest_tab
            current_tab.listen.start("https://api.matrica.io/api/user/profile")
            if "login" in current_tab.url:
                logger.info(f"【{self.index}】需要登录Discord")
                if not self.browser_controller.login_discord():
                    logger.error(f"【{self.index}】Discord登录失败")
                    return False

            if not current_tab.wait.url_change(text="discord.com/oauth2/authorize", timeout=10):
                logger.error(f"【{self.index}】登录后未跳转到授权页面")
                return False

            current_tab.set.window.max()
            current_tab.wait.ele_displayed("x:(//button)[2]", timeout=5)
            oauth_btn = get_element(current_tab, "x:(//button)[2]", 5)
            if not oauth_btn:
                logger.error(f"【{self.index}】{self.address}未找到Twitter授权按钮")
                return False

            oauth_btn.click()
            res = current_tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】绑定Discord失败，点击Allow按钮无响应")
                return False
            data = res.response.body.get("oauth", [])
            has_discord = any(item.get("name") == "discord" for item in data)
            discord_name = next(item["externalName"] for item in data if item["name"] == "discord")
            if has_discord:
                logger.success(f"【{self.index}】{self.address}绑定Discord成功")
                self.data_util.update(self.address, {"link_dc": "1", "dc_name": discord_name.replace("#0", "")})
                return True

            logger.error(f"【{self.index}】{self.address}绑定Discord失败")
            return False

        except Exception as e:
            logger.error(f"【{self.index}】绑定Discord失败: {e}")
            return False

    def link_x(self, tab) -> bool:
        try:
            existing_data = self.data_util.get(self.address)
            if existing_data.get("link_x") == "1":
                logger.info(f"【{self.index}】推特已绑定，跳过")
                return True

            logger.info(f"【{self.index}】开始绑定推特")
            twitter_row_xpath = "x://tr[.//span[text()='X'] and .//span[text()='No account linked']]"
            twitter_row = get_element(tab, twitter_row_xpath, timeout=5)

            if not twitter_row:
                # 可能已经绑定了，检查是否有已绑定的状态
                twitter_unlink_xpath = "x://tr[.//span[text()='X'] and .//label[text()='Unlink']]"
                twitter_unlink = get_element(tab, twitter_unlink_xpath, timeout=3)
                if twitter_unlink:
                    logger.success(f"【{self.index}】twitter已绑定")
                    self.data_util.update(self.address, {"link_x": "1"})
                    return True
                else:
                    logger.error(f"【{self.index}】绑定twitter失败，网络异常")
                    return False

            # 找到Link按钮并点击
            link_button = get_element(twitter_row, "x://button[text()='Link']", timeout=5)
            if not link_button:
                logger.error(f"【{self.index}】未找到twitter Link按钮")
                return False

            # 点击Link按钮
            link_button.click()
            sleep(2)
            logger.info(f"【{self.index}】已点击twitter Link按钮")

            self.browser_controller.page.reconnect()
            sleep(2)

            # 等待跳转到twitter授权页面
            current_tab = self.browser_controller.page.latest_tab
            current_tab.listen.start("https://api.matrica.io/api/user/profile")
            if "login" in current_tab.url:
                logger.info(f"【{self.index}】需要登录推特")
                if not self.browser_controller.login_x(False):
                    logger.error(f"【{self.index}】推特登录失败")
                    return False

            if not current_tab.wait.url_change(text="x.com/i/oauth2/authorize", timeout=10):
                logger.error(f"【{self.index}】登录后未跳转到授权页面")
                return False

            current_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            oauth_btn = get_element(current_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not oauth_btn:
                logger.error(f"【{self.index}】{self.address}未找到Twitter授权按钮")
                return False

            oauth_btn.click()
            res = current_tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】绑定Twitter失败，点击Authorization 按钮无响应")
                return False
            data = res.response.body.get("oauth", [])
            has_twitter = any(item.get("name") == "twitter" for item in data)
            twitter_name = next(item["externalName"] for item in data if item["name"] == "twitter")
            if has_twitter:
                logger.success(f"【{self.index}】{self.address}绑定twitter成功")
                self.data_util.update(self.address, {"link_x": "1", "x_name": twitter_name})
                return True

            logger.error(f"【{self.index}】{self.address}绑定twitter失败")
            return False

        except Exception as e:
            logger.error(f"【{self.index}】绑定推特失败: {e}")
            return False

    def link_tg(self, tab) -> bool:
        try:
            existing_data = self.data_util.get(self.address)
            if existing_data.get("link_tg") == "1" or existing_data.get("link_tg") == "-1":
                logger.info(f"【{self.index}】Telegram已绑定，跳过")
                return True

            if not self.browser_controller.browser_config.tg_cellphone:
                self.data_util.update(self.address, {"link_tg": "-1"})
                logger.info(f"【{self.index}】未配置Telegram, 跳过...")
                return True

            logger.info(f"【{self.index}】开始绑定Telegram")
            telegram_row_xpath = "x://tr[.//span[text()='telegram'] and .//span[text()='No account linked']]"
            telegram_row = get_element(tab, telegram_row_xpath, timeout=5)

            if not telegram_row:
                # 可能已经绑定了，检查是否有已绑定的状态
                telegram_unlink_xpath = "x://tr[.//span[text()='telegram'] and .//label[text()='Unlink']]"
                telegram_unlink = get_element(tab, telegram_unlink_xpath, timeout=3)
                if telegram_unlink:
                    logger.success(f"【{self.index}】telegram已绑定")
                    self.data_util.update(self.address, {"link_tg": "1"})
                    return True
                else:
                    logger.error(f"【{self.index}】绑定twitter失败，网络异常")
                    return False

            # 找到Link按钮并点击
            link_button = get_element(telegram_row, "x://button[text()='Link']", timeout=5)
            if not link_button:
                logger.error(f"【{self.index}】未找到telegram Link按钮")
                return False

            # 点击Link按钮
            tab.listen.start("https://api.matrica.io/api/user/profile")
            link_button.click()
            sleep(2)
            logger.info(f"【{self.index}】已点击telegram Link按钮")

            # 等待跳转到telegram授权页面
            current_tab = self.browser_controller.page.latest_tab
            current_tab.wait.url_change(text="oauth.telegram.org", timeout=15)
            current_tab.wait.ele_displayed("x://button[@class='button-item ripple-handler']", timeout=15)
            accept_btn = get_element(current_tab, "x://button[@class='button-item ripple-handler']", 5)
            if not accept_btn:
                logger.error(f"【{self.index}】{self.address}未找到telegram授权按钮或未登录")
                return False

            accept_btn.click()
            res = tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】绑定telegram失败，点击Accept 按钮无响应")
                return False
            data = res.response.body.get("oauth", [])
            has_telegram = any(item.get("name") == "telegram" for item in data)
            telegram_name = next(item["externalName"] for item in data if item["name"] == "telegram")
            if has_telegram:
                logger.success(f"【{self.index}】{self.address}绑定telegram成功")
                self.data_util.update(self.address, {"link_tg": "1", "tg_name": telegram_name})
                return True

            logger.error(f"【{self.index}】{self.address}绑定Telegram失败")
            return False

        except Exception as e:
            logger.error(f"【{self.index}】绑定Telegram失败: {e}")
            return False

    def link_email(self, tab) -> bool:
        try:
            existing_data = self.data_util.get(self.address)
            if existing_data.get("link_email") == "1":
                logger.info(f"【{self.index}】邮箱已绑定，跳过")
                return True

            logger.info(f"【{self.index}】开始绑定邮箱")
            email_row_xpath = "x://tr[.//td[contains(., 'Email')] and .//td[contains(., 'Verify')]]"
            email_row = get_element(tab, email_row_xpath, timeout=5)
            if not email_row:
                # 可能已经绑定了，检查是否有已绑定的状态
                email_unlink_xpath = "x://tr[.//td[contains(., 'Email')] and .//button[text()='Unlink']]"
                email_unlink = get_element(tab, email_unlink_xpath, timeout=3)
                if email_unlink:
                    logger.success(f"【{self.index}】邮箱已绑定")
                    self.data_util.update(self.address, {"link_email": "1"})
                    return True
                else:
                    logger.error(f"【{self.index}】绑定邮箱失败，网络异常")
                    return False

            email_input = get_element(email_row, "x://input[@type='text']", timeout=5)
            if not email_input:
                logger.error(f"【{self.index}】未找到邮箱输入框")
                return False

            email = self.browser_controller.browser_config.proxy_email or self.browser_controller.browser_config.email
            email_input.input(email)
            sleep(0.5)
            link_button = get_element(email_row, "x://button[text()='Verify']", timeout=5)
            if not link_button:
                logger.error(f"【{self.index}】未找到邮箱Link按钮")
                return False
            link_button.click()

            tab.listen.start("https://api.matrica.io/api/email/send")
            shadow_root_xpath = (
                "x://h3[text()='Security Verification']/parent::div[1]/following-sibling::div[1]/div[2]/div"
            )
            shadow_root = get_element(tab, shadow_root_xpath, 10)
            if not self._click_cloudflare(tab, shadow_root):
                logger.error(f"【{self.index}】验证邮箱过CF盾失败")
                return False
            res = tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】绑定邮箱失败，点击Link按钮无响应")
                return False
            if res.response.status not in [200, 201]:
                logger.error(f"【{self.index}】绑定邮箱发送验证码失败")
                return False

            close_btn_xpath = "x://h3[text()='Email Verification']/following-sibling::div[1]/button"
            close_btn = get_element(tab, close_btn_xpath)
            if not close_btn:
                close_btn.click()

            email_pwd = self.browser_controller.browser_config.email_password
            if "gmail.com" in email:
                email_pwd = self.browser_controller.browser_config.email_imap4_pwd

            verify_url = self._get_email_verify_url(self.browser_controller.browser_config.email, email_pwd, email)
            if not verify_url:
                logger.error(f"【{self.index}】{self.address}未收到邮箱验证邮件")
                return False

            new_tab = self.browser_controller.page.new_tab("")
            new_tab.listen.start("https://api.matrica.io/api/email/verify")
            new_tab.get(verify_url)
            res = tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】验证邮箱未收到请求")
                return False

            if res.response.statusin[200, 201]:
                logger.success(f"【{self.index}】邮箱绑定成功")
                self.data_util.update(self.address, {"link_email": "1", "email": email})
                return True
            else:
                logger.error(f"【{self.index}】{self.address}绑定邮箱失败， error={res.response.body}")
                return False

        except Exception as e:
            logger.error(f"【{self.index}】绑定邮箱失败失败: {e}")
            return False

    def _click_cloudflare(self, tab, root_element):
        try:
            if root_element:
                logger.info(f"【{self.index}】等待CF盾加载")
                sleep(8)
            if not root_element:
                return True
            iframe = root_element.sr("x://iframe[contains(@src, 'challenges.cloudflare.com')]", timeout=10)
            if not iframe:
                return True
            logger.info(f"【{self.index}】开始过CF盾")
            # sleep(6)

            try:
                success = iframe.ele("tag:body", timeout=10).sr("@id=success", timeout=10)
                if success and success.states.is_displayed:
                    logger.success(f"【{self.index}】过CF盾成功")
                    return True
            except PageDisconnectedError as e:
                logger.warning(f"【{self.index}】过CF盾失败， 页面断开连接")
                if tab.states.is_disconnected:
                    tab.reconnect()
                else:
                    self.browser_controller.page.reconnect()

            try:
                checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']", timeout=10)
                if not checkbox:
                    raise Exception(f"【{self.index}】找到CF盾勾选框")

                checkbox.wait.has_rect(timeout=20)
                checkbox.click()
            except PageDisconnectedError as e:
                logger.warning(f"【{self.index}】过CF盾失败， 页面断开连接")
                tab.reconnect()
                self.browser_controller.page.reconnect()

            if tab.wait.ele_deleted(root_element, timeout=10):
                return True

            success = iframe.ele("tag:body").sr("@id=success")
            if success and success.states.is_displayed:
                logger.success(f"【{self.index}】过CF盾成功")
                return True
            return False
        except Exception as e:
            if "frameId" in str(e):
                logger.success(f"【{self.index}】过CF盾成功")
                return True
            logger.error(f"【{self.index}】过CF盾失败， error={str(e)}")
            return False


    def _get_email_verify_url(self, email, email_pwd, to_email):
        try:
            subject = "Email verification for matrica.io"
            email_client = EmailClient(email, email_pwd)
            search_criteria = SearchCriteria(
                subject=subject,
                to=to_email,
                is_read=False,
            )
            try:
                emails = email_client.search_emails_with_retry(search_criteria, 15)
            except Exception as e:
                logger.error(f"【{self.index}】 获取验证码邮件出错, {email} 搜索邮件失败: {e}")
                emails = []

            if len(emails) == 0:
                latest_email = email_client.get_latest_email()
                if not latest_email:
                    logger.error(f"{self.index} 未找到验证码邮件")
                    return None

                emails.append(latest_email)

            email = emails[0]
            content = email["content"]
            pattern = r'<a href="(https://matrica\.io/auth/email\?code=[^"]+)"'
            match = re.search(pattern, content)
            return match.group(1) if match else None
        except Exception as e:
            logger.error(f"{self.index} 获取验证码失败: {e}")
            return None

    def verify_email(self, tab):
        try:
            email_row_xpath = "x://tr[.//td[contains(., 'Email')]]"
            email_row = get_element(tab, email_row_xpath, timeout=5)
            if not email_row:
                logger.error(f"【{self.index}】未找到邮箱行")
                return False

            link_button = get_element(email_row, "x://button", timeout=5)
            if not link_button:
                logger.error(f"【{self.index}】未找到发送邮件按钮")
                return False
            link_button.click()

            tab.listen.start("https://api.matrica.io/api/email/send")
            shadow_root_xpath = (
                "x://h3[text()='Security Verification']/parent::div[1]/following-sibling::div[1]/div[2]/div"
            )
            shadow_root = get_element(tab, shadow_root_xpath, 10)
            if not self._click_cloudflare(tab, shadow_root):
                logger.error(f"【{self.index}】验证邮箱过CF盾失败")
                return False

            res = tab.listen.wait(timeout=90)
            if not res or not res.response:
                logger.error(f"【{self.index}】验证邮箱失败，无响应")
                return False
            if res.response.status not in [200, 201]:
                logger.error(f"【{self.index}】绑定邮箱发送验证码失败")
                return False

            email = self.browser_controller.browser_config.proxy_email or self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            if "gmail.com" in email:
                email_pwd = self.browser_controller.browser_config.email_imap4_pwd

            verify_url = self._get_email_verify_url(self.browser_controller.browser_config.email, email_pwd, email)
            if not verify_url:
                logger.error(f"【{self.index}】{self.address}未收到邮箱验证邮件")
                return False

            new_tab = self.browser_controller.page.new_tab("")
            new_tab.listen.start("https://api.matrica.io/api/email/verify")
            new_tab.get(verify_url)
            res = new_tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.index}】验证邮箱未收到请求")
                return False

            if res.response.status in[200, 201]:
                logger.success(f"【{self.index}】邮箱绑定成功")
                self.data_util.update(self.address, {"link_email": "1", "email": email})
                return True
            else:
                logger.error(f"【{self.index}】{self.address}绑定邮箱失败， error={res.response.body}")
                return False
        except Exception as e:
            logger.error(f"{self.index} 验证邮箱失败: {e}")
            return False

    def link_social(self):
        try:
            record = self.data_util.get(self.address)
            if record.get("status", "0") == "1":
                logger.success(f"【{self.index}】社交四件套均已绑定成功")
                return True

            tab = self.browser_controller.page.latest_tab
            tab.listen.start("https://api.matrica.io/api/user/profile")
            tab.get("https://matrica.io/settings")
            res = tab.listen.wait(timeout=90)

            data = res.response.body.get("oauth", [])
            link_dc = False
            if record.get("link_dc") != "1":
                discord_name = (
                    next((item["externalName"] for item in data if item["name"] == "discord"), None) if data else None
                )
                if discord_name:
                    link_dc = True
                    discord_name = discord_name.replace("#0", "")
                    logger.success(f"【{self.index}】Discord已绑定")
                    self.data_util.update(self.address, {"link_dc": "1", "dc_name": discord_name})
                else:
                    link_dc = self.link_dc(tab)
            else:
                link_dc = True
                logger.success(f"【{self.index}】Discord已绑定")

            link_x = False
            if record.get("link_x") != "1":
                twitter_name = (
                    next((item["externalName"] for item in data if item["name"] == "twitter"), None) if data else None
                )
                if twitter_name:
                    link_x = True
                    logger.success(f"【{self.index}】twitter已绑定")
                    self.data_util.update(self.address, {"link_x": "1", "x_name": twitter_name})
                else:
                    link_x = self.link_x(tab)
            else:
                link_x = True
                logger.success(f"【{self.index}】twitter已绑定")

            link_tg = False
            if record.get("link_tg") == "1":
                link_tg = True
                logger.success(f"【{self.index}】telegram已绑定")
            elif record.get("link_tg") == "-1":
                link_tg = True
                logger.info(f"【{self.index}】telegram未配置, 跳过...")
            else:
                telegram_name = (
                    next((item["externalName"] for item in data if item["name"] == "telegram"), None) if data else None
                )
                if telegram_name:
                    link_tg = True
                    logger.success(f"【{self.index}】telegram已绑定")
                    self.data_util.update(self.address, {"link_tg": "1", "tg_name": telegram_name})
                else:
                    link_tg = self.link_tg(tab)

            link_email = False
            if record.get("link_email", "") != "1":
                email = res.response.body.get("profile", {}).get("email", "")
                email_verified = res.response.body.get("profile", {}).get("emailVerified", False)
                if email and email_verified:
                    link_email = True
                    logger.success(f"【{self.index}】邮箱已绑定")
                    self.data_util.update(self.address, {"link_email": "1", "email": email})
                if email and not email_verified:
                    logger.info(f"【{self.index}】邮箱绑定但未验证")
                    if self.verify_email(tab):
                        link_email = True
                if not email:
                    link_email = self.link_email(tab)
            else:
                link_email = True
                logger.success(f"【{self.index}】邮箱已绑定")

            if link_x and link_dc and link_tg and link_email:
                self.data_util.update(self.address, {"status": "1"})
                logger.success(f"【{self.index}】社交四件套均已绑定成功")
                return True

            return False
        except Exception as e:
            logger.error(f"【{self.index}】绑定社交账号异常, error={str(e)}")
            return False

    def link_monad(self) -> bool:
        for i in range(3):
            try:
                if self.data_util.get(self.address).get("link_dc", "0") != "1":
                    logger.warning(f"【{self.index}】Discord未绑定，请先绑定再授权monad登录")
                    return False

                tab = self.browser_controller.page.new_tab("https://recognizer.monad.xyz/get-started")
                if get_element(tab, "x://a[@href='/get-started']/following-sibling::button", 5):
                    logger.success(f"【{self.index}】授权monad成功")
                    self.data_util.update(self.address, {"link_monad": "1"})
                    return True

                matrica_btn = get_element(tab, "x://main/section[1]//button", 10)
                if not matrica_btn:
                    logger.error(f"【{self.index}】未找到Matrica按钮")
                    continue

                matrica_btn.click()
                tab.wait.url_change(text="matrica.io/oauth2", timeout=10)

                approve_btn = get_element(tab, "x://button[.//span[text()='Approve']]", 10)
                if not approve_btn:
                    logger.error(f"【{self.index}】未找到Approve按钮")
                    continue

                approve_btn.click()
                if not tab.wait.url_change(text="recognizer.monad.xyz", timeout=10):
                    logger.error(f"【{self.index}】点击Approve未跳转到monad页面")
                    continue

                if get_element(tab, "x://a[@href='/get-started']/following-sibling::button", 5):
                    logger.success(f"【{self.index}】授权monad成功")
                    self.data_util.update(self.address, {"link_monad": "1"})
                    return True
            except Exception as e:
                logger.error(f"【{self.index}】授权monad失败: {e}")

        return False

    def task(self) -> bool:
        """
        执行主要任务

        Returns:
            bool: 任务是否执行成功
        """
        try:
            logger.info(f"【{self.index}】开始执行任务")

            # 首先确保已登录
            if not self.login():
                logger.error(f"【{self.index}】登录失败，无法执行任务")
                return False

            tab = self.browser_controller.page.latest_tab

            if not self.register(tab):
                logger.error(f"【{self.index}】注册失败")
                return False

            self.link_social()

            if self.data_util.get(self.address).get("link_monad", "0") == "1":
                logger.info(f"【{self.index}】已经授权monad登录")
                return True

            return self.link_monad()
        except Exception as e:
            logger.error(f"【{self.index}】任务执行失败: {e}")
            return False
        finally:
            if self.browser_controller.page:
                self.browser_controller.close_page()


def _get_data_util() -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """
    data_dir = os.path.join(get_project_root_path(), "examples", DATA_DIR_NAME)
    csv_path = os.path.join(data_dir, CSV_FILE_NAME)
    return DataUtil(csv_path)


def _run_task(browser_type: BrowserType, index: int) -> bool:
    """
    运行单个任务

    Args:
        browser_type: 浏览器类型
        index: 浏览器索引

    Returns:
        bool: 任务是否成功
    """
    matrica = None
    try:
        data_util = _get_data_util()
        matrica = Matrica(browser_type, str(index), data_util)
        if (
            matrica.data_util.get(matrica.address).get("status", "0") == "1"
            and matrica.data_util.get(matrica.address).get("link_monad", "0") == "1"
        ):
            logger.info(f"【{index}】已完成全部任务")
            return True
        return matrica.task()
    except Exception as e:
        logger.error(f"【{index}】执行任务异常: {e}")
        return False


@click.group()
def cli():
    """Matrica自动化工具命令行接口"""
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=DEFAULT_BROWSER_TYPE_ENUM,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    """执行完整任务流程"""
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        data_util = _get_data_util()

        def process_task(idx: int) -> bool:
            """处理单个任务"""
            try:
                matrica = Matrica(type, str(idx), data_util)
                if (
                    matrica.data_util.get(matrica.address).get("status", "0") == "1"
                    and matrica.data_util.get(matrica.address).get("link_monad", "0") == "1"
                ):
                    logger.info(f"【{idx}】已完成全部任务")
                    return True
                return matrica.task()
            except Exception as e:
                logger.error(f"账号 {idx} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=TASK_TIMEOUT,
            retries=MAX_RETRIES,
            interval=RETRY_INTERVAL,
            task_name=f"Matrica-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)

        # 统计结果
        success_count = sum(1 for result in results if result)
        total_count = len(results)
        logger.info(f"任务执行完成: 成功 {success_count}/{total_count}")

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    # 使用示例:
    # python3 examples/monad/matrica.py login -t ads -i 1-10
    # python3 examples/monad/matrica.py run -t ads -i 1-10 -w 3
    # python3 examples/monad/matrica.py test -t ads -i 1
    cli()
    # _run_task(BrowserType.ADS, 9)
