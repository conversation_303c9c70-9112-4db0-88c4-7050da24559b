from loguru import logger
from retry import retry
from src.browsers import BrowserType
from src.utils.browser_config import BrowserConfigInstance
from dotenv import load_dotenv
import os
import random
import requests
from src.utils.ai import AI
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import json
import time
import string
load_dotenv()

PROXY_URL = os.getenv("PROXY_URL")


def get_kookeey_url():
    random_session = ''.join(random.choices(string.digits, k=8))
    proxy_url = PROXY_URL.replace("*", random_session)
    return proxy_url

def get_proxy():
    for _ in range(10):
        proxy = get_kookeey_url()
        proxies = {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }
        try:
            ip_check_url = "https://ipinfo.io/json"
            response = requests.get(ip_check_url, proxies=proxies, timeout=10)
            data = response.json()
            logger.info(
                f"获取代理成功: IP: {data.get('ip')}, 国家: {data.get('country')}, 地区: {data.get('region')}")
            break
        except Exception as e:
            logger.error(f"获取代理失败: {str(e)}")
        else:
            raise Exception("所有代理测试失败，终止请求！")
    return proxies

class StoryGaiaDomainChat(BrowserConfigInstance):
    """GaiaNet跟Domain聊天任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_api_url = os.getenv("OPENAI_API_URL")
        self.openai_model = os.getenv("OPENAI_MODEL")
        self.gaia_api_key = None


    def get_proxies(self):
        if not self.browser_config.proxy:
            return get_proxy()
        else:
            return {
                "http": self.browser_config.proxy,
                "https": self.browser_config.proxy
            }
    def get_gaianet_domain(self, amount=1):
        """获取gaianet domain列表"""
        url = "https://api.gaianet.ai/api/v1/network/domains/"

        proxies = self.get_proxies()
        response = requests.get(url, proxies=proxies)

        if response.status_code != 200:
            logger.error(f"{self.browser_id} 获取gaianet domain列表失败")
            return None
        domains = response.json()['data']['objects']
        # 要过滤掉statistics.total_running_nodes == 0的domain
        domains = [domain for domain in domains if domain['statistics']['total_running_nodes'] > 0]
        domains = random.sample(domains, amount)
        fqdns = [domain['fqdn'] for domain in domains]
        # fqdns = ["dogss.gaia.domains" for _ in range(amount)]
        logger.info(f"{self.browser_id} 随机选中的domain: {fqdns}")
        return fqdns
    
    def generate_questions(self, current_amount=1, amount_min=5, amount_max=10):
        # 从文件story/question.txt中读取问题，随机选account_min到account_max个
        with open("story/questions.txt", "r") as f:
            questions = f.readlines()
        # 返回二维数组，每个子数组中包含amount_min到amount_max个问题
        questions = [random.sample(questions, random.randint(amount_min, amount_max)) for _ in range(current_amount)]
        return questions


    def chat(self, fqdn: str, questions: list[str]):
        """聊天"""
        url = f"https://{fqdn}/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.gaia_api_key}",
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        for question in questions:
            data = {
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": question}
                    # {"role": "user", "content": f"{question} please use 10000words to answer this question and use more words as most as possible"}
                ]
            }
            try:
                logger.info(f"{self.browser_id} 跟{fqdn}聊天: {question}")
                proxies = self.get_proxies()
                response = requests.post(url, headers=headers, json=data, proxies=proxies, timeout=(30,120))
                if response.status_code != 200:
                    logger.error(f"{self.browser_id} 跟{fqdn}聊天失败: {response.status_code}")
                elif response.status_code == 402 or response.status_code == 429:
                    logger.error(f"{self.browser_id} 跟{fqdn}聊天失败: {response.status_code}")
                    return False
                else:
                    logger.success(f"{self.browser_id} 跟{fqdn}聊天结果: {response.status_code}")
            except Exception as e:
                logger.error(f"{self.browser_id} 跟{fqdn}聊天失败: {e}")
            finally:
                time.sleep(random.randint(1, 3))

    @retry(tries=3, delay=1)
    def task(self, current_amount=5, amount_min=5, amount_max=10) -> bool:
        """获取gaia-api-key"""
        with open("story/gaianet.json", "r") as f:
            self.gaia_api_key = json.load(f)[self.browser_id]
        if not self.gaia_api_key:
            logger.error(f"{self.browser_id} 获取gaia-api-key失败")
            return False
        
        # 用requests从https://api.gaianet.ai/api/v1/network/domains/获取gaianet domain列表，随机选一个聊天
        # 获取gaianet domain列表，requests请求返回的结果格式是
        # {code: 0
        # data: 
        # {objects: [,…]}
        # meta: {}
        # msg: "OK"
        # ts: 1740722671
        # }
        # 需要解析data中的objects列表，随机选一个domain。objects格式是
        # {
        #     "id": 1,
        #     "statistics": {
        #         "total_running_nodes": 945,
        #         "throughputs": 1809858557,
        #         "total_earned": 12668499.1001
        #     },
        #     "fqdn": "llama.gaia.domains",
        #     "quick_questions": [],
        #     "display_name": "Llama",
        #         "avatar_url": "https://res.gaianet.ai/domain/avatars/89bf6f0d-4185-4770-9bd0-1cbae692b2cd_3ho5DSN.jpg",
        #         "gdn": "llama",
        #         "domain_name": "gaia.domains",
        #         "system_prompt": "You're a helpful assistant.",
        #         "description": "Llama is a collection of Llama 3.2 3B models.",
        #         "hosting_type": "gaia-domain-cloud",
        #         "approval_method": "Automation",
        #         "llm_requirements": "Llama-3.2-3B-Instruct",
        #         "gguf_sha256": null,
        #         "server_configuration": "starter",
        #         "domain_tier": "DIAMOND DOMAIN",
        #         "initial_stake_tokens": 10500,
        #         "owner_id": "89bf6f0d-4185-4770-9bd0-1cbae692b2cd",
        #         "owner_wallet_address": "******************************************",
        #         "status": "",
        #         "region": "",
        #         "created_at": "2024-11-20T13:03:19.633787Z",
        #         "updated_at": "2025-02-14T08:28:30.063769Z"
        #     }
        # 需要从data中选择一个fqdn，随机选一个
        fqdns = self.get_gaianet_domain(amount=current_amount)
        if not fqdns:
            logger.error(f"{self.browser_id} 获取gaianet domain列表失败")
            return False
        
        # 从文件story/questions.txt中读取问题，随机选account_min到account_max个
        questions_list = self.generate_questions(current_amount=current_amount, amount_min=amount_min, amount_max=amount_max)
        # 配置代理
        os.environ["HTTP_PROXY"] = self.browser_config.proxy
        os.environ["HTTPS_PROXY"] = self.browser_config.proxy
        # current_amount个并发开聊，每个并发使用一个fqdn和一组questions
        with ThreadPoolExecutor(max_workers=current_amount) as executor:
            futures = [executor.submit(self.chat, fqdn, questions) for fqdn, questions in zip(fqdns, questions_list)]
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=1200)
                    # logger.info(f"{self.browser_id} 聊天结果: {result}")
                except Exception as e:
                    logger.error(f"{self.browser_id} 聊天失败: {e}")
        
        logger.success(f"{self.browser_id} 完成任务")
        return True