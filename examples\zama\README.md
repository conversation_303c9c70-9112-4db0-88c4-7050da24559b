# Zama Discord 自动加群脚本

## 项目介绍

这是一个专用于Zama服务器的Discord自动加群脚本，支持自动处理验证码、加入服务器、获取角色等功能。

## 主要功能

- 🔐 **自动验证码处理**：使用YOLO模型识别验证码
- 🤖 **自动加群**：自动加入Zama Discord服务器
- 🎭 **角色获取**：自动获取服务器角色
- 📝 **数据记录**：记录加群状态到CSV文件
- 🔄 **重试机制**：失败时自动重试
- 📊 **日志记录**：详细的日志和失败记录

## 安装和依赖

### 系统要求
- Python 3.8+
- Chrome/Brave浏览器
- 足够的磁盘空间用于存储验证码图片

### 依赖安装
```bash
pip install ultralytics
```

### 主要依赖
- DrissionPage：浏览器自动化
- loguru：日志记录
- click：命令行界面
- PIL：图像处理
- requests：网络请求

## 使用方法

### 1. 命令行使用

#### 加入服务器
```bash
# 使用Chrome浏览器，浏览器序号为1
python examples/zama/discord.py join -t chrome -i 1

# 使用Brave浏览器，浏览器序号为3
python examples/zama/discord.py join -t brave -i 3

# 等待验证码处理
python examples/zama/discord.py join -t chrome -w -i 1

# 不关闭浏览器
python examples/zama/discord.py join -t chrome -c -i 1
```

#### 退出服务器
```bash
# 退出指定浏览器的服务器
python examples/zama/discord.py leave -t chrome -i 1
```

### 2. 参数说明

#### join命令参数
- `-t, --type`：浏览器类型 (chrome/brave)，默认使用配置文件中的类型
- `-i, --index`：浏览器序号，必填
- `-w, --wait`：是否等待验证码，默认True
- `-c, --close`：是否关闭浏览器，默认True

#### leave命令参数
- `-t, --type`：浏览器类型
- `-i, --index`：浏览器序号

### 3. 程序化调用

```python
from examples.zama.discord import _join_dc_server
from src.browsers import BrowserType

# 加入服务器
fail_servers = _join_dc_server(
    type=BrowserType.CHROME,
    index="1",
    need_wait_captcha=True,
    close_page=True
)

# 检查结果
if fail_servers:
    print(f"失败的服务器: {fail_servers}")
else:
    print("所有服务器加入成功")
```

## 文件结构

```
examples/zama/
├── discord.py          # 主脚本文件
├── README.md           # 使用说明文档
├── png/                # 验证码图片存储目录
├── cap.log             # 验证码失败记录文件
└── zama_chrome.csv     # 数据记录文件（按浏览器类型）
```

## 配置说明

### 浏览器配置
脚本会自动读取浏览器配置文件，支持Chrome和Brave浏览器。

### 服务器配置
当前支持的服务器：
- Zama服务器 (ID: 1287736665103798433)

### 验证码配置
- 模型文件：`examples/cap/model/dc_zama.pt`
- 图片尺寸：320x320
- 置信度阈值：0.5

## 数据记录

### CSV文件格式
脚本会在`examples/zama/`目录下创建CSV文件，记录加群状态：
- `zama_chrome.csv`：Chrome浏览器数据
- `zama_brave.csv`：Brave浏览器数据

### 记录字段
- `index`：浏览器序号
- `type`：浏览器类型
- `address`：钱包地址
- `Discord zama`：Zama服务器加入状态

## 日志系统

### 日志文件
- `logs/zama_discord.log`：主日志文件
- `examples/zama/cap.log`：验证码失败记录

### 日志级别
- SUCCESS：成功操作
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息

## 故障排除

### 常见问题

#### 1. 验证码识别失败
- 检查模型文件是否存在
- 确认图片质量
- 查看`cap.log`文件中的失败记录

#### 2. 浏览器启动失败
- 确认浏览器已安装
- 检查浏览器配置文件
- 确认浏览器版本兼容性

#### 3. 网络连接问题
- 检查网络连接
- 确认代理设置
- 验证Discord服务状态

#### 4. 权限问题
- 确认脚本有文件读写权限
- 检查目录创建权限

### 调试方法

#### 启用详细日志
```python
import loguru
loguru.logger.add("debug.log", level="DEBUG")
```

#### 手动测试验证码
```python
from examples.cap.yolo_captcha_solver import YOLOCaptchaSolver

solver = YOLOCaptchaSolver(
    model_path="examples/cap/model/dc_zama.pt",
    img_size=320,
    conf_threshold=0.5
)
result = solver.predict("path/to/captcha.png")
print(f"识别结果: {result}")
```

## 注意事项

1. **账号安全**：请确保使用安全的账号，避免频繁操作
2. **验证码处理**：验证码识别可能不是100%准确，失败时会自动重试
3. **网络环境**：建议使用稳定的网络环境
4. **浏览器版本**：确保浏览器版本与DrissionPage兼容
5. **数据备份**：定期备份CSV数据文件

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的加群功能
- 集成验证码识别
- 添加重试机制

## 技术支持

如有问题，请查看：
1. 日志文件中的错误信息
2. 验证码失败记录
3. 浏览器控制台输出

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和Discord服务条款。 