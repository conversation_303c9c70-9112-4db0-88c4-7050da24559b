import random

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3
from src.evm.erc20_utils import send_transaction, get_transaction
from src.utils.secure_encryption import SecureEncryption


class StoryPosterMint(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "poster_mint"
        self.wallet_address = self.browser_config.evm_address

        pk = self.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)

        self.private_key = pk

        self.poster_contract_address = "******************************************"
        self.poster_contract_abi = [
            {
                "inputs": [
                    {"internalType": "address", "name": "to", "type": "address"},
                    {
                        "internalType": "string",
                        "name": "nftMetadataURI",
                        "type": "string",
                    },
                ],
                "name": "mint",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            }
        ]
        self.uris = [
            "ipfs://bafkreifad4xcmmoa6higmxlm7anffowyjv7cf64f4cdfkwlddmosjxllhy",
            "ipfs://bafkreieimm5d2iczpck3rfsucvcbs2d3h7uwpzgwzmnn3fae6xiagfhhhm",
            "ipfs://bafkreifhauvffj6b4qzkh4pjpksvipooxihxkhx3bfqsvkbovlybu7yksi",
            "ipfs://bafkreibagk6suols7ubs7oxqfaviuiz2uwhhmcyxukaqztyhodtzen222u",
            "ipfs://bafkreiglk6csw5lloiuoq7afnmciyvaab4pxyn3uiki7upmu4ka6u4okmu",
            "ipfs://bafkreiajglfh3ypwwpd4wqxns6mxzrrbky3fztl5jhtuvjmfc6vp7bm6bm",
            "ipfs://bafkreifm3bnkdieq4g3qrhqmvm7r44u7q7w53srzlatcf4mskb4uu6blmi",
            "ipfs://bafkreiaakzcga3icj4tke4j7nvetdw5iplhhgkw7b2tbd7ax65zgtetv3q",
            "ipfs://bafkreifeadt2isuzdz2h6ajcaevzcijraatbo7kiifnnf7yw6ejj2mwju4",
            "ipfs://bafkreier54lylugjmchymbu3ukccx53hvjksxrbce2ia6jkmpuzoy4u4lm",
            "ipfs://bafkreihkc6zydeau7jtcw2tk2sxj5xd55n3reblet5tithyzrfzocw7yna",
            "ipfs://bafkreigokk3tv5hktdjw5qbffkguavjibhamdkxclzx222ht2sm4kkvxda",
            "ipfs://bafkreidgrscjc4qa4yjw2yefyroggst2yrj5atc4hehf6gsba3yps5pmhm",
            "ipfs://bafkreigdtfly4eg7zw27ledyz3zzjjfxrjg4l3slvbwfk4nraxqhzxpg3e",
            "ipfs://bafkreiazfsteminxdwpzbvol2o76xuijpl4p3ipfzbf63z2tl35elm3a7q",
            "ipfs://bafkreiee62y6pilcbvdvevo4sc5tksyjv2lz3q5k4ojcqpmy27snexbj2m",
            "ipfs://bafkreid5l7y2tjyojtn3sjqzgcvu6o66ubpaitvbuzowc7xamwce4jcr2i",
            "ipfs://bafkreib35uajkjh3d47cvwvrovghrebljhpkn3mqunqtue6v3trvcm4k2m",
            "ipfs://bafkreibbm7u5nqkysb6cwq76wcidxrunwbebizay2iktkslmfrx7bgtxky",
            "ipfs://bafkreiaeg7x6hawg73k3z5jyhmy2j6njwovc6u45x43w4vrweteadrfjsy",
            "ipfs://bafkreihfkkba2rzokdwjkleganvbyzcqtujs2tqdqwwvoneofus4lfe6mu",
            "ipfs://bafkreieevntnanmxvedwudgvpgx6fn34siolcdq4mfj5au27xygkidxzgm",
            "ipfs://bafkreia2upulxwigkhfssxtnmcqhhb2o7x7lx6vuefrajzddxln5razgxi",
            "ipfs://bafkreif2jmuogvirjpwf3drt35h2nv3kj5lunptwm5olov2riigowyvbxa",
            "ipfs://bafkreie3std6vcdwzikpv6g6rtfvkthz2pzy5mk73jqnqzcncfg65tx4hi",
            "ipfs://bafkreic6oux3abpsjdm7ts4wi26h7a2rixxboccu4rctxuztbqo4q4wxre",
            "ipfs://bafkreif3bvlgbiajia7hhbyfyi55jmzqvmms7u3wczz6haxjymufgws754",
            "ipfs://bafkreibmv3rvcp3ydfdoxcw5uw4edtmuoh4jokx3unkzwc3e37kst4kd3m",
            "ipfs://bafkreievyqt7gyxdzawpwrfbkb3owdt4t6a7csbkmlfgdwz7btqkeidh4q",
            "ipfs://bafkreih4pjnnxf3gtsch6xmjpylp2uffg3shhabikzyqtwbwvpnh2p26hy",
            "ipfs://bafkreibxvvdhadpalzep5nvu6taa46mljaridghgbglbzscehss5mx7dsm",
            "ipfs://bafkreihki6fsg6wdabcutazl4oyoqn4juyzyr4kllbpzu3pv4hyopit77q",
            "ipfs://bafkreicbfmswjn2sbmb2hfdo5mdam7ul24zvugyg7ze22ntffwdeubik2q",
            "ipfs://bafkreidoiwu52xahqb3i2yhjgvd47ef2sy3tke7wysacwosemdhrarsd5q",
            "ipfs://bafkreigt4pqzwhrbpngilrp4uofok6ywota4raw2xqjvxeanbfwz4jigmq",
            "ipfs://bafkreiagrkn72y6rkgrnzbchks57psm3zie3thdfu5uxcsggeqcdsmqb5i",
            "ipfs://bafkreigsh2qkjzn6fkjx65vfn4ubpsbyeolo2oaka6u7yxwknicrvg4uta",
            "ipfs://bafkreiagmtwk4tvnq7kcp6agchrdod6zkbqlnmateflu7p5gs472v55uhe",
            "ipfs://bafkreifvt5wmfewqzs2p7537vzosjy6f3mvm42blrpchendlrt3ux4af2u",
            "ipfs://bafkreicdjav3d6fnxlbcpvdyhzeuufvtmwm5rb24aqz2bwgwd5y743ptu4",
            "ipfs://bafkreiho7thb2oq4z2ka6f424tc3qe2gie36re77zrx6ajvrpnutbanydm",
            "ipfs://bafkreicutrsrmvgkmu7pduoi6voyjcdxwjt7oxovhinwa3wenvo43dz3xe",
        ]

    def _mint(self) -> bool:
        logger.info("[INFO] 执行mint")
        # 随机生成uri
        uri = random.choice(self.uris)
        try:
            # 构建交易
            tx_params = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }
            # 执行合约函数
            poster_contract = self.web3.eth.contract(
                address=self.poster_contract_address, abi=self.poster_contract_abi
            )
            mint_function = poster_contract.functions.mint(self.wallet_address, uri)
            # 执行交易,需要先模拟交易执行
            # 先用 call() 检查交易是否会成功
            try:
                mint_function.call({"from": self.wallet_address})
            except Exception as e:
                logger.info(f"[INFO] mint 交易失败")
                return False

            transaction = get_transaction(
                self.web3, tx_params, mint_function, gas_adjustment_factor=1
            )
            if not transaction:
                logger.error(f"[ERROR] 构建交易失败")
                return False
            # 如果交易成功，则执行交易
            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] mint 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] mint 异常: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 poster mint 任务"""
        try:
            logger.info(f"[INFO] 开始执行 {self.browser_id} 的 poster mint 任务")
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            logger.info(f"[INFO] 使用代理: {proxy}")
            self.web3 = get_web3(
                rpc_url, proxy, user_agent=self.browser_config.user_agent
            )
            if self.web3 is None:
                return False
            logger.info(f"[INFO] 获取web3实例成功")

            if not self._mint():
                logger.error(f"[ERROR] {self.browser_id} poster mint 任务失败")
                return False

            logger.success(f"[SUCCESS] {self.browser_id} poster mint 任务执行完成")
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False

    def close(self):
        pass
