from dataclasses import dataclass


# 数据类用于统一管理配置
@dataclass
class BrowserConfig:
    """浏览器配置信息."""

    id: str
    browser_id: str
    mnemonic: str | None = None
    evm_address: str | None = None
    evm_private_key: str | None = None
    sol_private_key: str | None = None
    dc_id: str | None = None
    dc_username: str | None = None
    dc_token: str | None = None
    dc_user: str | None = None
    dc_password: str | None = None
    dc_two_fa: str | None = None
    dc_backup_code: str | None = None
    dc_email: str | None = None
    dc_email_password: str | None = None
    dc_proxy_email: str | None = None
    x_token: str | None = None
    x_user: str | None = None
    x_password: str | None = None
    x_two_fa: str | None = None
    x_backup_code: str | None = None
    x_email: str | None = None
    x_email_password: str | None = None
    x_proxy_email: str | None = None
    x_is_suspended: str | None = None
    tg_cellphone: str | None = None
    tg_user: str | None = None
    tg_password: str | None = None
    tg_email: str | None = None
    tg_email_password: str | None = None
    tg_proxy_email: str | None = None
    email: str | None = None
    email_password: str | None = None
    email_imap4_pwd: str | None = None
    email_two_fa: str | None = None
    proxy_email: str | None = None
    recovery_email: str | None = None
    recovery_email_pwd: str | None = None
    recovery_proxy_email: str | None = None
    email_is_suspended: str | None = None
    user_agent: str | None = None
    proxy: str | None = None
    region: str = ""
    client_id: str = ""
