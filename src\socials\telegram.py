import random
import re
from time import sleep

from loguru import logger

from src.utils.common import generate_login_name
from src.utils.element_util import get_element, get_elements, get_frame

from .base import BaseSocial


class Telegram(BaseSocial):
    def __init__(self, id, page) -> None:
        super().__init__(id, page)
        self._home_url = "https://web.telegram.org/a/"

    def join_channel(self, tg_group_name):
        # 加入群组
        try:
            # 目标地址
            tg_join_url_dest_a = f"https://web.telegram.org/a/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3D{tg_group_name}"
            tg_join_url_dest_k = f"https://web.telegram.org/k/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3D{tg_group_name}"

            new_tab_a = self._page.new_tab(tg_join_url_dest_a)
            join = new_tab_a.ele("x://button[contains(.,'Join')]", timeout=10)
            if join:
                join.click()
                sleep(5)
                join = new_tab_a.ele("x://button[contains(.,'Join')]", timeout=2)
                if not join:
                    logger.success(f"{self.id} 加入tg群组成功!")
                else:
                    logger.error(
                        f"{self.id} 加入tg群组失败， 请在浏览器中手动打开地址手动加群：{tg_join_url_dest_a}"
                    )
                new_tab_a.close()
            else:
                new_tab_k = self._page.new_tab(tg_join_url_dest_k)
                subscribe = new_tab_k.ele(
                    "x://button[contains(.,'SUBSCRIBE')]", timeout=10
                )
                if subscribe:
                    subscribe.click()
                    sleep(5)
                    subscribe = new_tab_k.ele(
                        "x://button[contains(.,'SUBSCRIBE')]", timeout=2
                    )
                    if not subscribe:
                        logger.success(f"{self.id} 加入tg群组成功!")
                    else:
                        logger.error(
                            f"{self.id} 加入tg群组失败， 请在浏览器中手动打开地址手动加群：{tg_join_url_dest_k}"
                        )
                new_tab_k.close()
        except Exception as e:
            logger.error(f"{self.id} 加入tg群组失败: {e}")

    @staticmethod
    def _set_unique_username(name, username_input, tab):
        unique_name = generate_login_name(name)
        username_input.input("", True)
        username_input.input(f"{unique_name}\n", True)
        sleep(2)
        while get_element(
            tab, "x://label[text()='This username is already taken.']", timeout=2
        ):
            year = random.randint(1980, 2005)
            username_input.input("", True)
            username_input.input(f"{unique_name}{year}\n", True)
            sleep(2)

    def update_profile(self, name):
        """
        设置/修改name、username
        先用name的值去设置Username；如果重复，则在username后面设置(1980,2005)之间随机数字
        :return:
        """
        tab = self._page.new_tab(self._home_url)
        sleep(2)
        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:

            # 点击左侧菜单
            sidebar = get_element(tab, "x://button[@aria-label='Open menu']", timeout=5)
            if not sidebar:
                logger.error(
                    f"【{self._id}】 {name} 修改用户资料失败，请检查网络是否正常或电报账号已正常登录"
                )
                return False
            sidebar.click()
            sleep(2)

            # 点击Setting
            try:
                get_element(
                    tab,
                    "x://div[@class='MenuItem compact' and contains(., 'Settings')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 {name} 修改profile时未找到Settings元素，retry..."
                )
                pass

            try:
                get_element(
                    tab, "x://button[@aria-label='Edit profile']", timeout=5
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 {name} 修改profile时未找到Edit profile元素，retry..."
                )
                pass

            try:
                get_element(
                    tab, "x://input[@aria-label='First name (required)']", timeout=3
                ).input(name, True)
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 {name} 修改profile时未找到First name元素，retry..."
                )
                pass

            try:
                get_element(
                    tab, "x://input[@aria-label='Last name (optional)']", timeout=5
                ).input("", True)
            except Exception:
                pass

            try:
                username_i = get_element(
                    tab, "x://input[@aria-label='Username']", timeout=3
                )
                self._set_unique_username(name, username_i, tab)
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 {name} 修改profile时未找到Username元素，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[@aria-label='Save']", timeout=5).click()
                sleep(1)
                logger.success(f"【{self._id}】 {name}用户名设置成功")
                return True
            except Exception:
                logger.warning(
                    f"【{self._id}】 {name} 修改profile时未找到Save元素，retry..."
                )
                pass

            logger.warning(
                f"【{self._id}】 {name} 第 {retry_count} 次修改profile失败，retry..."
            )
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

    def update_password(self, current_password, new_password):
        """
        修改密码
        :param current_password: 当前密码
        :param new_password: 新密码
        :return:
        """
        tab = self._page.new_tab(self._home_url)
        sleep(2)

        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:

            # 点击左侧菜单
            sidebar = get_element(tab, "x://button[@aria-label='Open menu']", timeout=5)
            if not sidebar:
                logger.error(
                    f"【{self._id}】 修改密码失败，请检查网络是否正常或电报账号已正常登录"
                )
                return False
            sidebar.click()
            sleep(2)

            # 点击Setting
            try:
                get_element(
                    tab,
                    "x://div[@class='MenuItem compact' and contains(., 'Settings')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self._id}】 修改密码时未找到Settings元素，retry...")
                pass

            # 点击Privacy and Security
            try:
                get_element(
                    tab,
                    "x://div[@class='ListItem-button' and contains(., 'Privacy and Security')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】修改密码时未找到Privacy and Security元素，retry..."
                )
                pass

            # 点击Two-Step Verification
            try:
                get_element(
                    tab,
                    "x://div[@class='multiline-menu-item' and contains(., 'Two-Step Verification')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改密码时未找到Two-Step Verification元素，retry..."
                )
                pass

            # 点击Change Password
            try:
                get_element(
                    tab,
                    "x://div[@class='ListItem-button' and contains(., 'Change Password')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改密码时未找到Change Password元素，retry..."
                )
                pass

            # 当前密码
            # 点击Change Password
            try:
                pwd_input = get_elements(tab, "x://input[@type='password']", timeout=5)[
                    0
                ]
                pwd_input.input(current_password, True)
                sleep(1)
                get_elements(tab, "x://button[text()='Next']", timeout=5)[0].click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改密码时未找到Change Password元素，retry..."
                )
                pass

            # 修改后的密码
            try:
                pwd_input = get_elements(tab, "x://input[@type='password']", timeout=5)[
                    1
                ]
                pwd_input.input(new_password, True)
                sleep(1)
                get_elements(tab, "x://button[text()='Next']", timeout=5)[1].click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改密码时未找到Change Password元素，retry..."
                )
                pass

            # 确认密码
            try:
                pwd_input = get_elements(tab, "x://input[@type='password']", timeout=5)[
                    2
                ]
                pwd_input.input(new_password, True)
                sleep(1)
                get_elements(tab, "x://button[text()='Next']", timeout=5)[2].click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改密码时 操作确认密码时发生异常，retry..."
                )
                pass

            # 跳过密码提示词
            try:
                get_element(
                    tab,
                    "x://button[@class='Button default primary text has-ripple' and contains(., 'Skip')]",
                    timeout=5,
                ).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self._id}】 修改密码时未找到Skip元素，retry...")
                pass

            try:
                if get_element(
                    tab, "x://button[text()='Return to Settings']", timeout=5
                ):
                    logger.success(f"【{self._id}】 密码修改成功")
                    return True
            except Exception:
                logger.warning(f"【{self._id}】 修改密码时未找到Skip元素，retry...")
                pass

            logger.warning(f"【{self._id}】 第 {retry_count} 次修改密码失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

    def update_recovery_mail(self, password, recovery_mail, recovery_mail_pwd, recovery_proxy_mail=""):
        tab = self._page.new_tab(self._home_url)
        sleep(2)
        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:

            # 点击左侧菜单
            sidebar = get_element(tab, "x://button[@aria-label='Open menu']", timeout=5)
            if not sidebar:
                logger.error(
                    f"【{self._id}】 修改邮箱失败，请检查网络是否正常或电报账号已正常登录"
                )
                return False
            sidebar.click()
            sleep(2)

            # 点击Setting
            try:
                get_element(
                    tab,
                    "x://div[@class='MenuItem compact' and contains(., 'Settings')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self._id}】 修改邮箱时未找到Settings元素，retry...")
                pass

            # 点击Privacy and Security
            try:
                get_element(
                    tab,
                    "x://div[@class='ListItem-button' and contains(., 'Privacy and Security')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改邮箱时未找到Privacy and Security元素，retry..."
                )
                pass

            # 点击Two-Step Verification
            try:
                get_element(
                    tab,
                    "x://div[@class='multiline-menu-item' and contains(., 'Two-Step Verification')]",
                    timeout=5,
                ).click()
                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】修改邮箱时未找到Two-Step Verification元素，retry..."
                )
                pass

            try:
                # 点击 Set Recovery Email
                get_element(
                    tab,
                    "x://div[@class='ListItem-button' and contains(., 'Set Recovery Email')]",
                    timeout=5,
                ).click()

                sleep(1)
            except Exception:
                logger.warning(
                    f"【{self._id}】 修改邮箱时未找到Set Recovery Email元素，retry..."
                )
                pass

            try:
                # 输入密码
                get_element(tab, "x://input[@id='sign-in-password']", timeout=5).input(
                    password, True
                )
                if get_element(tab, "x://label[text()='Invalid Password']", timeout=2):
                    logger.warning(f"【{self._id}】修改邮箱时 密码输入错误，请检查密码")
                    return False
                get_element(tab, "x://button[text()='Next']", timeout=5).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self._id}】 修改邮箱时未找到输入密码元素，retry...")
                pass

            try:
                # 输入邮箱, 优先使用代理邮箱
                email = recovery_proxy_mail or recovery_mail
                get_element(
                    tab, "x://input[@aria-label='Recovery Email']", timeout=5
                ).input(email, True)
                sleep(1)
                get_element(tab, "x://button[text()='Continue']", timeout=5).click()
            except Exception:
                logger.warning(f"【{self._id}】 修改邮箱时 输入邮箱操作错误，retry...")
                pass

            code = self.get_telegram_code(
                recovery_mail, recovery_mail_pwd, recovery_proxy_mail
            )
            if not code:
                logger.error(f"【{self._id}】修改邮箱时未接受到验证码，请检查邮箱")
                return False

            try:
                get_element(
                    tab, "x://input[@aria-label='Your Email Code']", timeout=5
                ).input(code, True)
                sleep(5)
            except Exception:
                logger.warning(f"【{self._id}】修改邮箱时 输入验证码发生异，retry...")
                pass

            if get_element(tab, "x://button[text()='Return to Settings']", timeout=5):
                logger.success(f"【{self._id}】 邮箱修改成功")
                return True

            logger.warning(f"【{self._id}】 第 {retry_count} 次修改邮箱失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

    def import_wallet(self, mnemonic):
        tab = self._page.new_tab(f"{self._home_url}#1985737506")
        sleep(2)
        try:
            get_element(tab, "x://input[@id='telegram-search-input']", timeout=3).input(
                "@wallet\n"
            )
        except Exception:
            logger.error(
                f"【{self._id}】 导入钱包失败，请检查网络是否正常或电报账号已正常登录"
            )
            return False

        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:

            try:
                get_element(
                    tab, "x://input[@id='telegram-search-input']", timeout=3
                ).input("@wallet\n")
            except Exception:
                logger.error(f"【{self._id}】 导入钱包失败，未找到搜索框, retry...")
                pass

            self._page.wait.ele_displayed("x://button[text()='START']", timeout=10)

            try:
                get_element(tab, "x://button[text()='START']", timeout=3).click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 START 按钮，retry..."
                )
                pass

            try:
                get_element(
                    tab, "x://button[contains(., 'Open Wallet')]", timeout=3
                ).click()
                sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Open Wallet 按钮，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[text()='Confirm']", timeout=3).click()
                sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Confirm 按钮，retry..."
                )
                pass

            try:
                get_element(tab, "x://input[@type='checkbox']", timeout=3).check(False)
                sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 checkbox，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[text()='Confirm']", timeout=3).click()
                sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】导入钱包出错，未找到 Confirm 按钮，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[text()='Confirm']", timeout=3).click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Confirm 按钮，retry..."
                )
                pass

            iframe = None
            try:
                iframe = get_frame(tab, 1, timeout=3)
            except Exception:
                logger.warning(f"【{self._id}】 导入钱包出错，未找到 iframe，retry...")
                pass

            try:
                get_element(
                    iframe, 'x://button[contains(., "Let\'s go")]', timeout=3
                ).click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Let's go 按钮，retry..."
                )
                pass

            tab.actions.move_to("x://button[@aria-label='More actions']")

            try:
                get_element(
                    tab, "x://button[@aria-label='More actions']", timeout=3
                ).click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 More actions按钮，retry..."
                )
                pass

            try:
                get_element(
                    tab,
                    "x://div[@class='MenuItem compact' and contains(., 'Settings')]",
                    timeout=3,
                ).click()
                sleep(2)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Settings按钮，retry..."
                )
                pass

            try:
                checkbox = get_element(
                    iframe,
                    "x://label[contains(., 'Show TON Space')]//input[@type='checkbox']",
                    timeout=3,
                )
                if checkbox and not checkbox.states.is_checked:
                    get_element(
                        iframe, "x://label[contains(., 'Show TON Space')]", timeout=3
                    ).check()
                    sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Show TON Space checkbox按钮，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[@aria-label='Back']", timeout=3).click()
                sleep(1.5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Back按钮，retry..."
                )
                pass

            try:
                get_element(
                    iframe,
                    "x://div[@color='subtitleText' and contains(., 'Start Exploring TON')]",
                    timeout=3,
                ).click()
                iframe.wait.ele_displayed(
                    "x://div[text()='Add Existing Wallet']", timeout=20
                )
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Start Exploring TON按钮，retry..."
                )
                pass

            try:
                get_element(
                    iframe, "x://div[text()='Add Existing Wallet']", timeout=3
                ).click()
                sleep(5)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Add Existing Wallet按钮，retry..."
                )
                pass

            try:
                inputs = get_elements(
                    iframe, "x://input[@type='text' and @class='Kndc']", timeout=3
                )
                mnemonics = mnemonic.split(" ")
                for index, e in enumerate(inputs):
                    item = mnemonics[index]
                    e.input(item)
                    sleep(0.1)
            except Exception as e:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 助记词输入框，retry..."
                )
                pass

            try:
                get_element(tab, "x://button[text()='Next']", timeout=3).click()
                iframe.wait.ele_displayed("x://div[text()='View Wallet']", timeout=15)
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 Next 按钮，retry..."
                )
                pass

            try:
                if get_element(iframe, "x://div[text()='View Wallet']", timeout=5):
                    logger.success(f"【{self._id}】 电报钱包导入成功")
                    return True
            except Exception:
                logger.warning(
                    f"【{self._id}】 导入钱包出错，未找到 View Wallet 按钮，retry..."
                )
                pass

            logger.warning(f"【{self._id}】 第 {retry_count} 次导入钱包失败，retry...")
            retry_count += 1
            try:
                get_element(tab, "x://button[@aria-label='Back']", timeout=3).click()
            except:
                pass
            try:
                get_element(tab, "x://button[@aria-label='Close']", timeout=3).click()
            except:
                pass

    def get_telegram_code(self, mail, mail_pwd, proxy_mail):
        """获取电报验证码"""
        from src.emails.imap4.email_client import EmailClient, SearchCriteria

        email_client = EmailClient(mail, mail_pwd)

        search_criteria = SearchCriteria(
            subject="Your Code",
            from_addr="Telegram",
            to=proxy_mail,
        )

        emails = email_client.search_emails_with_retry(search_criteria)
        if not emails:
            logger.error(f"{self._id} 未找到验证邮件")
            return None

        email_info = emails[0]
        to_email = email_info["to"]
        if proxy_mail.lower() not in to_email.lower():
            logger.error(f"{self._id} 验证邮件收件人不匹配: {to_email} != {proxy_mail}")
            return None

        subject = email_info.get("subject")
        code = re.search(r"Your Code - (\d{6})", subject)
        if code:
            return code.group(1)
        return None

    def login_by_password(self, username, password):
        pass

    def login_by_token(self, token):
        pass

    def login(self):
        pass
