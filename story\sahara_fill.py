import random
from loguru import logger
from time import sleep
from retry import retry
from src.utils.browser_config import BrowserConfigInstance
from src.browsers import BrowserType
from src.utils.ai import AI
from dotenv import load_dotenv
from DrissionPage import Chromium, ChromiumOptions
from faker import Faker
from src.browsers import AdsBrowser

load_dotenv()


class StorySaharaFill(BrowserConfigInstance):
    """Sahara Fill任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "sahara_fill"
        self.id = id
        self.type = type
        self.home_url = "https://hi.saharalabs.ai/get-started"
        self.question_1 = [
            "Contribute knowledge for AI training",
            "Use AI Solutions for Personal",
        ]
        self.question_2 = [
            "AI",
            "Math & natural sciences",
            "Writing, editing",
            "Social experience (dating, communication)",
            "Web3 & crypto",
            "Other",
        ]

    @retry(tries=3, delay=1)
    def task(self, clear_other_tabs: bool = True) -> bool:
        """执行Sahara Fill任务"""
        try:
            # 创建AI实例
            faker = Faker("en_US")
            name = faker.name()
            # 问题1从Contribute knowledge for AI training和Use AI Solutions for Personal随机选择一个
            question1 = random.choice(self.question_1)
            logger.info(f"{self.browser_id} 问题1: {question1}")
            if question1 == "Contribute knowledge for AI training":
                question2 = random.choice(self.question_2)
                system_prompt = f"""
                    你先通过链接https://saharalabs.ai/了解项目信息。你正在参与saharalabs.ai的waitlist表格填写。
                    它的第一个问题是What are you interested in? 
                    你选择了{question1}；
                    第二个问题是What type of knowledge would you like to contribute?，
                    你选择了{question2}。
                    它的最后一个问题是Anything else you'd like to known? 你需要做出回答：
                    1. 回答要简洁生动，不要超过400字
                    2. 你要先生成1000组回答，然后随机选取一个输出
                    """
            else:
                question2 = None
                system_prompt = f"""
                    你先通过链接https://saharalabs.ai/了解项目信息。你正在参与saharalabs.ai的waitlist表格填写。
                    它的第一个问题是What are you interested in? 
                    你选择了{question1}；
                    它的最后一个问题是Anything else you'd like to known? 你需要做出回答：
                    1. 回答要简洁生动，不要超过400字
                    2. 你要先生成1000组回答，然后随机选取一个输出
                    """
            ai = AI()
            response = ai.chat(
                message="请给出回答，英文版本, 输出里不要包含前后的双引号",
                system_prompt=system_prompt,
                temperature=2,
            )

            if self.type == BrowserType.CHROME:
                # 初始化浏览器
                options = ChromiumOptions()
                options.set_argument("--user-agent", self.browser_config.user_agent)
                if self.browser_config.proxy:
                    options.set_argument("--proxy-server", self.browser_config.proxy)
                tab = Chromium(addr_or_opts=options).latest_tab
                tab.set.window.max()
            elif self.type == BrowserType.ADS:
                ads_browser = AdsBrowser(
                    self.id, self.browser_config.browser_id, self.browser_config
                )
                tab = ads_browser.page.latest_tab
                tab.set.window.max()

            # 打开任务页面
            tab.get(self.home_url)

            # 网页填表
            first_name_input = tab.ele("x://input[@name='firstname']")
            last_name_input = tab.ele("x://input[@name='lastname']")
            email_input = tab.ele("x://input[@name='email']")
            first_name_input.input(name.split()[0])
            last_name_input.input(name.split()[1])
            email_input.input(self.browser_config.email)
            question1_checkbox = tab.ele(f"x://span[text()='{question1}']")
            question1_checkbox.click()
            if question2:
                question2_checkbox = tab.ele(f"x://span[text()='{question2}']")
                question2_checkbox.click()
            sleep(2)
            anything_else_input = tab.ele(
                'x://textarea[@placeholder="Anything else you\'d like us to know?"]'
            )
            anything_else_input.input(response)
            sleep(2)
            # agree to terms
            agree_to_terms_checkbox = tab.ele(
                "x://p[contains(text(), 'I agree to receive updates on my waitlist status')]"
            )
            agree_to_terms_checkbox.click()
            sleep(1)
            # 提交
            submit_button = tab.ele("x://input[@value='Submit']")
            submit_button.click()
            sleep(2)
            # 检查是否提交成功
            if tab.ele("x://h1[text()='Thank you!']"):
                logger.success(f"{self.browser_id} Sahara Fill任务完成")
                return True
            else:
                logger.error(f"{self.browser_id} Sahara Fill任务失败")
                return False

        except Exception as e:
            logger.error(f"{self.browser_id} Sahara Fill任务失败: {e}")
            return False
        finally:
            tab.close()
