import os
import random
from time import sleep

from loguru import logger
from .base import StoryBase
from src.browsers import BrowserType
from retry import retry
from typing import Optional
from .contract.wand_contract import WandContract
from src.utils.proxies import Proxies
from config import MAX_GAS_PRICE


def generate_random_amount() -> float:
    """生成随机金额，范围从配置文件读取，保留3位小数"""
    min_amount = float(os.getenv("MIN_AMOUNT", "0.001"))
    max_amount = float(os.getenv("MAX_AMOUNT", "0.009"))
    amount = random.uniform(min_amount, max_amount)
    return round(amount, 3)


class StoryWand(StoryBase):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "wand"
        self.home_url = "https://badge.wand.fi/"
        self.wallet_address = self.browser.browser_config.evm_address

    def _request_check_eligible(self, address: str) -> tuple[bool, bool]:
        """检查地址是否符合资格

        Args:
            address: 钱包地址

        Returns:
            (success, is_eligible)
        """
        try:
            url = "https://badge.wand.fi/api/eligible"
            headers = {
                "accept-language": "en,en-US;q=0.9",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
            }
            data = {"address": address}

            success, response = self._make_post_request(
                url=url, headers=headers, data=data
            )
            if success:
                return response.get("isEligible", False)

            return False

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 检查资格失败: {str(e)}"
            )
            return False, False

    def _request_get_signature(self, address: str) -> Optional[str]:
        """获取签名"""
        try:
            url = f"{self.home_url}api/signature"
            data = {"address": address}

            success, response_data = self._make_post_request(url=url, data=data)
            if not success:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )
                return False

            if "signature" in response_data:
                signature = response_data["signature"]
                # logger.info(
                #     f"{self.browser_id} {self.project_name} 获取签名成功: {signature}"
                # )
                return signature
            elif "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data['error']}"
                )
                return False

            return False

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return False

    def _connect_wallet(self) -> bool:
        """连接钱包"""
        return True

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行 Wand 任务"""
        if self._check_task_completed():
            return True

        rpc_url = os.getenv("RPC_URL")
        private_key = self.browser.browser_config.evm_private_key
        proxy = self.browser.browser_config.proxy
        if proxy:
            is_valid = Proxies(proxy).verify()
            if not is_valid:
                logger.error(f"{self.browser_id} {self.project_name} 代理无效")
                return False

        user_agent = self.browser.browser_config.user_agent
        wand = WandContract(
            id=self.browser_id,
            rpc_url=rpc_url,
            private_key=private_key,
            proxy=proxy,
            user_agent=user_agent,
        )
        if wand.check_nft_minted():
            logger.success(f"{self.browser_id} {self.project_name} NFT已铸造, 跳过任务")
            self._update_task_status(True)
            return True

        # gas低了再启用
        gas_prices = self.get_gas_price()
        if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            return False

        for _ in range(3):
            # 检查资格, 有资格则跳出循环
            is_eligible = self._request_check_eligible(self.wallet_address)
            if is_eligible:
                break

            # deposit 通过调用 hex_data 调用合约，实现 deposit
            deposit_result = wand.deposit(generate_random_amount())
            if not deposit_result:
                logger.error(f"{self.browser_id} {self.project_name} 存钱失败")
                return False

        # 获取签名
        signature = self._request_get_signature(self.wallet_address)
        if not signature:
            logger.error(f"{self.browser_id} {self.project_name} 获取签名失败")
            return False

        try:
            # 铸造NFT
            sleep(random.randint(6, 20))
            mint_nft_result = wand.mint_nft(signature, self.wallet_address)
            if not mint_nft_result:
                return False

            logger.success(f"{self.browser_id} {self.project_name} 任务完成")
            self._update_task_status(True)
            # 返回False，表示任务完成，主要是不用再关闭浏览器
            return False

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 任务执行失败: {str(e)}"
            )
            return False
