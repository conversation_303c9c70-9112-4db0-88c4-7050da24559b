from typing import Optional, Dict
from web3 import Web3
from requests import Request, Session
import requests.adapters

from eth_typing import ChecksumAddress
from web3.types import TxParams, <PERSON>
from loguru import logger


class PosterNFT:

    # ERC721 标准 ABI - 只包含 balanceOf 方法
    ABI = [
        {
            "constant": True,
            "inputs": [{"name": "owner", "type": "address"}],
            "name": "balanceOf",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function",
        },
        {
            "inputs": [
                {"internalType": "address", "name": "to", "type": "address"},
                {"internalType": "string", "name": "nftMetadataURI", "type": "string"},
            ],
            "name": "mint",
            "outputs": [
                {"internalType": "uint256", "name": "tokenId", "type": "uint256"}
            ],
            "stateMutability": "nonpayable",
            "type": "function",
        },
    ]

    def __init__(
        self,
        contract_address: str,
        provider_url: str,
        abi: Optional[list] = None,
        proxy: Optional[Dict[str, str]] = None,
    ):
        """
        初始化 PosterNFT 合约实例

        Args:
            contract_address: 合约地址
            provider_url: RPC节点URL
            abi: 可选的自定义 ABI
            proxy: 代理配置,格式如 {"http": "socks5h://host:port", "https": "socks5h://host:port"}
        """
        if proxy:
            # 使用代理创建session
            session = Session()

            # 如果 proxy 是字符串，转换为字典格式
            if isinstance(proxy, str):
                # 尝试解析代理地址
                try:
                    if ":" not in proxy:
                        raise ValueError("代理地址格式错误，需要包含端口号")

                    # 如果已经包含协议前缀，直接使用
                    if proxy.startswith(("socks5://", "socks5h://")):
                        proxy_str = proxy
                    else:
                        proxy_str = f"socks5h://{proxy}"

                    proxy = {"http": proxy_str, "https": proxy_str}
                except Exception as e:
                    logger.error(f"代理地址解析失败: {str(e)}")
                    raise

            session.proxies = proxy
            provider = Web3.HTTPProvider(provider_url, session=session)
        else:
            provider = Web3.HTTPProvider(provider_url)

        self.web3 = Web3(provider)
        self.contract_address = Web3.to_checksum_address(contract_address)
        self.contract = self.web3.eth.contract(
            address=self.contract_address, abi=abi if abi else self.ABI
        )

    def balance_of(self, address: str) -> int:
        address = Web3.to_checksum_address(address)
        return self.contract.functions.balanceOf(address).call()

    def mint(
        self,
        from_address: str,
        private_key: str,
        recipient: str,
        uri: str,
        value: Wei = 0,
        gas_limit: Optional[int] = None,
        gas_price: Optional[int] = None,
    ) -> str:
        """调用合约的 mint 方法"""
        try:
            from_address = Web3.to_checksum_address(from_address)
            recipient = Web3.to_checksum_address(recipient)

            # 构建交易参数
            tx_params: TxParams = {
                "from": from_address,
                "value": value,
                "nonce": self.web3.eth.get_transaction_count(from_address),
                "chainId": self.web3.eth.chain_id,
            }

            # 获取合约方法并传入参数
            mint_function = self.contract.functions.mint(recipient, uri)

            # 设置 gas 参数
            if not gas_limit:
                try:
                    tx_params["gas"] = mint_function.estimate_gas(tx_params)
                except Exception as e:
                    logger.error(f"Gas 估算失败: {str(e)}")
                    # 使用默认值
                    tx_params["gas"] = 200000
            else:
                tx_params["gas"] = gas_limit

            if not gas_price:
                tx_params["gasPrice"] = self.web3.eth.gas_price
            else:
                tx_params["gasPrice"] = gas_price

            # 构建交易
            transaction = mint_function.build_transaction(tx_params)

            # 签名交易
            signed_tx = self.web3.eth.account.sign_transaction(
                transaction, private_key=private_key
            )

            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.raw_transaction)

            # 返回交易哈希
            return self.web3.to_hex(tx_hash)

        except Exception as e:
            logger.error(f"Mint poster nft 交易失败: {str(e)}")
            raise Exception(f"Mint poster nft 交易失败: {str(e)}")
