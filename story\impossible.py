import os
import random
from time import sleep

from loguru import logger
from .base import StoryB<PERSON>
from src.browsers import BrowserType
from retry import retry
from typing import Optional

from src.utils.proxies import Proxies
from .contract.impossible_contract import ImpossibleContract
from config import MAX_GAS_PRICE


class StoryImpossible(StoryBase):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "impossible"
        self.home_url = "https://story.impossible.finance/"
        self.wallet_address = self.browser.browser_config.evm_address
        self._impossible_contract = None

    @property
    def impossible_contract(self) -> ImpossibleContract:
        if not self._impossible_contract:
            rpc_url = os.getenv("RPC_URL")
            private_key = self.browser.browser_config.evm_private_key
            proxy = self.browser.browser_config.proxy
            if proxy:
                is_valid = Proxies(proxy).verify()
                if not is_valid:
                    logger.error(f"{self.browser_id} {self.project_name} 代理无效")
                    return False

            user_agent = self.browser.browser_config.user_agent
            self._impossible_contract = ImpossibleContract(
                id=self.browser_id,
                rpc_url=rpc_url,
                private_key=private_key,
                proxy=proxy,
                user_agent=user_agent,
            )

        return self._impossible_contract

    def _connect_wallet(self) -> bool:
        """连接钱包"""
        return True

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行 Impossible 任务"""
        if self._check_task_completed():
            return True

        impossible = self.impossible_contract

        # 检查是否已铸造NFT
        if impossible.check_nft_minted():
            logger.success(f"{self.browser_id} {self.project_name} NFT已铸造, 跳过任务")
            self._update_task_status(True)
            return True

        # gas低了再启用
        gas_prices = self.get_gas_price()
        if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            return False

        # 领取测试币
        token_balance_wei = impossible.mint_test_token()
        if token_balance_wei <= 0:
            logger.error(f"{self.browser_id} {self.project_name} 领取测试币失败")
            return False

        # 等待一段时间后进行质押
        sleep(random.randint(6, 20))

        stake_result = impossible.stake_token(token_balance_wei)
        if not stake_result:
            logger.error(f"{self.browser_id} {self.project_name} 质押失败")
            return False

        # claim
        sleep(random.randint(6, 20))
        signature = self._request_get_signature()
        if len(signature) <= 2:
            logger.error(f"{self.browser_id} {self.project_name} 获取签名失败")
            return False

        claim_result = impossible.mint_nft(signature, self.wallet_address)
        if not claim_result:
            logger.error(f"{self.browser_id} {self.project_name} 领取奖励失败")
            return False

        logger.success(f"{self.browser_id} {self.project_name} 任务完成")
        self._update_task_status(True)
        return False

    def _request_get_signature(self) -> Optional[str]:
        """获取签名"""
        try:
            url = f"{self.home_url}api/signature"
            data = {
                "address": self.impossible_contract.web3.to_checksum_address(
                    self.wallet_address
                )
            }

            success, response_data = self._make_post_request(url=url, data=data)
            if not success:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )
                return False

            if "signature" in response_data:
                signature = response_data["signature"]
                return signature
            elif "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data['error']}"
                )
                return False

            return False

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return False
