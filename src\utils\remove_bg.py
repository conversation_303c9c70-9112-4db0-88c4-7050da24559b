"""
本脚本用于去除图片中的背景字母和数字，仅保留绿色前景（如绿色线条和字母）。

绿色提取参数说明：
- lower_green, upper_green 为HSV颜色空间下的绿色范围。
- 如发现绿色前景提取不完整或有误，可通过调整这两个参数进行优化。
- 可使用图像编辑工具（如GIMP、Photoshop）或OpenCV自带的颜色拾取工具获取更精确的HSV值。

形态学操作说明：
- kernel 大小和操作次数可根据实际图片噪点和前景连通性调整。
- 若前景断裂可适当增加闭运算次数，若噪点多可增加开运算次数。

使用方法：
    python remove_bg.py [input_image] [output_image]
    # 若不指定参数，默认处理 input.png，输出 output.png
"""

import os
import sys

import cv2
import numpy as np

# 输入输出文件名，可根据需要修改
INPUT_IMAGE = r"C:\Users\<USER>\Pictures\cap\altiuslabs\2eRre.png"  # 原始图片名
OUTPUT_IMAGE = "toutput2.png"  # 处理后图片名


def remove_background(input_path, output_path):
    # 读取图片
    img = cv2.imread(input_path)
    if img is None:
        raise FileNotFoundError(f"无法读取图片: {input_path}")

    # --- 原图HSV调试信息输出 ---
    hsv_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv_img)

    unique_hsv = np.unique(hsv_img.reshape(-1, 3), axis=0)

    # --- END ---

    # 只在原始图片img上做一次绿色掩膜
    hsv_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower_green = np.array([58, 40, 98])
    upper_green = np.array([75, 255, 255])
    green_mask = cv2.inRange(hsv_img, lower_green, upper_green)

    # 白色掩膜（RGB均大于250）
    white_mask = np.all(img >= 250, axis=-1)

    # 创建全白背景
    clean_result = np.ones_like(img, dtype=np.uint8) * 255

    # 保留绿色像素
    clean_result[green_mask > 0] = img[green_mask > 0]
    # 保留白色像素（可省略，因背景已全白，但更严谨）
    clean_result[white_mask] = img[white_mask]

    result = clean_result

    # === 骨架化处理 ===
    # gray = cv2.cvtColor(result, cv2.COLOR_BGR2GRAY)
    # # 二值化
    # _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    # # 先腐蚀再骨架化
    # kernel = np.ones((2, 2), np.uint8)
    # eroded = cv2.erode(binary, kernel, iterations=2)
    # # 骨架化（需安装opencv-contrib-python）
    # try:
    #     from cv2.ximgproc import thinning
    #     skeleton = thinning(eroded)
    # except ImportError:
    #     print("未安装opencv-contrib-python，使用自定义骨架化算法。")
    #     skeleton = eroded  # 占位
    # cv2.imwrite("skeleton.png", skeleton)
    # print("骨架化结果已保存为: skeleton.png")

    # # === Hough变换检测直线并用inpainting修复 ===
    # # 1. 转为灰度图
    # gray = cv2.cvtColor(result, cv2.COLOR_BGR2GRAY)
    # # 2. Canny边缘检测
    # edges = cv2.Canny(gray, 50, 150, apertureSize=3)
    # # 3. Hough变换检测直线
    # lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=30, minLineLength=30, maxLineGap=10)
    # # 4. 基于横向覆盖范围筛选干扰折线
    # mask = np.zeros_like(gray)
    # min_x_span = int(result.shape[1] * 0.15)  # 线段横向跨度需覆盖70%以上宽度
    # selected_lines = []
    # if lines is not None:
    #     for line in lines:
    #         x1, y1, x2, y2 = line[0]
    #         x_span = abs(x2 - x1)
    #         if x_span >= min_x_span:
    #             selected_lines.append((x1, y1, x2, y2))
    #             cv2.line(mask, (x1, y1), (x2, y2), 255, 5)
    # # 5. 直接将mask区域设为白色
    # result[mask > 0] = [255, 255, 255]
    # === END Hough+inpaint ===

    # 保存结果图片
    cv2.imwrite(output_path, result)


def split_characters(image_path, output_dir, char_size=(30, 30)):
    """
    从去背景后的图片中分割出6个字符小图，每个为char_size（默认30x30），保存到output_dir。
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图片: {image_path}")
        return
    # 转为灰度并二值化（绿色为前景，白色为背景）
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # 绿色区域较暗，白色为255
    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
    # 形态学操作去除细线（可调参数）
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    morph = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=1)
    # 连通域分析/轮廓检测
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    # 按面积排序，取前6大
    contours = sorted(contours, key=cv2.contourArea, reverse=True)[:6]
    chars = []
    for i, cnt in enumerate(contours):
        x, y, w, h = cv2.boundingRect(cnt)
        char_img = img[y : y + h, x : x + w]
        # 等比缩放后居中填充到30x30
        blank = np.ones((char_size[1], char_size[0], 3), dtype=np.uint8) * 255
        scale = min(char_size[0] / w, char_size[1] / h)
        nw, nh = int(w * scale), int(h * scale)
        resized = cv2.resize(char_img, (nw, nh), interpolation=cv2.INTER_AREA)
        x_offset = (char_size[0] - nw) // 2
        y_offset = (char_size[1] - nh) // 2
        blank[y_offset : y_offset + nh, x_offset : x_offset + nw] = resized
        out_path = os.path.join(output_dir, f"char_{i+1}.png")
        cv2.imwrite(out_path, blank)
        chars.append(out_path)
    if len(contours) != 6:
        print(f"警告：检测到的字符区域数为{len(contours)}，请检查分割效果。")
    print(f"字符分割完成，输出目录: {output_dir}")
    return chars


if __name__ == "__main__":
    # 支持命令行参数输入
    input_path = sys.argv[1] if len(sys.argv) > 1 else INPUT_IMAGE
    output_path = sys.argv[2] if len(sys.argv) > 2 else OUTPUT_IMAGE
    try:
        remove_background(input_path, output_path)
        # 新增：分割字符
        import ddddocr
        import cv2

        det = ddddocr.DdddOcr(det=True)

        with open(output_path, "rb") as f:
            image = f.read()

        bboxes = det.detection(image)

        im = cv2.imread(output_path)

        for bbox in bboxes:
            x1, y1, x2, y2 = bbox
            im = cv2.rectangle(im, (x1, y1), (x2, y2), color=(0, 0, 255), thickness=2)

        cv2.imwrite("result.jpg", im)
    except Exception as e:
        print(f"处理失败: {e}")
