#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（脚本目录的上一级）
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." && pwd )"

# 设置日志文件路径
LOG_FILE="$PROJECT_DIR/logs/somnia_cron.log"
LOG_DIR=$(dirname "$LOG_FILE")

# 创建日志目录（如果不存在）
mkdir -p "$LOG_DIR"

# 日志记录函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 获取当前的 crontab 内容
CURRENT_CRON=$(crontab -l 2>/dev/null)

# 如果 crontab 为空，则无需操作
if [ -z "$CURRENT_CRON" ]; then
    log_message "当前 crontab 为空，无需删除任务"
    exit 0
fi

# 定义要删除的任务模式（使用 run_somnia.sh 的任务）
TASK_PATTERN="$PROJECT_DIR/scripts/run_somnia.sh"

# 创建一个临时文件
TEMP_CRON=$(mktemp)

# 过滤掉包含指定模式的行
echo "$CURRENT_CRON" | grep -v "$TASK_PATTERN" > "$TEMP_CRON"

# 检查过滤后的内容是否与原内容不同
# 使用更兼容的方式检查文件差异
ORIGINAL_LINES=$(echo "$CURRENT_CRON" | wc -l)
FILTERED_LINES=$(cat "$TEMP_CRON" | wc -l)

if [ "$ORIGINAL_LINES" -eq "$FILTERED_LINES" ]; then
    log_message "未找到相关定时任务，无需删除"
else
    # 应用新的 crontab
    crontab "$TEMP_CRON"
    
    if [ $? -eq 0 ]; then
        log_message "成功删除所有 somnia 相关定时任务"
        log_message "当前 crontab 内容："
        
        # 直接将 crontab 内容输出到日志
        CRONTAB_CONTENT=$(crontab -l 2>/dev/null)
        if [ -z "$CRONTAB_CONTENT" ]; then
            log_message "  crontab 为空"
        else
            echo "$CRONTAB_CONTENT" | while IFS= read -r line; do
                log_message "  $line"
            done
        fi
    else
        log_message "删除定时任务失败"
    fi
fi

# 删除临时文件
rm "$TEMP_CRON"

log_message "定时任务清理脚本执行完成" 