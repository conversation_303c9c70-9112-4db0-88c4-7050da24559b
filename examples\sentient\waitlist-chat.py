import click
import random
import os
import shutil
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices, get_project_root_path
from time import sleep
from retry import retry
from src.browsers.operations import try_click
from datetime import datetime
from openai import OpenAI
from src.utils.element_util import click_on_image, get_elements, get_element
from src.utils.thread_executor import ThreadExecutor
from src.utils.hhcsv import HHCSV

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

# 是否开启AI对比
AI_COMPARE = False

logger.add("logs/sentient.log", rotation="10MB", level="SUCCESS")


class Rafflechat:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def _connect_wallet(self, lasted_tab):

        try_count = 6
        for i in range(try_count):

            if "connect" not in lasted_tab.url:
                if lasted_tab.ele("What can I help you with today", timeout=10):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

            connect_wallet_button = lasted_tab.ele(
                "x://button[.='Connect Wallet']", timeout=5
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("Continue with a wallet").click()
                sleep(2)
                lasted_tab.ele("x://button[@title='okx']").click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
                continue

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _check_cf_shield(self, lasted_tab):

        for _ in range(6):

            # 1. 判断是否在Dobby Battle Arena页面
            if "Sentient Chat Waitlist" in lasted_tab.title:
                return True

            

            # 2. 判断是否在CF盾页面
            div_ele = lasted_tab.ele("x://div[@class='main-content']/div/div/div")
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            if iframe:
                try:
                    logger.warning(f"【{self.id}】 在CF盾页面")

                    checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                    if not checkbox:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                    checkbox.wait.has_rect(timeout=20)
                    checkbox.click()
                    sleep(3)
                    if lasted_tab.ele("What can I help you with today"):
                        return True
                    else:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                except Exception as e:
                    logger.error(f"【{self.id}】 过CF盾失败: {e}")
                    sleep(3)
                    continue

        return False

    def _click_button(self, lasted_tab, button_text):
        try_click(lasted_tab, f"x://button[contains(.,'{button_text}')]")

    def _task_register(self, lasted_tab):
        logger.info(f"【{self.id}】 开始注册")

        ele = lasted_tab.wait.ele_displayed("x://h1[.='Tasks']", timeout=3)
        if "success" in lasted_tab.url:
            logger.success(f"【{self.id}】 已经加入成功")
            return True
        if not ele:
            # 1. 点击开始
            try:
                self._click_button(lasted_tab, "Continue")
            except Exception as e:
                pass

            # 2. 点击注册
            try:
                self._click_button(lasted_tab, "Continue with a wallet")
                lasted_tab.ele("x://button[@title='okx']").click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
            except Exception as e:
                pass

            # 3. 点击任务
            ele = lasted_tab.wait.ele_displayed("x://h1[.='Tasks']", timeout=360)
            if not ele:
                raise Exception(f"{self.id} 未找到任务页面")

        # 4. 点击关注twitter
        try_click(
            lasted_tab,
            "x://h3[contains(text(), 'Follow SentientAGI')]/../../../button",
        )
        sleep(10)

        # 5. 点击关注x
        lasted_tab.ele(
            "x://h3[contains(text(), 'Follow Himanshu Tyagi')]/../../../button"
        ).click()
        sleep(10)

        # 5. 点击关注x
        lasted_tab.ele(
            "x://h3[contains(text(), 'Follow the Sentient Intern')]/../../../button"
        ).click()
        sleep(10)

        # 6. 分享
        lasted_tab.ele("x://button[.='Share']").click()
        sleep(10)

        # 加入
        lasted_tab.ele("x://button[.='Join the waitlist']").click()

        ele = lasted_tab.ele("You're in!", timeout=60)
        if ele:
            logger.success(f"【{self.id}】 加入成功")
            return True
        else:
            logger.error(f"【{self.id}】 加入失败")
            return False
    
    def _check_waitlist_page(self, lasted_tab):
        """等待队列页面处理，返回是否成功通过队列"""
        try:
            # 检查是否在等待页面
            if not lasted_tab.ele("Your estimated wait time is", timeout=3):
                logger.info(f"【{self.id}】 不在等待队列页面，继续执行")
                return True
            
            # 开始等待队列
            logger.info(f"【{self.id}】 检测到等待队列，开始等待...")
            max_wait_attempts = 1200  # 最多等待约1小时
            wait_count = 0
            
            # 等待队列元素消失
            while lasted_tab.ele("Your estimated wait time is", timeout=3):
                wait_count += 1
                
                # 每30秒记录一次等待时间
                if wait_count % 10 == 0:
                    logger.info(f"【{self.id}】 正在等待队列，已等待 {wait_count * 3} 秒")
                
                # 检查是否超时
                if wait_count >= max_wait_attempts:
                    logger.warning(f"【{self.id}】 等待队列超时，已等待 {wait_count * 3} 秒")
                    return False

                sleep(3)

            # 队列元素消失，等待页面加载
            logger.success(f"【{self.id}】 已通过等待队列，总等待时间: {wait_count * 3} 秒")
            sleep(5)

            return True
            
        except Exception as e:
            logger.error(f"【{self.id}】 等待队列过程出错: {e}")
            return False


    @retry(tries=3, delay=1)
    def task(self):
        try:
            # 1. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            lasted_tab = self.page.new_tab("https://waitlist-chat.sentient.xyz/")
            sleep(8)

            # 2. 判断是否在等待页面
            if not self._check_waitlist_page(lasted_tab):
                logger.error(f"【{self.id}】 未在等待页面")
                return False

            # 3. 判断是否在CF盾页面
            result = self._check_cf_shield(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 过CF盾失败")
                return False

            # 4. 点击开始
            self._task_register(lasted_tab)

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")

    def _get_today_clicks(self):
        """获取今日已点击次数"""
        return self.today_clicks

    def join_fraction_ai(self):
        #
        url = f"https://discord.com/invite/wEJCeckB9a"
        try:
            tab = self.page.new_tab(url)

            # 检查是否登录
            if get_element(tab, "x://div[text()='Already have an account?']", 3):
                logger.warning(f"{self.id} dc已经登出, 请登录dc")
                return False

            # 点击加群按钮
            join = get_element(tab, "x://button[@type='button']", 5)
            if join:
                join.click()
            else:
                logger.error(f"{self.id} 加群失败, 未找到加入按钮")
                return False

            # 判断是否跳转到进群页面
            if not tab.wait.url_change("onboarding", timeout=10):
                # 检查是否需要处理验证码
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", 3):
                    logger.warning(f"{self.id} 需要处理验证码，请手动处理")
                    return False
                ele = get_element(tab, "x://div[text()='Continue to Discord']/..", 3)
                if ele:
                    ele.click()
                    sleep(2)

            sleep(5)
            eles = get_elements(
                tab, "x://div[contains(@class, 'reactionInner') and @role='button']", 5
            )
            if not eles or len(eles) == 0:
                logger.warning(f"{self.id} 点击验证失败，请手动处理")
                return False

            eles[0].click()
            sleep(5)
            logger.success(f"{self.id} 加入 Fraction Ai Discord服务器成功")
            return True
        except Exception as e:
            logger.error(f"{self.id} 加入 Discord 服务器时发生错误: {str(e)}")
            return False


def _run_task(type, index):
    try:
        browser = Rafflechat(type, str(index))
        browser.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                browser = Rafflechat(type, str(index))
                return browser.task()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Sentient-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/sentient/rafflechat.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.CHROME, "2")
    # sentient = Sentient(BrowserType.ADS, "134")
    # if sentient.join_sentient() and sentient.join_fraction_ai():
    #     sentient.page.quit()
