import discum
from loguru import logger
import openai
import os
import time
import random
from typing import Optional, List, Dict
import requests
import uuid
from fake_useragent import UserAgent


class DiscordChatBot:
    # 添加表情符号池
    EMOJI_POOLS = {
        "positive": [
            "👍",
            "❤️",
            "🔥",
            "💯",
            "⭐",
            "✨",
            "🎉",
            "🙌",
            "💪",
            "👏",
            "🚀",
            "💫",
            "🌟",
            "💖",
            "💓",
            "💝",
            "💕",
            "♥️",
            "😊",
            "🥰",
            "✅",
        ],
        "funny": [
            "😂",
            "🤣",
            "😆",
            "😅",
            "😄",
            "😹",
            "🤪",
            "🤭",
            "😝",
            "😛",
            "🤡",
            "🤠",
            "🥳",
            "😎",
            "🤓",
            "🧐",
            "🤔",
            "🫡",
            "🫢",
            "😏",
        ],
        "sad": ["😢", "😭", "😿", "💔", "😥", "😓", "😔", "😞", "😟", "🥺"],
        "surprised": ["😮", "😲", "😱", "🤯", "😨", "😰", "😯", "😦", "🫢", "😳"],
        "gaming": ["🎮", "🎲", "🎯", "🎪", "🎨", "🎭", "🎪", "🎰", "🎳", "🎤"],
    }

    def __init__(
        self,
        proxy: str,
        token: str,
        user_id: Optional[str] = None,
        openai_api_key: Optional[str] = None,
        user_agent: Optional[str] = None,
    ):
        self.proxy = DiscordChatBot._format_proxy_url(proxy)
        self.token = token
        self.user_id = user_id
        self.openai_api_key = openai_api_key
        self.user_agent = (
            user_agent
            or UserAgent(browsers=["Chrome"], os=["Windows", "Mac OS X"]).random
        )
        if not self.token:
            logger.error("DISCORD_TOKEN 未设置")
            return False

        logger.info(f"Discord 使用代理: {self.proxy}")

        # 创建Discord客户端实例
        self.discord_client = discum.Client(
            token=self.token, proxy=self.proxy, log=False, user_agent=self.user_agent
        )

        # 获取机器人自己的信息
        if self.user_id:
            self.bot_info = self._get_bot_info(self.user_id)
            logger.info(f"机器人信息: {self.bot_info}")

        # 初始化OpenAI客户端
        if self.openai_api_key:
            self.client = openai.OpenAI(
                api_key=self.openai_api_key, base_url="https://api.deepseek.com"
            )

        # 设置聊天参数
        self.min_reply_delay = 2
        self.max_reply_delay = 5
        self.typing_speed = (30, 100)

    # 处理代理 URL 格式
    @staticmethod
    def _format_proxy_url(proxy: str):
        if not proxy:
            return None

        if proxy.startswith(("socks5://", "socks5h://", "http://", "https://")):
            return proxy

        # 如果是 IP:PORT 格式，转换为 HTTP URL
        if ":" in proxy:
            proxy = f"http://{proxy}"

        return proxy

    def _simulate_typing(self, channel_id: str, message: str):
        """模拟打字状态"""
        if not message:
            return

        # 计算模拟打字时间
        chars_per_minute = random.randint(self.typing_speed[0], self.typing_speed[1])
        typing_time = len(message) / (chars_per_minute / 60)

        try:
            # 发送打字状态
            self.discord_client.typingAction(channel_id)
            # 等待计算出的打字时间
            time.sleep(typing_time)
        except Exception as e:
            logger.error(f"模拟打字状态失败: {e}")

    def _get_natural_delay(self, message: str) -> float:
        """计算自然的回复延迟时间"""
        # 基础延迟
        base_delay = random.uniform(self.min_reply_delay, self.max_reply_delay)

        # 根据消息长度增加延迟
        length_factor = len(message) / 100  # 每100个字符增加的延迟因子

        # 最终延迟时间
        total_delay = base_delay + (length_factor * random.uniform(0.5, 1.5))

        return min(total_delay, self.max_reply_delay * 2)  # 限制最大延迟

    def send_message(self, channel_id: str, message: str):
        """发送消息到指定频道"""

        # 先等待一个自然的延迟时间
        time.sleep(self._get_natural_delay(message))

        # 模拟打字状态
        self._simulate_typing(channel_id, message)

        # 发送消息
        try:
            response = self.discord_client.sendMessage(channel_id, message)

            # 检查响应状态
            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get("id")
                logger.success(f"消息发送成功 [ID: {message_id}]: {message}")
                return True
            else:
                logger.error(f"消息发送失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False

    def send_message_curl(self, channel_id: str, message: str):
        """发送消息到指定频道"""

        # 模拟打字状态
        self._simulate_typing(channel_id, message)

        # 发送消息
        try:
            # 这里ua怎么转换成post header
            headers = {
                "Authorization": f"{self.token}",
                "Content-Type": "application/json",
                "User-Agent": self.user_agent,
            }
            data = {
                "mobile_network_type": "unknown",
                "content": message,
                "nonce": str(
                    (int(time.time() * 1000) - 1420070400000) << 22
                    | random.randint(0, 0x3FFFFF)
                ),  # 生成一个随机nonce,discord的nonce怎么生成？
                "tts": False,
                "flags": 0,
            }
            response = requests.post(
                f"https://discord.com/api/v9/channels/{channel_id}/messages",
                headers=headers,
                json=data,
            )

            # 检查响应状态
            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get("id")
                logger.success(f"消息发送成功 [ID: {message_id}]: {message}")
                return True
            else:
                logger.error(f"消息发送失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False

    def _get_bot_info(self, user_id: str) -> Dict[str, str]:
        """获取机器人自己的信息"""
        try:
            me = self.discord_client.getProfile(user_id).json()
            user = me.get("user")
            return {"id": user.get("id", ""), "username": user.get("username", "Bot")}
        except Exception as e:
            logger.error(f"获取机器人信息失败: {e}")
            return {"id": "", "username": "Bot"}

    def _format_messages_context(self, messages: List[Dict]) -> List[Dict[str, str]]:
        """
        格式化消息上下文，返回OpenAI消息格式的列表
        每条消息都包含role和content
        """
        # 按时间顺序排序消息
        messages.sort(key=lambda x: x["id"])

        # 构建消息列表
        formatted_messages = [
            {
                "role": "system",
                "content": f"""You are participating in a Discord chat as {self.bot_info['username']}.
                When responding, remember that you are {self.bot_info['username']}.
                Please provide natural and engaging responses as if you are a real person.
                Keep responses concise (must under 10 words) and only one sentence.
                Consider the entire conversation context when responding. Do not say hello, directly follow the content of the response.
                Never mention or include usernames in your response.
                Do not end your response with any punctuation marks (no period, comma, exclamation mark, question mark, etc).
                Act like a real person, not a bot. Be natural and casual.
                Never use emojis or special characters too frequently.
                Avoid formal or overly enthusiastic language.
                Do not actively reply to others unless directly addressed.
                Never mention usernames or refer to previous messages explicitly.
                Do not use any punctuation marks at the end of responses.
                Sometimes intentionally make minor typos to appear more human.
                Vary your response style and avoid repetitive patterns.
                Do not reply to technical questions or questions that require specific step-by-step instructions.
                """,
            }
        ]

        # 添加聊天消息
        for msg in messages:
            # 判断消息是否来自机器人自己
            is_self = msg["author"] == self.bot_info["username"]

            if is_self:
                # 如果是自己的消息，使用assistant角色
                formatted_messages.append(
                    {"role": "assistant", "content": msg["content"]}
                )
            else:
                # 如果是其他人的消息，使用user角色，并标注发送者
                formatted_messages.append(
                    {"role": "user", "content": f"<{msg['author']}> {msg['content']}"}
                )

        return formatted_messages

    def _generate_response(
        self, messages: List[Dict[str, str]], language: str
    ) -> Optional[str]:
        """使用DeepSeek API生成回复"""
        max_retries = 3  # 最大重试次数

        try:
            # 在消息列表开头添加语言设置
            messages[0][
                "content"
            ] += f"""
            Please respond in {language} language.
            Maintain a casual, friendly tone.
            """

            for attempt in range(max_retries):
                # 生成回复
                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=messages,
                    stream=False,
                    temperature=0.7,
                    max_tokens=100,
                )

                content = response.choices[0].message.content.strip()

                # 检查是否包含用户名格式 (<name>)
                if not any(
                    part.startswith("<") and part.endswith(">")
                    for part in content.split()
                ):
                    return content
                else:
                    logger.warning(
                        f"生成的回复包含用户名格式，正在重试 ({attempt + 1}/{max_retries})"
                    )
                    continue

            logger.error("达到最大重试次数，无法生成合适的回复")
            return None

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return None

    def _detect_channel_language(self, messages: List[Dict]) -> str:
        """
        根据频道最近的消息检测主要使用的语言
        返回 'en' 或 'zh' 或其他语言代码
        """
        try:
            # 收集所有消息内容
            contents = [msg["content"] for msg in messages if msg["content"]]
            if not contents:
                return "en"  # 如果没有消息内容，默认使用英语

            # 使用DeepSeek检测语言
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a language detector. Respond with only the language code ('en' for English, 'zh' for Chinese, etc).",
                    },
                    {
                        "role": "user",
                        "content": f"Detect the main language used in these messages and respond with only the language code:\n{' '.join(contents[:5])}",
                    },
                ],
                stream=False,
                temperature=0.1,
                max_tokens=10,
            )

            detected_lang = response.choices[0].message.content.strip().lower()
            logger.info(f"检测到频道主要语言: {detected_lang}")
            return detected_lang

        except Exception as e:
            logger.error(f"语言检测失败: {e}")
            return "en"  # 出错时默认使用英语

    def send_del_message(
        self,
        channel_id: str,
        count: int = 1,
        msg: str = "gm",
        min_delay: int = 5,
        max_delay: int = 10,
        min_time_diff: float = 3600.0,
    ) -> int:
        """
        发送消息并删除

        Args:
            count: 发送次数
            msg: 消息内容
            min_delay: 最小延迟（秒）
            max_delay: 最大延迟（秒）
            min_time_diff: 最新消息与当前时间的最小间隔（秒），默认3600秒（1小时）

        Returns:
            int: 成功发送的消息次数
        """
        if not self.token:
            logger.error("DISCORD_TOKEN 未设置")
            return 0

        success_count = 0
        try:
            for _ in range(count):
                # 检查最新消息时间间隔
                time_diff = self.get_latest_message_time_diff(channel_id)
                if time_diff < min_time_diff:
                    logger.warning(
                        f"最新消息时间间隔（{time_diff}秒）小于指定值（{min_time_diff}秒），跳过本次发送"
                    )
                    sleep_time = random.uniform(min_delay, max_delay)
                    time.sleep(sleep_time)
                    continue

                # 发送消息
                message = self.discord_client.sendMessage(
                    channelID=channel_id, message=msg
                )
                if not message:
                    logger.error("发送消息失败")
                    continue

                message = message.json()
                message_id = message.get("id")
                if not message_id:
                    logger.error("获取消息ID失败")
                    continue

                logger.success(f"消息发送成功 [ID: {message_id}]: {msg}")

                # 删除消息（最多重试3次）
                delete_success = False
                for retry in range(3):
                    if self.discord_client.deleteMessage(
                        channelID=channel_id, messageID=message_id
                    ):
                        delete_success = True
                        break
                    logger.warning(f"删除消息失败，正在重试 ({retry + 1}/3)")
                    time.sleep(1)  # 等待1秒后重试

                if not delete_success:
                    logger.error("删除消息最终失败，跳过计数")
                    continue

                logger.success(f"消息删除成功 [ID: {message_id}]: {msg}")

                success_count += 1

                # 随机延迟，最后一条消息不延迟
                if _ < count - 1:
                    sleep_time = random.uniform(min_delay, max_delay)
                    time.sleep(sleep_time)

        except Exception as e:
            logger.error(f"发送消息出错: {e}")

        return success_count

    def _split_message(self, message: str) -> List[str]:
        """
        根据标点符号分割消息
        逗号有50%概率分割，其他标点符号100%分割
        只返回第一行
        """
        # 如果消息中没有标点符号，直接返回
        if not any(p in message for p in ",.!?。，！？"):
            return [message]

        # 先处理逗号以外的标点符号
        parts = []
        temp = message
        for punct in ".!?。！？":
            if punct in temp:
                # 按标点符号分割
                splits = temp.split(punct)
                # 去掉空字符串并清理每个部分
                temp_parts = [p.strip() for p in splits if p.strip()]
                if temp_parts:
                    parts.extend(temp_parts)
                    temp = ""  # 清空临时字符串，因为已经处理完了

        # 如果没有其他标点符号分割，检查逗号
        if not parts and ("," in temp or "，" in temp):
            # 50%概率分割逗号
            if random.random() < 0.5:
                # 处理中英文逗号
                temp = temp.replace("，", ",")
                splits = temp.split(",")
                parts = [p.strip() for p in splits if p.strip()]
            else:
                parts = [temp]
        elif not parts:  # 如果没有任何分割
            parts = [temp]

        # 只返回第一行
        return [parts[0]] if parts else [message]

    def get_latest_message_time_diff(self, channel_id: str) -> float:
        """
        获取指定频道最新消息距离当前时间的间隔（秒）
        只考虑非机器人的消息

        Returns:
            float: 时间间隔（秒），如果获取失败返回-1
        """
        try:
            # 获取最新消息
            messages = self.discord_client.getMessages(
                channel_id, num=20
            )  # 获取更多消息以确保能找到非机器人消息
            messages = messages.json()

            if not messages:
                logger.warning("未找到任何消息")
                return -1

            # 找到最新的非机器人消息
            latest_message = None
            for msg in messages:
                if not msg["author"].get("bot", False):
                    latest_message = msg
                    break

            if not latest_message:
                logger.warning("未找到非机器人消息")
                return -1

            # 获取最新消息的时间戳
            message_timestamp = latest_message.get("timestamp")

            if not message_timestamp:
                logger.warning("消息中未找到时间戳")
                return -1

            # 将Discord时间戳转换为时间对象
            from datetime import datetime
            import time

            message_time = datetime.strptime(
                message_timestamp, "%Y-%m-%dT%H:%M:%S.%f%z"
            )
            current_time = datetime.now(message_time.tzinfo)

            # 计算时间差（秒）
            time_diff = (current_time - message_time).total_seconds()

            return time_diff

        except Exception as e:
            logger.error(f"获取最新消息时间间隔失败: {e}")
            return -1

    def auto_chat(
        self,
        channel_id: str,
        curl: bool = False,
        language: str = "auto",
        max_messages: int = 50,
        min_delay: int = 5,
        max_delay: int = 50,
    ):
        """自动参与频道聊天

        Args:
            channel_id: 频道ID
            language: 聊天语言，默认auto自动检测
            max_messages: 最大消息数量
            min_delay: 随机延迟最小值（秒），默认为5秒
            max_delay: 随机延迟最大值（秒），默认为50秒
        """
        if not self.openai_api_key:
            logger.error("API_KEY 未设置")
            return False

        if not self.token:
            logger.error("DISCORD_TOKEN 未设置")
            return False

        try:
            # 获取最新消息用于语言检测
            messages = self.discord_client.getMessages(channelID=channel_id, num=20)
            messages = messages.json()

            # 如果language是auto，则自动检测语言
            if language == "auto":
                language = self._detect_channel_language(messages)

            logger.info(f"开始自动聊天，使用语言: {language}")

            # 存储已处理的消息ID和最后回复时间
            processed_messages = set()
            message_count = 0
            last_reply_time = 0

            while message_count < max_messages:
                # 获取最新消息
                messages = self.discord_client.getMessages(channelID=channel_id, num=20)
                messages = messages.json()

                # 过滤出未处理的非机器人消息
                new_messages = []
                for msg in messages:
                    if msg["id"] not in processed_messages and not msg["author"].get(
                        "bot", False
                    ):
                        new_messages.append(
                            {
                                "id": msg["id"],
                                "content": msg["content"],
                                "author": msg["author"].get("username", "Unknown"),
                                "is_bot": msg["author"].get("bot", False),
                            }
                        )
                        processed_messages.add(msg["id"])

                # 如果有新消息，且距离上次回复有足够间隔
                if new_messages and time.time() - last_reply_time >= min_delay:
                    formatted_messages = self._format_messages_context(new_messages)
                    response = self._generate_response(formatted_messages, language)
                    if response:
                        # 分割消息
                        # message_parts = self._split_message(response)
                        message_parts = [response]
                        success = True

                        # 发送每个部分
                        for part in message_parts:
                            logger.info(part)
                            if curl:
                                if not self.send_message_curl(channel_id, part):
                                    success = False
                                    break
                            else:
                                if not self.send_message(channel_id, part):
                                    success = False
                                    break

                        if success:
                            message_count += 1
                            last_reply_time = time.time()

                # 随机化检查间隔，最后一条消息不延迟
                if message_count < max_messages - 1:
                    sleep_time = random.uniform(min_delay, max_delay)
                    time.sleep(sleep_time)

        except Exception as e:
            logger.error(f"自动聊天出错: {e}")
            return False
        finally:
            self.discord_client.gateway.close()

        return True

    def _get_random_emoji(self, category: Optional[str] = None) -> str:
        """
        获取随机表情

        Args:
            category: 表情类别，可选值：positive, funny, sad, surprised, gaming
                     如果不指定，则从所有表情中随机选择

        Returns:
            str: 随机表情
        """
        if category and category in self.EMOJI_POOLS:
            return random.choice(self.EMOJI_POOLS[category])

        # 如果类别不存在或未指定，从所有表情中随机选择
        all_emojis = []
        for emojis in self.EMOJI_POOLS.values():
            all_emojis.extend(emojis)
        return random.choice(all_emojis)

    def add_reaction(
        self,
        channel_id: str,
        message_id: str,
        emoji: Optional[str] = None,
        category: Optional[str] = None,
    ) -> bool:
        """
        为指定消息添加表情反应

        Args:
            channel_id: 频道ID
            message_id: 消息ID
            emoji: 要添加的表情，如果不指定则随机选择
            category: 表情类别，当emoji未指定时生效

        Returns:
            bool: 是否成功
        """
        try:
            # 如果没有指定表情，则随机选择
            if not emoji:
                emoji = self._get_random_emoji(category)

            # 添加随机延迟，模拟真实用户行为
            time.sleep(random.uniform(0.5, 2))

            # 添加反应
            response = self.discord_client.addReaction(channel_id, message_id, emoji)

            if response.status_code == 204:  # Discord API 成功响应码
                # logger.success(f"消息 {message_id} 添加反应 {emoji} 成功")
                return True
            else:
                # logger.error(f"添加反应失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"添加反应失败: {e}")
            return False

    def react_to_latest_messages(
        self,
        channel_id: str,
        count: int = 1,
        emoji: Optional[str] = None,
        category: Optional[str] = None,
        skip_bot_messages: bool = True,
        random_category: bool = True,
    ) -> int:
        """
        为频道最新的消息添加反应

        Args:
            channel_id: 频道ID
            count: 要添加反应的消息数量
            emoji: 要添加的表情，如果不指定则随机选择
            category: 表情类别，当emoji未指定时生效
            skip_bot_messages: 是否跳过机器人的消息，默认为True
            random_category: 是否为每条消息随机选择不同类别的表情，默认为True

        Returns:
            int: 成功添加反应的消息数量
        """
        try:
            # 获取最新消息
            messages = self.discord_client.getMessages(channel_id, num=20)
            messages = messages.json()

            if not messages:
                logger.warning("未找到任何消息")
                return 0

            success_count = 0
            processed_count = 0

            for msg in messages:
                # 如果达到指定数量，退出
                if processed_count >= count:
                    break

                # 如果需要跳过机器人消息
                if skip_bot_messages and msg["author"].get("bot", False):
                    continue

                # 如果需要随机类别
                current_category = (
                    random.choice(list(self.EMOJI_POOLS.keys()))
                    if random_category
                    else category
                )

                # 添加反应
                if self.add_reaction(channel_id, msg["id"], emoji, current_category):
                    success_count += 1

                processed_count += 1

                # 添加随机延迟
                if processed_count < count:
                    time.sleep(random.uniform(1, 3))

            return success_count

        except Exception as e:
            logger.error(f"为最新消息添加反应失败: {e}")
            return 0

    def get_messages_by_time(
        self, channel_id: str, before: Optional[str] = None, limit: int = 20
    ) -> List[Dict]:
        """
        按时间获取消息

        Args:
            channel_id: 频道ID
            before: 获取此消息ID之前的消息（Discord的消息ID包含时间戳）
            limit: 单次获取的消息数量限制

        Returns:
            List[Dict]: 消息列表
        """
        try:
            # 获取消息
            messages = self.discord_client.getMessages(
                channelID=channel_id, num=limit, beforeDate=before
            )
            messages = messages.json()

            return messages if messages else []

        except Exception as e:
            logger.error(f"获取消息失败: {e}")
            return []
