# CSV过滤和匹配工具

这是一个完整的CSV数据处理工具，用于根据`satlayer2_chrome.csv`中的status字段过滤数据，然后在`chrome.csv`中匹配钱包地址。

## 功能特点

✅ **智能过滤**: 自动过滤status字段为空或为0的数据  
✅ **精确匹配**: 根据钱包地址在两个CSV文件间进行数据匹配  
✅ **完整日志**: 详细的处理日志和错误信息  
✅ **数据验证**: 文件存在性和列完整性验证  
✅ **详细报告**: 生成处理统计报告和数据质量分析  
✅ **多种输出**: CSV结果文件 + JSON统计文件  
✅ **命令行支持**: 支持命令行参数自定义  

## 文件说明

### 核心文件
- `csv_processor_complete.py` - 完整的处理器类，包含所有功能
- `run_csv_processing.py` - 简化的运行脚本
- `filter_and_match_csv.py` - 原始的简单版本

### 输入文件
- `examples/okx/satlayer2_chrome.csv` - Satlayer数据文件
- `data/chrome.csv` - Chrome钱包数据文件

### 输出文件
- `filtered_matched_data_YYYYMMDD_HHMMSS.csv` - 匹配结果
- `filtered_matched_data_stats_YYYYMMDD_HHMMSS.json` - 统计信息
- `csv_processing_YYYYMMDD.log` - 处理日志

## 使用方法

### 方法1: 简单运行（推荐）

```bash
# 使用默认文件路径
python run_csv_processing.py

# 指定自定义文件路径
python run_csv_processing.py path/to/satlayer.csv path/to/chrome.csv

# 指定文件路径和输出前缀
python run_csv_processing.py path/to/satlayer.csv path/to/chrome.csv my_output
```

### 方法2: 完整功能运行

```bash
# 使用默认参数
python csv_processor_complete.py

# 自定义所有参数
python csv_processor_complete.py \
  --satlayer examples/okx/satlayer2_chrome.csv \
  --chrome data/chrome.csv \
  --output filtered_data \
  --log-level INFO

# 查看帮助
python csv_processor_complete.py --help
```

### 方法3: 原始简单版本

```bash
python filter_and_match_csv.py
```

## 处理流程

1. **文件验证** - 检查输入文件是否存在
2. **数据加载** - 读取CSV文件并验证必要列
3. **数据过滤** - 过滤status字段为空或为0的行
4. **地址匹配** - 根据钱包地址匹配两个数据集
5. **结果保存** - 保存匹配结果和统计信息
6. **报告生成** - 生成详细的处理报告

## 过滤条件

Status字段满足以下任一条件的行会被过滤出来：
- 空值 (NaN)
- 数字 0
- 字符串 '0'
- 空字符串 ''
- 字符串 'None'
- 只包含空格的字符串

## 输出说明

### CSV结果文件
包含匹配成功的所有数据，合并了两个输入文件的所有列：
- **Satlayer相关列**: index, type, address, task_*, status, message等
- **Chrome相关列**: evm_address, evm_private_key, dc_*, x_*, tg_*等

### JSON统计文件
包含处理统计信息：
```json
{
  "timestamp": "处理时间",
  "output_file": "输出文件名",
  "total_rows": "总行数",
  "total_columns": "总列数",
  "file_size_bytes": "文件大小",
  "columns": ["列名列表"]
}
```

### 处理报告
控制台输出包含：
- 📈 数据流转统计
- 📄 输出文件信息  
- 📋 列信息分类
- 🔍 数据质量分析
- 👀 关键数据预览

## 示例输出

```
📊 CSV处理完成 - 详细报告
================================================================================
📁 输出文件: filtered_matched_data_20250626_103641.csv
⏰ 处理时间: 2025-06-26 10:36:41

📈 数据流转统计:
  ├─ 原始satlayer数据: 1,000 行
  ├─ 过滤后数据: 207 行 (20.7%)
  └─ 最终匹配成功: 2 行 (1.0% 匹配率)

📄 输出文件信息:
  ├─ 文件大小: 1,260 字节 (1.2 KB)
  ├─ 列数: 60
  └─ 行数: 2
```

## 依赖要求

```bash
pip install pandas
```

## 错误处理

- 文件不存在时会给出明确错误信息
- 缺少必要列时会列出可用列
- 处理过程中的异常会记录到日志文件
- 支持不同日志级别 (DEBUG, INFO, WARNING, ERROR)

## 注意事项

1. 确保输入文件格式正确（CSV格式）
2. Satlayer文件必须包含 `address` 和 `status` 列
3. Chrome文件必须包含 `evm_address` 列
4. 地址匹配是精确匹配，区分大小写
5. 输出文件会自动添加时间戳避免覆盖

## 故障排除

### 常见问题

**Q: 匹配率很低怎么办？**
A: 检查两个文件中的地址格式是否一致，确认地址的大小写匹配。

**Q: 过滤后没有数据？**
A: 检查status列的值分布，确认过滤条件是否正确。

**Q: 文件加载失败？**
A: 确认文件路径正确，文件格式为标准CSV，编码为UTF-8。

### 调试模式

使用DEBUG日志级别获取更详细信息：
```bash
python csv_processor_complete.py --log-level DEBUG
```

---

🎉 **处理完成后，你可以使用生成的CSV文件进行进一步的数据分析！**
