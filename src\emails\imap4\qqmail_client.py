from loguru import logger

from ..exceptions import MailAuthError
from .imap_client import IMAPClient, MailConfig


class QQmailClient(IMAPClient):
    """Gmail 邮件客户端

    专门处理 Gmail 邮箱的 IMAP 客户端，继承自基础的 IMAPClient。
    提供了 Gmail 特定的配置和功能。
    """

    SUPPORTED_DOMAINS = ("qq.com",)
    IMAP_SERVER = "imap.qq.com"
    IMAP_PORT = 993

    def __init__(self, email: str, password: str):
        """初始化 Gmail 邮件客户端

        Args:
            email: Gmail 邮箱地址
            password: 应用专用密码

        Note:
            Gmail 需要使用应用专用密码，而不是普通的 Google 账号密码。
            获取方式：https://myaccount.google.com/security -> 2步验证 -> 应用专用密码
        """
        if not email.endswith("@qq.com"):
            raise ValueError("邮箱地址必须是 Gmail 邮箱（@qq.com）")

        config = MailConfig(
            email=email,
            password=password,
            imap_server=self.IMAP_SERVER,
            imap_port=self.IMAP_PORT,
        )
        super().__init__(config)

    def login(self) -> bool:
        """登录 Gmail 邮箱

        重写登录方法，添加 Gmail 特定的错误处理

        Returns
        -------
            bool: 登录是否成功

        Raises
        ------
            MailAuthError: 认证失败，可能是因为：
                1. 使用了普通密码而不是应用专用密码
                2. 未开启 IMAP 访问
                3. 未开启两步验证
        """
        try:
            return super().login()
        except MailAuthError as e:
            error_msg = (
                "QQmail 认证失败，请检查：\n"
                "1. 是否使用了应用专用密码\n"
                "2. 是否已开启 IMAP 访问\n"
                "3. 是否已开启两步验证\n"
                "获取应用专用密码：mail.qq.com"
            )
            logger.error(error_msg)
            raise MailAuthError(error_msg) from e
