from loguru import logger

from .first_client import FirstClient
from .gmail_client import GmailClient
from .hotmail_client import HotmailClient
from .icloud_client import ICloudClient
from .imap_client import IMAPClient, SearchCriteria
from .qqmail_client import QQmailClient
from .rambler_client import RamblerClient


class EmailClient:
    """邮件客户端

    自动识别邮箱类型并创建对应的专用客户端实例。
    支持 Gmail、Hotmail/Outlook、iCloud 等常见邮箱。
    """

    _CLIENT_MAPPING: dict[str, type[IMAPClient]] = {}
    _initialized: bool = False

    def __init__(self, email: str, password: str):
        """初始化邮件客户端

        Args:
            email: 邮箱地址
            password: 密码

        Raises
        ------
            ValueError: 当邮箱格式不正确或不支持该邮箱类型时抛出
        """
        if not self._initialized:
            self._init_clients()

        self.client = self._create_client(email, password)

    def __getattr__(self, name):
        """代理所有方法到具体的客户端实例"""
        return getattr(self.client, name)

    @classmethod
    def _init_clients(cls) -> None:
        """初始化支持的邮件客户端"""
        if cls._initialized:
            return

        # 注册所有专用客户端
        for client_class in [GmailClient, HotmailClient, ICloudClient, RamblerClient, QQmailClient, FirstClient]:
            cls._register_client_class(client_class)

        cls._initialized = True

    @classmethod
    def _register_client_class(cls, client_class: type[IMAPClient]) -> None:
        """注册客户端类"""
        if not (client_class.SUPPORTED_DOMAINS and client_class.IMAP_SERVER):
            return

        for domain in client_class.SUPPORTED_DOMAINS:
            cls._CLIENT_MAPPING[domain.lower()] = client_class

    @classmethod
    def _create_client(cls, email: str, password: str) -> IMAPClient:
        """创建具体的邮件客户端实例"""
        # 验证并获取域名
        if "@" not in email:
            raise ValueError("邮箱地址格式不正确：缺少 @ 符号")
        domain = email.split("@")[1].lower()

        # 获取对应的客户端类
        client_class = cls._CLIENT_MAPPING.get(domain)
        if client_class:
            logger.info(f"使用专用客户端: {client_class.__name__}")
            return client_class(email, password)
        else:
            client_class = cls._CLIENT_MAPPING.get("first.mail")
            return client_class(email, password)
        raise ValueError(f"未支持的邮箱类型: {domain}\n请检查邮箱地址是否正确，或联系开发者添加支持")

    @classmethod
    def register_client(cls, domain: str, client_class: type[IMAPClient]) -> None:
        """注册新的专用客户端类

        Args:
            domain: 邮箱域名
            client_class: 客户端类
        """
        cls._CLIENT_MAPPING[domain.lower()] = client_class
        logger.info(f"已注册新的专用客户端: {domain} -> {client_class.__name__}")

    def set_client_id(self, client_id: str):
        """设置客户端ID"""
        # 检查客户端是否支持设置客户端ID
        if not hasattr(self.client, "set_client_id"):
            return

        self.client.set_client_id(client_id)

    def get_latest_email(self):
        """获取最新邮件"""
        return self.client.get_latest_email()

    def search_emails_with_retry(self, query: SearchCriteria, max_retries: int = 3):
        """搜索邮件，并重试"""
        return self.client.search_emails_with_retry(query, max_retries)

    def search_emails(self, query: SearchCriteria):
        """搜索邮件"""
        return self.client.search_emails(query)

    def search_emails_common(self, email, password, mail_proxy, subject: str = None, from_addr: str = None, retry=3):
        """搜索邮件, 如果未找到则获取最新邮件"""
        email_client = EmailClient(email, password)
        search_criteria = SearchCriteria(
            to=mail_proxy or email,
            from_addr=from_addr,
            subject=subject,
        )

        try:
            # 尝试获取未读邮件
            emails = email_client.search_emails_with_retry(search_criteria, max_retries=retry)
        except Exception as e:
            logger.error(f"{email} 获取未读邮件时发生错误: {e}")
            emails = []

        try:
            if not emails:
                lasted_email = email_client.get_latest_email()
                if not lasted_email:
                    logger.error(f"{email} 未找到验证码邮件")
                    return None
                emails.append(lasted_email)
        except Exception as e:
            logger.error(f"{email} 获取最新邮件时发生错误: {e}")
            return None

        return emails

    def delete_email(self, email_id: str, folder: str = "INBOX"):
        """删除邮件"""
        return self.client.delete_email(email_id, folder)

    def logout(self):
        """登出"""
        return self.client.logout()

    @classmethod
    def is_supported(cls, email: str) -> bool:
        """检查邮箱是否支持

        Args:
            email: 邮箱地址

        Returns
        -------
            bool: 如果支持返回 True，否则返回 False

        Examples
        --------
            >>> EmailClient.is_supported("<EMAIL>")
            True
            >>> EmailClient.is_supported("<EMAIL>")
            False
        """
        if not cls._initialized:
            cls._init_clients()

        try:
            if "@" not in email:
                return False
            domain = email.split("@")[1].lower()
            return domain in cls._CLIENT_MAPPING
        except:
            return False


# 在模块导入时初始化
EmailClient._init_clients()
