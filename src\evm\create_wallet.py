from bip_utils import (
    Bip39MnemonicGenerator,
    Bip39WordsNum,
    Bip39SeedGenerator,
    Bip44,
    Bip44Coins,
    Bip44Changes,
)
import csv
from datetime import datetime
from src.utils.secure_encryption import SecureEncryption
from src.utils.common import get_project_root_path


def generate_bip39_mnemonic(
    words_num: Bip39WordsNum = Bip39WordsNum.WORDS_NUM_24,
) -> str:
    """
    生成 EVM 助记词（BIP-39 标准）。

    :param words_num: 助记词的单词数量，默认为 24 个单词。
    :return: 生成的助记词字符串。
    """
    mnemonic = Bip39MnemonicGenerator().FromWordsNumber(words_num)
    return mnemonic.ToStr()


def get_aptos_address(mnemonic: str, index: int = 0) -> str:
    """
    根据助记词获取 Aptos 钱包地址

    Args:
        mnemonic: BIP-39 助记词
        index: 派生路径索引，默认为 0

    Returns:
        str: Aptos 钱包地址
    """
    utils = AddressUtils(mnemonic)
    return utils.generate_address(Bip44Coins.APTOS, index)


def get_address_and_private_key(mnemonic: str, index: int = 0, is_encrypt=True) -> dict:
    coin_types = {
        "evm": Bip44Coins.ETHEREUM,
    }

    utils = AddressUtils(mnemonic)

    result = {}
    for coin, coin_type in coin_types.items():
        result[f"{coin}_address"] = utils.generate_address(coin_type, index)
        pk = utils.generate_private_key(coin_type, index)
        if is_encrypt:
            pk = SecureEncryption().encrypt_data(pk)
        result[f"{coin}_private_key"] = pk

    return result


def generate_mnemonics_to_csv(
    count: int,
    output_path: str = None,
    is_encrypt: bool = True,
    words_num: Bip39WordsNum = Bip39WordsNum.WORDS_NUM_24,
) -> str:
    """
    批量生成助记词并写入到 CSV 文件中

    Args:
        count: 生成助记词的数量
        output_path: CSV 文件输出路径，如果为 None 则使用默认路径
        words_num: 助记词的单词数量，默认为 24 个单词

    Returns:
        str: 生成的 CSV 文件路径
    """
    if output_path is None:
        # 获取当前日期并格式化为字符串作为默认文件名
        date_str = datetime.now().strftime("%Y%m%d")
        output_path = f"{get_project_root_path()}/data/wallets_{date_str}_{count}.csv"

    mnemonics_list = [generate_bip39_mnemonic(words_num) for _ in range(count)]

    with open(output_path, "w", newline="") as csvfile:
        fieldnames = [
            "id",
            "mnemonic",
            "evm_address",
            "evm_private_key",
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for i, mnemonic in enumerate(mnemonics_list, start=1):
            result = get_address_and_private_key(mnemonic, is_encrypt=is_encrypt)
            if is_encrypt:
                mnemonic = SecureEncryption().encrypt_data(mnemonic)
            row = {"id": i, "mnemonic": mnemonic, **result}
            writer.writerow(row)

    return output_path  # 返回生成的文件路径


class AddressUtils:
    def __init__(self, mnemonic: str):
        self.mnemonic = mnemonic
        self.seed = self._generate_seed_from_mnemonic(mnemonic)

    def _generate_seed_from_mnemonic(self, mnemonic: str) -> bytes:
        return Bip39SeedGenerator(mnemonic).Generate()

    def _format_private_key(self, private_key: str) -> str:
        if not private_key.startswith("0x"):
            private_key = "0x" + private_key
        return private_key

    def _create_bip44_context(self, coin_type) -> Bip44:
        return (
            Bip44.FromSeed(self.seed, coin_type)
            .Purpose()
            .Coin()
            .Account(0)
            .Change(Bip44Changes.CHAIN_EXT)
        )

    def generate_address(self, coin_type, index: int = 0) -> str:
        bip44_ctx = self._create_bip44_context(coin_type)
        address_ctx = bip44_ctx.AddressIndex(index)
        return address_ctx.PublicKey().ToAddress()

    def generate_private_key(self, coin_type, index: int = 0) -> str:
        bip44_ctx = self._create_bip44_context(coin_type).AddressIndex(index)
        private_key = bip44_ctx.PrivateKey().Raw().ToHex()
        return self._format_private_key(private_key)
