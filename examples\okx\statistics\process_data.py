import pandas as pd


def process_data():
    # 读取两个CSV文件
    try:
        satlayer2_df = pd.read_csv("satlayer2_chrome.csv")
        chrome_df = pd.read_csv("../../data/chrome.csv")

        # 筛选status为空或为0的记录
        filtered_df = satlayer2_df[(satlayer2_df["status"].isna()) | (satlayer2_df["status"] == 0)]

        # 根据钱包地址匹配数据
        merged_df = pd.merge(filtered_df, chrome_df, left_on="wallet_address", right_on="evm_address", how="left")

        # 保存结果到新的CSV文件
        output_file = "matched_results.csv"
        merged_df.to_csv(output_file, index=False)
        print(f"处理完成，结果已保存到 {output_file}")

    except Exception as e:
        print(f"处理数据时出错: {str(e)}")


if __name__ == "__main__":
    process_data()
