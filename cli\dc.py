import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.hhcsv import H<PERSON>SV

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/dc.log", rotation="10MB", level="SUCCESS")


def filter_data_by_indices(data, indices: list[int]) -> list[dict]:
    filtered = []
    for row in data:
        try:
            row_id = row.get("id")
            if row_id is None:
                logger.warning(f"跳过缺少id的数据: {row}")
                continue

            if int(row_id) in indices:
                filtered.append(row)
        except ValueError:
            logger.warning(f"无法将id转换为整数: {row}")
            continue
    return filtered


def _query_all(file_path: str):
    hhcsv = HHCSV(file_path)
    return hhcsv.query()


def _update_password(type: BrowserType, index: int, encrypt: bool = True):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        return browser.update_password_discord(encrypt)
    except Exception as e:
        logger.error(f"{index} 更新Discord密码失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()


def _update_dc_token(type: BrowserType, index: str):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        return browser.update_dc_token()
    except Exception as e:
        logger.error(f"{index} 更新Discord token失败: {e}")
        return False
    finally:
        browser.close_page()


def _save_dc_username_to_csv(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        return browser.get_dc_user_name()
    except Exception as e:
        logger.error(f"【{index}】更新Discord用户名失败: {e}")
        return False
    finally:
        browser.close_page()


def _get_dc_email(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        return browser.get_dc_email()
    except Exception as e:
        logger.error(f"【{index}】获取Discord邮箱失败: {e}")
        return False
    finally:
        browser.close_page()


def _update_email(
    type: BrowserType,
    id: str,
    dc_password: str | None = None,
    current_email: str | None = None,
    current_email_password: str | None = None,
    new_email: str | None = None,
    new_email_password: str | None = None,
    new_email_proxy: str | None = None,
):
    browser = BrowserController(type, str(id))
    if not dc_password:
        dc_password = browser.browser_config.dc_password
    if not current_email:
        current_email = browser.browser_config.dc_email
    if not current_email_password:
        current_email_password = browser.browser_config.dc_email_password
    if not new_email:
        new_email = browser.browser_config.email
        new_email_proxy = browser.browser_config.proxy_email
    if not new_email_password:
        new_email_password = browser.browser_config.email_password
    try:
        result = browser.update_email_discord(
            dc_password,
            current_email,
            current_email_password,
            new_email,
            new_email_password,
            new_email_proxy,
        )
        if result:
            browser.close_page()
        return result
    except Exception as e:
        logger.error(f"{id} 更新Discord邮箱失败: {e}")
        return False


def _update_dc_id(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        result = browser.update_dc_id()
        if result:
            browser.close_page()
    except Exception as e:
        logger.error(f"{index} 更新Discord id失败: {e}")


def _get_dc_2fa_key(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        return browser.get_dc_2fa_key()
    except Exception as e:
        logger.error(f"【{index}】 开启Discord 2FA失败: {e}")
        return False
    finally:
        browser.close_page()


def _join_dc_server(
    type: BrowserType,
    index: str,
    invite_code: str,
    close: bool = False,
    need_wait_captcha: bool = True,
):
    browser = BrowserController(type, str(index))
    try:
        result = browser.join_dc_server(invite_code, need_wait_captcha)
        if result:
            if close:
                browser.close_page()
    except Exception as e:
        logger.error(f"{index} 加群失败: {e}")


def _reset_password(type: BrowserType, index: str, close: bool = False):
    browser = BrowserController(type, str(index))
    try:
        # 清除discord.com的站点数据
        browser.page
        browser.clear_site_data("discord.com")

        # 重置密码
        result = browser.reset_password()
        if result:
            browser.close_page()
        return result
    except Exception as e:
        logger.error(f"{index} 重置密码失败: {e}")
        return False
    finally:
        if close and browser:
            browser.close_page()


def _logout(browser_type: BrowserType, index: str, close: bool = False):
    browser = BrowserController(browser_type, str(index))
    try:
        # 登出dc
        result = browser.logout_dc()
        if result and close:
            browser.close_page()
        return result
    except Exception as e:
        logger.error(f"{index} 重置密码失败: {e}")
        return False
    finally:
        if close and browser:
            browser.close_page()


@click.group()
def cli():
    pass


# 登录discord
@cli.command("login")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def login_dc(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            result = browser.login_discord()
            if result:
                browser.close_page()
                logger.success(f"{_index} 登录Discord成功")

        except Exception as e:
            logger.error(f"{_index} 登录Discord失败: {e}")


# 更新discord密码
@cli.command("up")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_password(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _update_password(type, _index)
        except Exception as e:
            logger.error(f"{_index} 更新X密码失败: {e}")


# 更新discord密码，不加密密码
@cli.command("upn")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_password(index, type):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        if _update_password(type, _index, encrypt=False):
            logger.success(f"{_index} 更新Discord密码成功")
        else:
            logger.error(f"{_index} 更新Discord密码失败")
            fail_indices.append(str(_index))
    if len(fail_indices) > 0:
        logger.error(f"token 更新失败：{','.join(fail_indices)}")


# 更新discord密码


@cli.command("ue")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_email(index, type):

    required_cols = [
        "id",
        "dc_password",
        "dc_email",
        "dc_email_password",
        "new_email",
        "new_email_password",
        "new_email_proxy",
    ]
    file_path = input(
        f"请输入Excel数据文件的完整路径(表头格式：{required_cols})：\n"
        "提示：\n"
        "1. 支持的文件格式：csv\n"
        "2. 支持拖拽文件到终端\n"
        "3. 输入 'e' 退出程序\n"
        "4. 文件中id列必须跟data/*.csv中的id列一致, 否则会更新数据失败\n"
        "> "
    ).strip()

    file_path = file_path.strip("'\"")
    data = _query_all(file_path)

    # 根据index过滤数据
    if index:
        try:
            indices = parse_indices(index)
            filtered_data = filter_data_by_indices(data, indices)
            if not filtered_data:
                logger.warning(f"未找到匹配的数据，请检查输入的序号 {index} 是否正确")
                return
        except ValueError as e:
            logger.error(f"解析索引出错: {e}")
            return
    else:
        filtered_data = data

    fail_indices = []
    for row in filtered_data:
        _id = row.get("id")
        try:
            # 从 row 中获取所需数据
            dc_password = row.get("dc_password", "")
            current_email = row.get("dc_email", "")
            current_email_password = row.get("dc_email_password", "")
            new_email = row.get("new_email", "")
            new_email_password = row.get("new_email_password", "")
            new_email_proxy = row.get("new_email_proxy", "")
            # logger.info(
            #     f"{_index} 更新dc邮箱: {dc_password} {current_email} {current_email_password} {new_email} {new_email_password} {new_email_proxy}"
            # )
            # 调用更新函数
            result = _update_email(
                type,
                _id,
                dc_password,
                current_email,
                current_email_password,
                new_email,
                new_email_password,
                new_email_proxy,
            )
            if result:
                logger.success(f"{_id} 更新dc邮箱成功")
            else:
                logger.error(f"{_id} 更新dc邮箱失败")
                fail_indices.append(str(_id))
        except Exception as e:
            logger.error(f"{_id} 更新dc邮箱失败: {e}")
            fail_indices.append(str(_id))
    if len(fail_indices) > 0:
        logger.error(f"邮箱更新失败：{','.join(fail_indices)}")


# 更新discord邮箱，新邮箱使用浏览器配置文件的email和proxy_email
@cli.command("uen")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_email_new(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            # 调用更新函数
            _update_email(type, _index)
            logger.success(f"{_index} 更新dc邮箱成功")
        except Exception as e:
            logger.error(f"{_index} 更新dc邮箱失败: {e}")


# 更新discord token
@cli.command("token")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_dc_token(index: str, type: BrowserType):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            if not _update_dc_token(type, _index):
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"{_index} 更新Discord token失败: {e}")
            fail_indices.append(str(_index))
    if len(fail_indices) > 0:
        logger.error(f"token 更新失败：{','.join(fail_indices)}")


# 更新discord token
@cli.command("email")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_dc_email(index: str, type: BrowserType):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            if not _get_dc_email(type, str(_index)):
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"{_index} 更新Discord 邮箱失败: {e}")
            fail_indices.append(str(_index))
    if len(fail_indices) > 0:
        logger.error(f"邮箱 更新失败：{','.join(fail_indices)}")


# 更新discord token
@cli.command("logout")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-c", "--close", is_flag=True, default=False, help="是否关闭浏览器")
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def logout(index: str, type: BrowserType, close: bool):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            if not _logout(type, str(_index)):
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"{_index} Discord 登出失败: {e}")
            fail_indices.append(str(_index))
    if len(fail_indices) > 0:
        logger.error(f"Discord 登出失败：{','.join(fail_indices)}")


# 重置discord密码
@cli.command("reset")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-c", "--close", is_flag=True, default=False, help="是否关闭浏览器")
def reset_password(index, type, close):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _reset_password(type, _index, close)
        if result:
            logger.success(f"{_index} 重置密码成功")
        else:
            logger.error(f"{_index} 重置密码失败")
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        logger.error(f"重置密码失败IDS: {','.join(failed_indices)}")


# 更新discord id
@cli.command("ui")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_dc_id(index: str, type: BrowserType):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _update_dc_id(type, _index)
        except Exception as e:
            logger.error(f"{_index} 更新Discord id失败: {e}")


# 保存discord用户名到csv
@cli.command("us")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def save_dc_username(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        if not _save_dc_username_to_csv(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Discord更新用户名失败: {idxs}")


# 保存discord用户名到csv
@cli.command("fa")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def get_dc_2fa_key(index, type):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        if not _get_dc_2fa_key(type, _index):
            fail_indices.append(str(_index))

    if len(fail_indices) > 0:
        logger.error(f"token 更新失败：{','.join(fail_indices)}")


# 加群，无验证，无角色的群
@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--code", type=str, prompt="请输入邀请码", help="群邀请码")
@click.option("-c", "--close", is_flag=True, default=False, help="是否关闭浏览器")
@click.option("-w", "--wait", is_flag=True, default=True, help="是否等待验证码")
def join_server(index, type, invite_code, close, wait):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _join_dc_server(type, _index, invite_code, close, wait)
        except Exception as e:
            logger.error(f"{_index} 加群失败: {e}")


# 更新discord token
# python3 cli/dc.py token  -i 1-10

# 更新discord id
# python3 cli/dc.py ui  -i 1-10

# 更新discord邮箱
# python3 cli/dc.py ue  -i 1-10

# 更新discord邮箱，新邮箱使用浏览器配置文件的email和proxy_email
# python3 cli/dc.py uen  -i 1-10

# 更新discord密码
# python3 cli/dc.py up  -i 1-10

# 更新discord密码，不加密密码
# python3 cli/dc.py upn  -i 1-10

# 登录discord
# python3 cli/dc.py login  -i 1-10

# 加群
# python3 cli/dc.py join -t ads -i 1-10 -u https://discord.com/invite/Q3HWbDQVQS

# 开启2FA
# python3 cli/dc.py fa -t ads -i 1-10

if __name__ == "__main__":
    cli()
    # _logout(BrowserType.CHROME, "1")
    # _get_dc_email(BrowserType.CHROME, "2")
    # _update_dc_token(BrowserType.ADS, "56")
    # _index = "3"
    # _reset_password(BrowserType.ADS, "49", False)
    # update_email(_index, default_browser_type)
    # _join_dc_server(BrowserType.CHROME, "2", "aYb46pKNCv")
