import click
from loguru import logger
from src.evm.create_wallet import generate_mnemonics_to_csv


@click.group()
def cli():
    pass


@cli.command("g")
@click.option(
    "-n", "--number", type=int, default=100, prompt="请输入生成数量", help="生成数量"
)
@click.option(
    "-e",
    "--is_encrypt",
    type=bool,
    default=True,
    help="是否加密",
)
def generate_wallet(number: int, is_encrypt: bool = True) -> None:
    """生成钱包助记词并保存到CSV文件"""
    try:
        output_file = generate_mnemonics_to_csv(count=number, is_encrypt=is_encrypt)
        logger.success(f"钱包助记词已成功保存到 {output_file} 文件")
    except Exception as e:
        logger.error(f"生成钱包助记词时发生错误: {e}")


# 生成 1000 个助记词并写入 CSV 文件
# python3 cli/evm.py g -n 1000 -e False

if __name__ == "__main__":
    cli()
