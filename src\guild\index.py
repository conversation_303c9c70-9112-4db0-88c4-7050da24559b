import click
import random
import os
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import  get_project_root_path
from time import sleep
from retry import retry
from src.browsers.operations import try_click

from src.utils.element_util import click_on_image
from src.utils.thread_executor import ThreadExecutor
from src.wallets.okx_wallet import OKXWallet


class Guild:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def _connect_wallet(self, lasted_tab):

        try_count = 6
        for i in range(try_count):

            if "connect" not in lasted_tab.url:
                if lasted_tab.ele("What can I help you with today", timeout=10):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

            connect_wallet_button = lasted_tab.ele(
                "x://button[.='Connect Wallet']", timeout=5
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("Continue with a wallet").click()
                sleep(2)
                lasted_tab.ele("x://button[@title='okx']").click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
                continue

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _check_cf_shield(self, lasted_tab):

        for _ in range(6):

            # 1. 判断是否在Dobby Battle Arena页面
            if "Dobby Battle Arena" in lasted_tab.title:
                return True

            # 2. 判断是否在CF盾页面
            div_ele = lasted_tab.ele("x://div[@id='EQIhq6']/div/div")
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            if iframe:
                try:
                    logger.warning(f"【{self.id}】 在CF盾页面")

                    checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                    if not checkbox:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                    checkbox.wait.has_rect(timeout=15)

                    if os.name == "nt":  # Windows
                        image_path = (
                            f"{get_project_root_path()}/story/img/check_box.jpg"
                        )
                    else:
                        image_path = (
                            f"{get_project_root_path()}/story/img/check_box.png"
                        )
                    click_on_image(image_path)
                    sleep(3)
                    if lasted_tab.ele("What can I help you with today"):
                        import pyautogui

                        pyautogui.moveTo(200, 200, duration=0.2)
                        return True
                    else:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                except Exception as e:
                    logger.error(f"【{self.id}】 过CF盾失败: {e}")
                    sleep(3)
                    continue

        return False

    # 解析方法
    def _parse_openai_response(self, response_text):
        import re

        try:
            # 使用正则表达式提取 \n0 和 \ne 之间的内容
            match = re.search(r'\n0:"(.*?)"\ne:', response_text, re.DOTALL)

            if match:
                # 提取匹配的内容
                content = match.group(1)

                # 移除 \n0:" 和 引号
                content = content.replace('\n0:"', "").replace('"', "")

                # 合并内容
                content = " ".join(content.split())

                return content

            return None
        except Exception as e:
            logger.error(f"【{self.id}】 解析OpenAI响应失败: {e}")
            return None

    def get_answers(self, lasted_tab):
        res = lasted_tab.listen.wait(timeout=30, count=2)

        if not res:
            logger.error(f"【{self.id}】 获取答案失败")
            return False

        answers = {}
        for packet in res:
            body = packet.response.raw_body
            post_data = packet.request.postData
            id = post_data.get("id")
            result = self._parse_openai_response(body)
            if not result:
                logger.error(f"【{self.id}】 解析OpenAI响应失败")
                return False
            answers[id] = result
        return answers

    def _click_button(self, lasted_tab, button_text):
        try_click(lasted_tab, f"x://button[contains(.,'{button_text}')]")

    def _get_used_times(self, lasted_tab):
        try:
            messages_remaining = lasted_tab.ele(
                "x://p[contains(.,'messages remaining')]", timeout=30
            )
            if messages_remaining:
                return int(
                    messages_remaining.text.replace("/10 messages remaining", "")
                )
            else:
                logger.error(f"【{self.id}】 获取剩余次数失败")
                return 0
        except Exception as e:
            logger.error(f"【{self.id}】 获取剩余次数失败: {e}")
            return 0

    def _task_ask(self, lasted_tab):

        used_times = self._get_used_times(lasted_tab)
        if used_times == 0:
            logger.error(f"【{self.id}】 剩余次数为0, 停止投票")
            return True

        ai = AI()
        questions = ai.generate_question()

        # 再加上序号
        for i, q in enumerate(questions):
            try:
                logger.info(f"【{self.id}】 第{i+1}次问题: {q}")
                if not q:
                    logger.error(f"【{self.id}】 生成问题失败")
                    continue

                textarea = lasted_tab.ele("x://textarea")
                textarea.clear(True)
                sleep(random.randint(1, 3))
                textarea.input(q)

                lasted_tab.listen.start("https://dobby-arena.sentient.xyz/api/chat")
                try_click(lasted_tab, "x://button[@type='submit']", timeout=10)
                sleep(1)

                answers = self.get_answers(lasted_tab)
                if not answers:
                    logger.error(f"【{self.id}】 获取答案失败")
                    continue

                logger.info(f"【{self.id}】 第{i+1}次问题: {q}, 答案: {answers}")

                battle1 = answers.get("battle1")
                battle2 = answers.get("battle2")
                result = ai.compare_answers(q, battle1, battle2)
                click_text = ""
                if result == "a":
                    logger.info(f"【{self.id}】 第{i+1}次问题: {q}, 答案A 更好")
                    click_text = "Left is Better"
                elif result == "b":
                    logger.info(f"【{self.id}】 第{i+1}次问题: {q}, 答案B 更好")
                    click_text = "Right is Better"
                elif result == "0":
                    logger.info(
                        f"【{self.id}】 第{i+1}次问题: {q}, 答案A 和 B 质量相近"
                    )
                    click_text = "Tie"
                else:
                    logger.info(f"【{self.id}】 第{i+1}次问题: {q}, 答案A 和 B 都不好")
                    click_text = "Both are Bad"

                self._click_button(lasted_tab, click_text)

                if lasted_tab.ele("Vote submitted successfully"):
                    used_times = self._get_used_times(lasted_tab)
                    logger.success(f"【{self.id}】 投票成功, 剩余次数: {used_times}")
                    if used_times == 0:
                        logger.error(f"【{self.id}】 剩余次数为0, 停止投票")
                        return True

                    sleep(random.randint(2, 5))
                    continue

                logger.error(f"【{self.id}】 第{i+1}次问题: {q}, 投票失败")
                continue

            except Exception as e:
                logger.error(f"【{self.id}】 第{i+1}次提问失败: {e}")
                continue

        return True

    @retry(tries=3, delay=1)
    def task(self):
        try:
            # 0. 初始化
            self.browser_controller.okx_wallet_login()

            # 1. 初始化页面
            lasted_tab = self.page.new_tab("https://dobby-arena.sentient.xyz/")
            sleep(5)

            # 2. 判断是否在CF盾页面
            result = self._check_cf_shield(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 过CF盾失败")
                return False

            # 3. 连接钱包
            result = self._connect_wallet(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return False

            # 4. 点击开始
            self._task_ask(lasted_tab)

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")

    def _get_today_clicks(self):
        """获取今日已点击次数"""
        return self.today_clicks





if __name__ == "__main__":
    pass
