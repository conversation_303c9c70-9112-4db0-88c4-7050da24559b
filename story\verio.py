import os
import random
import time

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3_with_proxy
from src.evm.erc20 import ERC20
from decimal import Decimal, ROUND_DOWN
from src.evm.erc20_utils import get_transaction, send_transaction, formatTokenAmount
from web3.types import TxParams
from src.utils.secure_encryption import SecureEncryption


def generate_random_amount() -> float:
    """生成随机金额，范围从配置文件读取，保留3位小数"""
    min_amount = float(os.getenv("MIN_AMOUNT", "0.001"))
    max_amount = float(os.getenv("MAX_AMOUNT", "0.009"))
    amount = random.uniform(min_amount, max_amount)
    return round(amount, 3)


class StoryVerio(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "verio"
        self.wallet_address = self.browser_config.evm_address

        pk = self.browser_config.evm_private_key
        if SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        if not pk:
            raise Exception(f"[{self.browser_id}]  私钥为空")

        self.private_key = pk

        self.verio_contract_address = "******************************************"
        self.verio_abi = [
            {
                "inputs": [],
                "name": "stake",
                "outputs": [],
                "stateMutability": "payable",
                "type": "function",
            },
            {
                "inputs": [{"name": "amount", "type": "uint256"}],
                "name": "unstake",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]
        self.verio_vIP_contract_address = "******************************************"
        self.PiperXswapV2Router02 = "0x8812d810EA7CC4e1c3FB45cef19D6a7ECBf2D85D"
        self.PiperXswapV2Router02_abi = [
            {
                "inputs": [
                    {"name": "amountIn", "type": "uint256"},
                    {"name": "path", "type": "address[]"},
                ],
                "name": "getAmountsOut",
                "outputs": [{"name": "amounts", "type": "uint256[]"}],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "amountIn", "type": "uint256"},
                    {"name": "amountOutMin", "type": "uint256"},
                    {"name": "path", "type": "address[]"},
                    {"name": "to", "type": "address"},
                    {"name": "deadline", "type": "uint256"},
                ],
                "name": "swapExactTokensForETH",
                "outputs": [{"name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]
        self.WIP = "******************************************"
        self.withdraw_contract_address = "******************************************"
        self.withdraw_contract_abi = [
            {
                "inputs": [{"name": "recipient", "type": "address"}],
                "name": "getUnbondedWithdrawalsByRecipient",
                "outputs": [
                    {
                        "components": [
                            {"name": "amount", "type": "uint256"},
                            {"name": "unbondedTimestamp", "type": "uint256"},
                        ],
                        "type": "tuple[]",
                    }
                ],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [],
                "name": "withdraw",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]
        self.catalog_contract_address = "0x8b1e8a582edaE30a0b0fEd9ecb7F17C6C92959ba"
        self.catalog_contract_abi = [
            {
                "inputs": [
                    {"name": "ip", "type": "address"},
                    {"name": "isCapped", "type": "bool"},
                ],
                "name": "getPoolInfo",
                "outputs": [
                    {"name": "cap", "type": "uint256"},
                    {"name": "multiplier", "type": "uint256"},
                    {"name": "unstakeFee", "type": "uint256"},
                    {"name": "totalStaked", "type": "uint256"},
                ],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "ip", "type": "address"},
                    {"name": "isCapped", "type": "bool"},
                    {"name": "user", "type": "address"},
                ],
                "name": "getUserStakeAmountByIP",
                "outputs": [{"name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [],
                "name": "getMinStakeAmount",
                "outputs": [{"name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "ip", "type": "address"},
                    {"name": "user", "type": "address"},
                ],
                "name": "calculateUserRewards",
                "outputs": [
                    {
                        "components": [
                            {"name": "rewardToken", "type": "address"},
                            {"name": "rewardAmount", "type": "uint256"},
                        ],
                        "name": "rewardsInfo",
                        "type": "tuple[]",
                    }
                ],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [{"name": "ip", "type": "address"}],
                "name": "claimRewards",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "ip", "type": "address"},
                    {"name": "tryCapped", "type": "bool"},
                    {"name": "amount", "type": "uint256"},
                ],
                "name": "stake",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "ip", "type": "address"},
                    {"name": "isCapped", "type": "bool"},
                    {"name": "amount", "type": "uint256"},
                ],
                "name": "unstake",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]
        self.catalog_IPPY = "0xf78CB65a5c346C039215Ed1fD3501e7cfDCf6220"
        self.catalog_Piper = "0x16C470ecfdE917dAa20f37aA93600863c08DB17D"
        self.catalog_Freg = "0x78dF4B9d4F2AF91C28D7F57448041Cf18013Ad7A"
        self.catalog_Benny = "0x35a8d320e1861DcB9f43e20Bf435a04A09f2b568"
        self.catalog_Geter = "0x5F0B7a3253e5eFB00db18e77C99d58b183Fb409E"
        self.catalog_SmileWorld = "0xA5011126BC9d47AC6771521bF1e0801E71eac6Ee"
        self.catalog_Punka = "0xcB3dE62262eF9f67A6Fcb11EF63B968F82eFAB74"
        self.catalog_Globkin = "******************************************"
        self.catalogs = [
            self.catalog_IPPY,
            self.catalog_Piper,
            self.catalog_Freg,
            self.catalog_Benny,
            self.catalog_Geter,
            self.catalog_SmileWorld,
            self.catalog_Punka,
            self.catalog_Globkin,
        ]

    # catalog 获取pool info
    def _catalog_getPoolInfo(self, ip) -> list:
        catalog_contract = self.web3.eth.contract(
            address=self.catalog_contract_address, abi=self.catalog_contract_abi
        )
        cappedPoolInfo = catalog_contract.functions.getPoolInfo(ip, True).call()
        cappedCap = cappedPoolInfo[0]
        cappedTotalStaked = cappedPoolInfo[3]
        uncappedPoolInfo = catalog_contract.functions.getPoolInfo(ip, False).call()
        uncappedTotalStaked = uncappedPoolInfo[3]
        totalStaked = cappedTotalStaked + uncappedTotalStaked
        return [cappedCap, totalStaked]

    # catalog 获取最小stake amount
    def _catalog_getMinStakeAmount(self) -> int:
        catalog_contract = self.web3.eth.contract(
            address=self.catalog_contract_address, abi=self.catalog_contract_abi
        )
        min_stake_amount = catalog_contract.functions.getMinStakeAmount().call()
        return min_stake_amount

    # get user rewards, 合约返回值格式[('******************************************', 69455013246874)]，只需要把数量返回即可
    def _catalog_getUserRewards(self, ip) -> int:
        catalog_contract = self.web3.eth.contract(
            address=self.catalog_contract_address, abi=self.catalog_contract_abi
        )
        rewards = catalog_contract.functions.calculateUserRewards(
            ip, self.wallet_address
        ).call()
        return rewards[0][1]

    # claim rewards
    def _catalog_claimRewards(self, ip) -> bool:
        logger.info("[INFO] 执行catalog claimRewards")
        try:
            catalog_contract = self.web3.eth.contract(
                address=self.catalog_contract_address, abi=self.catalog_contract_abi
            )
            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }
            # 调用合约函数
            claim_function = catalog_contract.functions.claimRewards(ip)
            transaction = get_transaction(self.web3, tx_params, claim_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] claim rewards 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] catalog claimRewards 异常: {e}")
            return False

    # catalog stake
    def _catalog_stake(self, ip, amount, tryCapped=True) -> bool:
        logger.info("[INFO] 执行catalog stake")
        amount_bn = self.web3.to_wei(amount, "ether")
        # 先查询池子状态以及最小stake amount
        pool_info = self._catalog_getPoolInfo(ip)
        if pool_info[0] - pool_info[1] < amount_bn:
            logger.info("[INFO] 池子已满, 不执行任何操作")
            return False

        min_stake_amount = self._catalog_getMinStakeAmount()
        # 如果amount小于min_stake_amount，则不执行任何操作
        if amount_bn < min_stake_amount:
            logger.info(
                f"[INFO] {amount} 小于最小stake amount {formatTokenAmount(min_stake_amount, 5)}, 不执行任何操作"
            )
            return False

        try:
            # 先approve
            verio_vIP_contract = ERC20(self.verio_vIP_contract_address, self.web3)
            approved = verio_vIP_contract.approval(
                self.wallet_address,
                self.catalog_contract_address,
                self.private_key,
                self.web3.to_wei(amount, "ether"),
            )
            if not approved:
                logger.error("[ERROR] approve vIP to catalog 失败")
                return False

            catalog_contract = self.web3.eth.contract(
                address=self.catalog_contract_address, abi=self.catalog_contract_abi
            )
            # 构造交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }
            # 调用合约函数
            stake_function = catalog_contract.functions.stake(ip, tryCapped, amount_bn)
            transaction = get_transaction(self.web3, tx_params, stake_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] catalog stake 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] catalog stake 异常: {e}")
            return False

    # 获取vIP余额，返回人类可读余额，截取保留5位小数
    def _catalog_vIP_balance(self, ip) -> float:
        catalog_contract = self.web3.eth.contract(
            address=self.catalog_contract_address, abi=self.catalog_contract_abi
        )
        balance = catalog_contract.functions.getUserStakeAmountByIP(
            ip, True, self.wallet_address
        ).call()
        formatted = formatTokenAmount(balance, 5)  # 保留5位小数
        return float(formatted) if formatted else 0.0

    # catalog unstake
    def _catalog_unstake(self, ip, amount, tryCapped=True) -> bool:
        logger.info("[INFO] 执行catalog unstake")

        # 先查询vIP余额
        vIP_balance = self._catalog_vIP_balance(ip)
        logger.info(f"[INFO] vIP余额: {vIP_balance}")
        # 如果vIP余额小于amount，则不执行任何操作
        if vIP_balance < float(amount):
            logger.info(
                f"[INFO] vIP余额 {vIP_balance} 小于amount {amount}, 不执行任何操作"
            )
            return False
        amount_bn = self.web3.to_wei(amount, "ether")
        try:
            catalog_contract = self.web3.eth.contract(
                address=self.catalog_contract_address, abi=self.catalog_contract_abi
            )
            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }

            # 调用合约函数
            unstake_function = catalog_contract.functions.unstake(
                ip, tryCapped, amount_bn
            )
            transaction = get_transaction(self.web3, tx_params, unstake_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] catalog unstake 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] catalog unstake 异常: {e}")
            return False

    # 提现，首先查询余额，然后提现，打印出tx hash, 返回true/false.
    def _withdraw(self) -> bool:
        logger.info("[INFO] 执行withdraw")
        try:
            withdraw_contract = self.web3.eth.contract(
                address=self.withdraw_contract_address, abi=self.withdraw_contract_abi
            )
            unbondedWithdrawals = (
                withdraw_contract.functions.getUnbondedWithdrawalsByRecipient(
                    self.wallet_address
                ).call()
            )
            if len(unbondedWithdrawals) == 0:
                logger.info("[INFO] 没有��提现记录")
                return True

            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }

            # 调用合约函数
            withdraw_function = withdraw_contract.functions.withdraw()
            transaction = get_transaction(self.web3, tx_params, withdraw_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] withdraw 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] withdraw 异常: {e}")
            return False

    # unstake verio，打印出tx hash, 返回true/false.
    def _verio_unstake(self, amount) -> bool:
        logger.info("[INFO] 执行verio unstake")
        try:
            verio_contract = self.web3.eth.contract(
                address=self.verio_contract_address, abi=self.verio_abi
            )
            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }

            # 调用合约函数
            unstake_function = verio_contract.functions.unstake(
                self.web3.to_wei(amount, "ether")
            )
            transaction = get_transaction(self.web3, tx_params, unstake_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] verio unstake 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] verio unstake {amount} IP 异常: {e}")
            return False

    # unstake from PiperXswapV2Router02，打印出tx hash, 返回true/false.
    def _unstake_from_PiperXswapV2Router02(self, amount) -> bool:
        logger.info("[INFO] 执行unstake from PiperXswapV2Router02")
        try:
            # 首先做approve，无限额度
            verio_vIP_contract = ERC20(self.verio_vIP_contract_address, self.web3)
            approved = verio_vIP_contract.approval(
                self.wallet_address,
                self.PiperXswapV2Router02,
                self.private_key,
                self.web3.to_wei(amount, "ether"),
            )
            if not approved:
                logger.error("[ERROR] approve vIP to PiperXswapV2Router02 失败")
                return False

            PiperXswapV2Router02_contract = self.web3.eth.contract(
                address=self.PiperXswapV2Router02, abi=self.PiperXswapV2Router02_abi
            )
            amounts = PiperXswapV2Router02_contract.functions.getAmountsOut(
                self.web3.to_wei(amount, "ether"),
                [self.verio_vIP_contract_address, self.WIP],
            ).call()
            # 这个BigNumber，需要转换成人类可读的数值，保留5位小数，但是不做四舍五入，直接截断
            amount_out = formatTokenAmount(amounts[1], 5)
            logger.info(f"[INFO] receipt amounts: {amount_out}")

            deadline = int(time.time()) + 60 * 10  # 当前时间戳 + 10分钟

            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }

            # 调用合约函数
            swap_function = (
                PiperXswapV2Router02_contract.functions.swapExactTokensForETH(
                    self.web3.to_wei(amount, "ether"),
                    self.web3.to_wei(amount_out, "ether"),
                    [self.verio_vIP_contract_address, self.WIP],
                    self.wallet_address,
                    deadline,
                )
            )

            # 获取并发送交易
            transaction = get_transaction(self.web3, tx_params, swap_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] unstake from router 交易成功")
            return result

        except Exception as e:
            logger.error(
                f"[ERROR] unstake {amount} from PiperXswapV2Router02 异常: {e}"
            )
            return False

    # stake verio，打印出tx hash, 返回true/false
    def _verio_stake(self, amount) -> bool:
        logger.info("[INFO] 执行verio stake")
        # 检查amount是否大于0.1
        if float(amount) < 0.1:
            logger.error("[ERROR] verio stake amount 小于0.1")
            return False
        try:
            verio_contract = self.web3.eth.contract(
                address=self.verio_contract_address, abi=self.verio_abi
            )

            # 构建基础交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                "value": self.web3.to_wei(amount, "ether"),
            }

            stake_function = verio_contract.functions.stake()
            transaction = get_transaction(self.web3, tx_params, stake_function)
            if not transaction:
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] verio stake 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] verio stake {amount} IP 异常: {e}")
            return False

    # 获取vIP余额，返回人类可读余额，保留3位小数
    def _verio_vIP_balance(self) -> float:
        verio_vIP_contract = ERC20(self.verio_vIP_contract_address, self.web3)

        balance = verio_vIP_contract.balance_of(self.wallet_address)
        formattedValue = float(balance.quantize(Decimal("0.001"), rounding=ROUND_DOWN))
        return formattedValue

    # 获取钱包原生代币（IP）余额，返回人类可读余额，保留3位小数
    def get_native_balance(self) -> float:
        """
            获取钱包原生代币（IP）余额

        Returns:
                float: 格式化后的余额，保留3位小数
        """
        try:
            balance_wei = self.web3.eth.get_balance(self.wallet_address)
            formatted_balance = formatTokenAmount(balance_wei, 3)
            logger.info(f"钱包余额: {formatted_balance} IP")
            return formatted_balance
        except Exception as e:
            logger.error(f"获取钱包余额失败: {e}")
            return 0.0

    def should_execute(self, probability: float = 0.3) -> bool:
        """根据概率决定是否执行操作"""
        return random.random() < probability

    def get_random_amount(
        self, min_amount: float = 0.1, max_amount: float = 1.0, decimals: int = 3
    ) -> str:
        """生成指定范围内的随机金额，保留3位小数"""
        amount = random.uniform(min_amount, max_amount)
        return f"{amount:.{decimals}f}"

    def get_percentage_amount(
        self, balance: float, percentage: float = 0.1, decimals: int = 3
    ) -> str:
        """获取余额的百分比金额，保留3位小数"""
        amount = balance * percentage
        return f"{amount:.{decimals}f}"

    # 检查余额是否超过安全阈值
    def _check_balance_threshold(self, balance: float, threshold: float = 0.05) -> bool:
        """
        检查余额是否超过安全阈值

        Args:
            balance: 当前余额
            threshold: 安全阈值,默认0.05

        Returns:
            bool: 是否超过阈值
        """
        if balance < threshold:
            logger.warning(f"[WARNING] 当前余额 {balance} 低于安全阈值 {threshold}")
            return False
        return True

    # 生成有界的随机数
    def _generate_bounded_random(
        self, min_val: float, max_val: float, decimals: int = 3
    ) -> float:
        """
        生成有界的随机数

        Args:
            min_val: 最小值
            max_val: 最大值
            decimals: 小数位数,默认3位

        Returns:
            float: 随机数
        """
        # 确保边界有效
        if min_val > max_val:
            min_val, max_val = max_val, min_val

        # 生成随机数并格式化到指定小数位
        random_val = random.uniform(min_val, max_val)
        return round(random_val, decimals)

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 Verio 任务"""
        try:
            logger.info(f"[INFO] 开始执行 {self.browser_id} 的 Verio 任务")
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            self.web3 = get_web3_with_proxy(
                rpc_url, proxy, user_agent=self.browser_config.user_agent
            )

            # 1. verio stake 逻辑
            ip_balance = float(self.get_native_balance())
            vip_balance = self._verio_vIP_balance()

            logger.info(f"[INFO] 当前IP余额: {ip_balance}, vIP余额: {vip_balance}")

            # 检查IP余额是否超过安全阈值
            if self._check_balance_threshold(ip_balance, 0.15):
                if vip_balance < 0.1:
                    # 必须存款
                    stake_amount = min(
                        self._generate_bounded_random(0.1, 1, 2), ip_balance
                    )
                    logger.info(f"[INFO] 执行必要存款, 金额: {stake_amount}")
                    self._verio_stake(f"{stake_amount:.2f}")
                elif self.should_execute(0.3):
                    # 30%概率存款
                    stake_amount = min(
                        self._generate_bounded_random(0.1, 0.2, 2), ip_balance
                    )
                    logger.info(f"[INFO] 执行概率存款, 金额: {stake_amount}")
                    self._verio_stake(f"{stake_amount:.2f}")
                # 任务间随机延迟
                delay = random.randint(10, 30)
                logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                time.sleep(delay)

            # 2. unstake from PiperXswapV2Router02 逻辑
            vip_balance = self._verio_vIP_balance()  # 重新获取余额
            logger.info(f"[INFO] 当前vIP余额: {vip_balance}")

            if self._check_balance_threshold(vip_balance, 0.1) and self.should_execute(
                0.4
            ):
                unstake_amount = self.get_percentage_amount(vip_balance, 0.3, 2)
                logger.info(f"[INFO] 执行Router unstake, 金额: {unstake_amount}")
                self._unstake_from_PiperXswapV2Router02(unstake_amount)

                # 任务间随机延迟
                delay = random.randint(10, 30)
                logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                time.sleep(delay)

            # 3. verio unstake 逻辑
            vip_balance = self._verio_vIP_balance()  # 重新获取余额
            logger.info(f"[INFO] 当前vIP余额: {vip_balance}")

            if self._check_balance_threshold(vip_balance, 0.1) and self.should_execute(
                0.3
            ):
                unstake_amount = self.get_percentage_amount(vip_balance, 0.2, 2)
                logger.info(f"[INFO] 执行verio unstake, 金额: {unstake_amount}")
                self._verio_unstake(unstake_amount)

                # 任务间随机延迟
                delay = random.randint(10, 30)
                logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                time.sleep(delay)

            # 4. withdraw 逻辑
            if self.should_execute(0.1):  # 10%概率
                logger.info("[INFO] 执行withdraw操作")
                self._withdraw()

                # 任务间随机延迟
                delay = random.randint(10, 30)
                logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                time.sleep(delay)

            # 5. catalogs 逻辑
            # selected_catalogs = random.sample(self.catalogs, random.randint(1, min(3, len(self.catalogs))))
            random.shuffle(self.catalogs)
            logger.info(f"[INFO] 选择执行的catalogs: {self.catalogs}")

            for catalog in self.catalogs:
                # stake 逻辑
                vip_balance = self._verio_vIP_balance()  # 重新获取余额
                logger.info(f"[INFO] 当前vIP余额: {vip_balance}")

                if self._check_balance_threshold(
                    vip_balance, 0.1
                ) and self.should_execute(0.4):
                    stake_amount = min(
                        self._generate_bounded_random(0.1, 0.2), vip_balance
                    )
                    logger.info(
                        f"[INFO] 执行catalog stake, 目标: {catalog}, 金额: {stake_amount}"
                    )
                    self._catalog_stake(catalog, f"{stake_amount:.3f}")

                    # 任务间随机延迟
                    delay = random.randint(10, 30)
                    logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                    time.sleep(delay)

                # claim rewards 逻辑
                rewards = self._catalog_getUserRewards(catalog)
                logger.info(f"[INFO] catalog {catalog} 可领取奖励: {rewards}")
                if rewards > 0:
                    logger.info(f"[INFO] 执行claim rewards, 目标: {catalog}")
                    self._catalog_claimRewards(catalog)

                    # 任务间随机延迟
                    delay = random.randint(10, 30)
                    logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                    time.sleep(delay)

                # unstake 逻辑
                catalog_balance = self._catalog_vIP_balance(catalog)
                logger.info(f"[INFO] catalog {catalog} 当前余额: {catalog_balance}")

                if self._check_balance_threshold(
                    catalog_balance, 0.01
                ) and self.should_execute(0.3):
                    unstake_amount = self.get_percentage_amount(catalog_balance, 0.2, 2)
                    logger.info(
                        f"[INFO] 执行catalog unstake, 目标: {catalog}, 金额: {unstake_amount}"
                    )
                    self._catalog_unstake(catalog, unstake_amount)

                    if catalog != self.catalogs[-1]:  # 如果不是最后一个catalog,添加延迟
                        delay = random.randint(10, 30)
                        logger.info(f"[INFO] 任务间随机延迟 {delay} 秒")
                        time.sleep(delay)

            logger.success(f"[SUCCESS] {self.browser_id} Verio任务执行完成")
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False

    def close(self):
        pass
