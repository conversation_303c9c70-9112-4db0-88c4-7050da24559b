import email
import imaplib
from abc import ABC
from dataclasses import dataclass
from datetime import date
from email.header import decode_header, make_header
from time import sleep
from typing import Any, Optional, Tuple, Union
from zoneinfo import ZoneInfo

from dateutil.parser import parse as parsedate_to_datetime
from loguru import logger

from ..exceptions import MailAuthError, MailConnectionError, MailOperationError


@dataclass
class MailConfig:
    """邮件配置"""

    email: str  # 邮箱地址
    password: str  # 密码
    imap_server: str  # IMAP服务器地址
    imap_port: int = 993  # IMAP服务器端口


@dataclass
class SearchCriteria:
    """邮件搜索条件配置类"""

    folder: str = "INBOX"  # 文件夹
    limit: int = 10  # 限制数量
    offset: int = 0  # 偏移量
    sort_order: str = "DESC"  # 排序顺序
    subject: str | None = None  # 主题
    from_addr: str | None = None  # 发件人
    since: date | None = None  # 发送时间
    to: str | None = None  # 收件人
    is_read: bool | None = None  # None表示不限制，True表示已读，False表示未读

    def __post_init__(self):
        """参数验证"""
        if self.limit < 1:
            raise ValueError("limit 必须大于 0")
        if self.offset < 0:
            raise ValueError("offset 必须大于等于 0")
        if self.sort_order not in ["ASC", "DESC"]:
            raise ValueError("sort_order 必须是 'ASC' 或 'DESC'")


class IMAPClient(ABC):
    """IMAP邮件客户端

    处理IMAP协议的邮件客户端类，提供了基础的IMAP操作实现，
    包括连接、认证、搜索邮件等功能。
    """

    # 支持的域名列表
    SUPPORTED_DOMAINS: tuple[str, ...] = tuple()
    # IMAP服务器配置
    IMAP_SERVER: str | None = None
    IMAP_PORT: int = 993

    def __init__(self, config: MailConfig):
        self.config = config
        self.server = None
        self.imap_host = self.config.imap_server
        self.imap_port = self.config.imap_port

    def connect(self):
        """连接并登录邮箱服务器

        Returns
        -------
            self: 返回自身用于支持 with 语句
        """
        try:
            if self.server:
                try:
                    self.server.logout()
                except:
                    pass

            self.server = imaplib.IMAP4_SSL(self.imap_host, self.imap_port)
            # 确保在 connect 时就完成登录

            result, _ = self.server.login(self.config.email, self.config.password)
            if result != "OK":
                raise MailAuthError("认证失败")
            logger.success(f"登录成功: {self.config.email}")
            return self
        except Exception as e:
            if self.server:
                try:
                    self.server.logout()
                except:
                    pass
            self.server = None
            raise MailConnectionError(f"连接或登录失败: {str(e)}") from e

    def login(self) -> bool:
        """登录邮箱

        Returns
        -------
            bool: 登录是否成

        Raises
        ------
            MailAuthError: 认证失败
            MailConnectionError: 连接失败
        """
        try:
            if not self.server:
                self.connect()

            # 检查登录状态码
            result, _ = self.server.login(self.config.email, self.config.password)
            if result != "OK":
                raise MailAuthError("认证失败")
            logger.success(f"登录成功: {self.config.email}")
            return True
        except imaplib.IMAP4.error as e:
            if "authentication failed" in str(e).lower():
                raise MailAuthError("邮箱或密码错误") from e
            raise MailConnectionError(f"IMAP连接错误: {str(e)}") from e

    def logout(self) -> None:
        """登出邮箱"""
        self._reset_connection()

    def _validate_connection(self) -> None:
        """验证连接状态，确保已连接且已登录

        Raises
        ------
            MailConnectionError: 连接验证失败
        """
        # 最大重试次数
        MAX_RETRIES = 3
        retry_count = 0

        while retry_count < MAX_RETRIES:
            try:
                # 如果没有连接，直接建立新连接
                if not self.server:
                    self.connect()
                    return

                # 检查现有连接是否有效
                try:
                    self.server.noop()
                    return  # 连接正常，直接返回
                except (imaplib.IMAP4.error, ConnectionError, OSError) as e:
                    logger.warning(
                        f"连接已断开 (尝试 {retry_count + 1}/{MAX_RETRIES}): {str(e)}"
                    )
                    self._reset_connection()

                # 尝试重新连接
                self.connect()
                return

            except Exception as e:
                retry_count += 1
                if retry_count >= MAX_RETRIES:
                    raise MailConnectionError(
                        f"连接验证失败，已重试{MAX_RETRIES}次: {str(e)}"
                    ) from e

                logger.warning(f"重试连接 ({retry_count}/{MAX_RETRIES})")
                sleep(1)  # 重试前等待1秒

    def _reset_connection(self) -> None:
        """重置连接状态"""
        if self.server:
            try:
                # 尝试注销
                try:
                    self.server.logout()
                except:
                    pass

                # 尝试关闭连接
                try:
                    self.server.close()
                except:
                    pass

                # 尝试清理连接
                try:
                    self.server.shutdown()
                except:
                    pass
            finally:
                self.server = None

    def _select_folder(self, folder: str) -> None:
        """选择邮件文件夹"""
        try:
            result, _ = self.server.select(folder)
            if result != "OK":
                raise MailOperationError(f"无法访问文件夹: {folder}")
        except imaplib.IMAP4.error as e:
            raise MailOperationError(f"选择文件夹失败: {str(e)}") from e

    def _execute_search(self, search_criteria: str) -> list[bytes]:
        """执行搜索操作"""
        try:
            encoded_criteria = search_criteria.encode("utf-8")
            result, data = self.server.search("UTF-8", encoded_criteria)

            if result != "OK":
                raise MailOperationError("搜索邮件失败")

            if not data or not data[0]:
                return []

            return data[0].split()
        except Exception as e:
            logger.error(f"执行搜索操作失败: {str(e)}")
            raise MailOperationError(f"执行搜索操作失败: {str(e)}") from e

    def _handle_pagination(
        self, message_ids: list[bytes], offset: int, limit: int, sort_order: str
    ) -> list[bytes]:
        """处理分页逻辑"""
        if sort_order.upper() == "DESC":
            message_ids.reverse()

        total_messages = len(message_ids)
        if offset >= total_messages:
            return []

        start_idx = min(offset, total_messages)
        end_idx = min(offset + limit, total_messages) if limit > 0 else total_messages
        return message_ids[start_idx:end_idx]

    def _fetch_emails_info(
        self, message_ids: list[bytes], folder: str = None
    ) -> list[dict[str, Any]]:
        """获取邮件详细信息"""
        emails = []
        for msg_id in message_ids:
            try:
                # 添加日志以确认 msg_id 的类型
                logger.debug(f"处理邮件 ID: {msg_id}, 类型: {type(msg_id)}")

                email_info = self._fetch_single_email(msg_id, folder)
                if email_info:
                    emails.append(email_info)
            except Exception as e:
                logger.error(f"处理邮件 {msg_id} 时出错: {str(e)}")
                continue
        return emails

    def _fetch_single_email(
        self, msg_id: bytes | str | int, folder: str = None
    ) -> dict[str, Any] | None:
        """获取单个邮件的信息"""
        try:
            # 转换为 IMAP 可用的格式
            fetch_id = msg_id if isinstance(msg_id, bytes) else str(msg_id).encode()

            result, msg_data = self.server.fetch(fetch_id, "(BODY[])")
            if result != "OK" or not msg_data or not msg_data[0]:
                logger.warning(f"无法获取邮件 {msg_id}")
                return None

            email_message = email.message_from_bytes(msg_data[0][1])

            # 统一处理返回的 ID 格式
            msg_id_str = msg_id.decode() if isinstance(msg_id, bytes) else str(msg_id)

            return {
                "id": msg_id_str,
                "subject": self._decode_header(email_message["subject"]),
                "from": self._decode_header(email_message["from"]),
                "to": self._decode_header(email_message["to"]),
                "date": self._format_email_date(email_message["date"]),
                "content": self._get_text_from_email(email_message),
                "html_content": self._get_html_from_email(email_message),
                "message_id": email_message.get("message-id", ""),
                "cc": self._decode_header(email_message.get("cc", "")),
                "has_attachments": any(
                    part.get_filename() for part in email_message.walk()
                ),
                "folder": folder,
            }
        except Exception as e:
            logger.error(f"处理邮件 {msg_id} 时出错: {str(e)}")
            return None

    def _get_text_from_email(self, msg) -> str:
        """从邮件中提取纯文本内容"""
        text = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    try:
                        charset = part.get_content_charset() or "utf-8"
                        text += part.get_payload(decode=True).decode(
                            charset, errors="replace"
                        )
                    except Exception as e:
                        logger.warning(f"解码邮件内容时发生错误: {e}")
        else:
            try:
                charset = msg.get_content_charset() or "utf-8"
                text = msg.get_payload(decode=True).decode(charset, errors="replace")
            except Exception as e:
                logger.warning(f"解码邮件内容时发生错误: {e}")
        return text

    def _get_html_from_email(self, msg) -> str:
        """从邮件中提取HTML内容"""
        html = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/html":
                    try:
                        charset = part.get_content_charset() or "utf-8"
                        html = part.get_payload(decode=True).decode(
                            charset, errors="replace"
                        )
                        break  # 只返回第一个HTML内容
                    except Exception as e:
                        logger.warning(f"解码HTML内容时发生错误: {e}")
        else:
            try:
                if msg.get_content_type() == "text/html":
                    charset = msg.get_content_charset() or "utf-8"
                    html = msg.get_payload(decode=True).decode(
                        charset, errors="replace"
                    )
            except Exception as e:
                logger.warning(f"解码HTML内容时发生错误: {e}")
        return html

    def _decode_header(self, header_value: str | None) -> str:
        """解码邮件头信息"""
        if not header_value:
            return ""
        try:
            return str(make_header(decode_header(header_value)))
        except Exception:
            # 如果解码失败，返回原始值
            return str(header_value)

    def get_latest_email(self) -> dict[str, Any] | None:
        """获取最新邮件

        Returns
        -------
            Optional[Dict[str, Any]]: 邮件信息，如果没有邮件则返回 None

        Raises
        ------
            MailConnectionError: 连接错误
            MailOperationError: 操作错误
        """
        try:
            # 使用 search_emails 获取所有邮件
            search_criteria = SearchCriteria(limit=1)
            emails = self.search_emails(search_criteria)

            # 如果没有邮件，回 None
            if not emails:
                return None

            # 返回最新的邮件（列表中的最后一个）
            return emails[-1]

        except (MailConnectionError, MailOperationError) as e:
            raise e from e
        except Exception as e:
            raise MailOperationError(f"获取最新邮件失败: {str(e)}") from e

    def _format_email_date(
        self, date_str: str | None, timezone: str = "Asia/Shanghai"
    ) -> str:
        """格式化邮件日期

        Args:
            date_str: 原始日期字符串
            timezone: 目标时区，默认为北京时间

        Returns
        -------
            str: 格式化后的日期字符串
        """
        if not date_str:
            return ""

        try:
            # 解析邮件日期
            utc_date = parsedate_to_datetime(date_str)
            if not utc_date.tzinfo:
                utc_date = utc_date.replace(tzinfo=ZoneInfo("UTC"))

            # 转换到目标时区
            local_date = utc_date.astimezone(ZoneInfo(timezone))

            # 格式化日期
            return local_date.strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            logger.warning(f"日期格式化失败 ({date_str}): {str(e)}")
            return date_str

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.logout()

    def search_emails_with_retry(
        self,
        search_criteria: SearchCriteria,
        max_retries: int = 10,
        retry_delay: float = 1,
    ) -> list[dict[str, Any]]:
        """持续搜索直到找到邮件或达到最大尝试次数

        Args:
            search_criteria: 搜索条件
            max_retries: 最大重试次数，默认20次
            retry_delay: 重试间隔时间(秒)，默认1秒

        Returns
        -------
            List[Dict[str, Any]]: 搜索到的邮件列表

        Raises
        ------
            MailOperationError: 所有重试都失败后抛出异常
        """
        retry_count = 0

        # 获取所有可用文件夹
        try:
            available_folders = self.list_folders()

            # 定义可能的文件夹名称
            target_folders = [
                "INBOX",
                "Inbox",
                "Archive",
                "Junk",
                "Spam",
                "垃圾邮件",
                "Junk Email",
                "Bulk Mail",
                "Junk Email1",
            ]
            # 过滤出实际存在的文件夹
            folders_to_search = [
                folder for folder in target_folders if folder in available_folders
            ]

            if not folders_to_search:
                logger.warning("未找到任何目标文件夹，使用默认文件夹")
                folders_to_search = ["INBOX"]
        except Exception as e:
            logger.warning(f"获取文件夹列表失败: {str(e)}")
            folders_to_search = ["INBOX"]  # 降级使用基本文件夹

        while retry_count < max_retries:
            for folder in folders_to_search:
                try:
                    search_criteria.folder = folder
                    emails = self.search_emails(search_criteria)
                    if emails:  # 如果找到邮件，直接返回
                        logger.info(f"在文件夹 {folder} 中找到邮件")
                        return emails

                except Exception as e:
                    logger.error(f"在文件夹 {folder} 搜索时发生错误: {str(e)}")
                    try:
                        # 发生错误时尝试重新连接
                        self.connect()
                    except Exception as conn_e:
                        logger.error(f"重新连接失败: {str(conn_e)}")

            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"第 {retry_count} 次重试，等待 {retry_delay} 秒...")
                sleep(retry_delay)
                continue
            else:
                self._reset_connection()
                raise MailOperationError(f"持续搜索失败，已尝试{retry_count}次")

        return []

    def list_folders(self) -> list[str]:
        """获取邮箱中所有可用的文件夹列表并打印"""
        try:
            self._validate_connection()
            result, folder_list = self.server.list()

            if result != "OK":
                raise MailOperationError("获取文件夹列表失败")

            folders = []
            for folder_info in folder_list:
                if not folder_info:
                    continue
                try:
                    folder_name = folder_info.decode()
                    # logger.info(f"找到文件夹: {folder_name}")  # 添加日志
                    folder_name = folder_name.split('"')[-2]
                    folders.append(folder_name)
                except Exception as e:
                    logger.warning(f"解析文件夹名称失败: {str(e)}")
                    continue

            return folders
        except Exception as e:
            logger.error(f"获取文件夹列表失败: {str(e)}")
            return []

    def search_emails(
        self,
        search_criteria: SearchCriteria,
    ) -> list[dict[str, Any]]:
        """搜索邮件"""
        try:
            self._validate_connection()
            self._select_folder(search_criteria.folder)

            # 构建搜索条件
            criteria_parts = []
            if search_criteria.subject:
                criteria_parts.append(f'SUBJECT "{search_criteria.subject}"')
            if search_criteria.from_addr:
                criteria_parts.append(f'FROM "{search_criteria.from_addr}"')
            if search_criteria.since:
                criteria_parts.append(f'SINCE "{search_criteria.since}"')
            if search_criteria.to:
                criteria_parts.append(f'TO "{search_criteria.to}"')

            # 新增已读/未读条件
            if search_criteria.is_read is not None:
                if search_criteria.is_read:
                    criteria_parts.append("SEEN")
                else:
                    criteria_parts.append("UNSEEN")

            criteria_str = f"({' '.join(criteria_parts)})" if criteria_parts else "ALL"

            # 执行搜索
            message_ids = self._execute_search(criteria_str)
            page_message_ids = self._handle_pagination(
                message_ids,
                search_criteria.offset,
                search_criteria.limit,
                search_criteria.sort_order,
            )

            return self._fetch_emails_info(page_message_ids, search_criteria.folder)

        except Exception as e:
            logger.error(f"搜索邮件失败: {str(e)}")
            raise

    def delete_email(
        self, message_id: str | bytes | int, folder: str = "INBOX"
    ) -> bool:
        """删除指定的邮件"""
        try:
            self._validate_connection()
            self._select_folder(folder)

            # 统一消息ID的格式处理
            if isinstance(message_id, str):
                message_id = message_id.encode("utf-8")
            elif isinstance(message_id, int):
                message_id = str(message_id).encode("utf-8")
            elif not isinstance(message_id, bytes):
                raise ValueError(
                    f"不支持的message_id类型: {type(message_id)}"
                )  # noqa: TRY004

            # 标记邮件为删除
            result, _ = self.server.store(message_id, "+FLAGS", r"(\Deleted)")
            if result != "OK":
                raise MailOperationError(f"标记删除邮件失败: {message_id}")

            # 执行删除操作
            result, _ = self.server.expunge()
            if result != "OK":
                raise MailOperationError(f"删除邮件失败: {message_id}")

            logger.success(f"成功删除邮件: {message_id}")
            return True

        except Exception as e:
            logger.error(f"删除邮件失败: {str(e)}")
            raise MailOperationError(f"删除邮件操作失败: {str(e)}") from e

    def delete_emails(
        self, message_ids: list[str | bytes | int], folder: str = "INBOX"
    ) -> dict[str | bytes | int, bool]:
        """批量删除邮件

        Args:
            message_ids: 要删除的邮件ID列表
            folder: 邮件所在文件夹，默认为收件箱

        Returns
        -------
            Dict[Union[str, bytes, int], bool]: 每个邮件ID的删除结果

        Raises
        ------
            MailOperationError: 删除操作失败
            MailConnectionError: 连接错误
        """
        results = {}
        for msg_id in message_ids:
            try:
                success = self.delete_email(msg_id, folder)
                results[msg_id] = success
            except Exception as e:
                logger.error(f"删除邮件 {msg_id} 失败: {str(e)}")
                results[msg_id] = False

        return results
