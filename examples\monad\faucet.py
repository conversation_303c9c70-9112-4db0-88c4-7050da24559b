import asyncio
import json
import os
import random
import re
import string
import time
from time import sleep

import click
import requests
from loguru import logger
from retry import retry
from web3 import Web3

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BrowserType, BROWSER_TYPES
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import parse_indices
from src.utils.thread_executor import ThreadExecutor
from src.utils.tls_client import TLSClient
from src.utils.fake_ua import get_headers

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")

NOCAPTCHA_KEY = os.getenv("NOCAPTCHA_KEY")
NOCAPTCHA_DEV_CODE = os.getenv("NOCAPTCHA_DEV_CODE")
PROXY_URL = os.getenv("PROXY_URL")
MONAD_RPC = os.getenv("MONAD_RPC")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")


def w3_get_balance(address: str, rpc: str = MONAD_RPC, token_contract: str = None) -> float:
    """
    查询 Monad 链上某个地址的余额（原生代币或 ERC-20 代币）

    :param address: 需要查询的地址
    :param rpc: Monad RPC URL
    :param token_contract: 代币合约地址（可选，不传则查询原生代币）
    :return: 余额（十进制格式）
    """
    web3 = Web3(Web3.HTTPProvider(rpc))

    if not web3.is_connected():
        raise ConnectionError("无法连接到 Monad RPC，请检查 RPC URL")

    if token_contract:
        # 查询 ERC-20 代币余额
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        contract = web3.eth.contract(address=web3.to_checksum_address(token_contract), abi=erc20_abi)

        # 获取代币余额
        balance_wei = contract.functions.balanceOf(web3.to_checksum_address(address)).call()
        # 获取代币小数位数
        decimals = contract.functions.decimals().call()
        # 转换成十进制
        balance = balance_wei / (10 ** decimals)
    else:
        # 查询原生代币余额
        balance_wei = web3.eth.get_balance(web3.to_checksum_address(address))
        balance = web3.from_wei(balance_wei, 'ether')
    return float(balance)


def get_kookeey_url():
    random_session = ''.join(random.choices(string.digits, k=8))
    proxy_url = PROXY_URL.replace("*", random_session)
    return proxy_url


def get_proxy():
    for _ in range(10):
        # FIXME: 这里可以获取其他的代理，自行切换
        proxy = get_kookeey_url()
        proxies = {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }
        try:
            ip_check_url = "https://ipinfo.io/json"
            response = requests.get(ip_check_url, proxies=proxies, timeout=10)
            data = response.json()
            logger.info(
                f"获取代理成功: IP: {data.get('ip')}, 国家: {data.get('country')}, 地区: {data.get('region')}")
            break
        except Exception as e:
            logger.error(f"获取代理失败: {str(e)}")
        else:
            raise Exception("所有代理测试失败，终止请求！")
    return proxy


class NocaptchaService:
    def __init__(self, nocaptcha_key, dev_code):
        self.nocaptcha_key = nocaptcha_key
        self.dev_code = dev_code

        # if not self.nocaptcha_key or not self.dev_code:
        #     raise Exception("nocaptcha_key 或 dev_code 为空")

        self.headers = {
            "User-Token": self.nocaptcha_key,
            "Content-Type": "application/json",
            "Developer-Id": self.dev_code
        }

    # 这里采用了本地打码的形式，未采用nocaptcha的api
    # nocaptcha的api http://api.nocaptcha.io/api/wanda/cloudflare/universal
    def get_cloudflare_token(self):
        """获取 Cloudflare token"""
        url = 'http://localhost:3000/cf-clearance-scraper'
        payload = {
            "url": "https://testnet.monad.xyz/",
            "siteKey": "0x4AAAAAAA-3X4Nd7hf3mNGx",
            "mode": "turnstile-min",
        }
        for _ in range(10):
            try:
                response = requests.post(
                    url,
                    headers={"Content-Type": "application/json"},
                    json=payload,
                )
                response.raise_for_status()
                if response.status_code != 200:
                    logger.error(f"Cloudflare 请求失败，状态码: {response.status_code}")
                    continue

                result = response.json()
                return result.get("token", None)
            except requests.exceptions.RequestException as e:
                logger.error(f"Cloudflare 请求异常: {str(e)}")
                continue

        logger.error("Cloudflare token 获取失败，超过最大重试次数！")
        return None

    def get_vercel_data(self, proxy):
        url = 'http://api.nocaptcha.io/api/wanda/vercel/universal'
        payload = {
            "href": "https://testnet.monad.xyz/",
            "proxy": proxy,
            "timeout": 30
        }

        for _ in range(200):
            try:
                response = requests.post(url, json=payload, headers=self.headers, timeout=60)
                if response.status_code != 200:
                    logger.error(f"vercel请求失败, 状态码: {response.status_code}")
                    continue

                response_data = response.json()
                if response_data.get("data", {}):
                    vcrl_token = response_data.get("data", {}).get("_vcrcs", "")
                    if vcrl_token:
                        return response_data

                sleep(1)
            except Exception as e:
                logger.error(f"vercel请求异常: {str(e)}")
                continue

        return None

    def tsl_request(self, url, headers, payload_data, proxy):
        tsl_url = 'http://api.nocaptcha.io/api/wanda/tls/v1'
        payload = {
            "url": url,
            "method": "post",
            "headers": headers,
            "proxy": proxy,
            "json": payload_data,
            "http2": True,
            "show_ad": False,
            "debug": True
        }

        for _ in range(5):
            try:
                tls_response = requests.post(tsl_url, json=payload, headers=self.headers, timeout=60)
                if tls_response.status_code == 200:
                    tls_data = tls_response.json()
                    logger.info('TLS request successfully', tls_data)
                    if tls_data['data']:
                        drip_response = tls_data['data']['response']
                        if drip_response['status'] != 200:
                            drip_message = json.loads(drip_response["text"])['message']
                            logger.info(f'{drip_message}')
                            if drip_message == 'Claimed already, Please try again later.':
                                return 'claimed already'
                    return "success"
            except Exception as e:
                logger.error(f"tls请求失败:", str(e))
                return e


class Faucet(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.nocaptcha = NocaptchaService(NOCAPTCHA_KEY, NOCAPTCHA_DEV_CODE)
        self.tls_client = TLSClient(get_proxy(), {
            'origin': 'https://testnet.monad.xyz',
            'sec-fetch-site': 'cross-site',
        })

    def get_visitor_id(self):
        return ''.join(random.choices(string.hexdigits[:16].lower(), k=32))

    def get_request_verification(self):
        headers = self.get_headers()
        headers["cookie"] = self.generate_cookie()
        headers[
            "accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
        headers["sec-fetch-dest"] = "document"
        headers["sec-fetch-mode"] = "navigate"
        headers["sec-fetch-site"] = "none"
        headers["upgrade-insecure-requests"] = "1"
        headers["sec-fetch-user"] = "?1"
        url = "https://testnet.monad.xyz/"
        try:
            resp = requests.get(url, headers=headers)
            with open("page_content.txt", "w", encoding="utf-8") as f:
                f.write(resp.text)
            if resp.status_code == 200:
                # 修改正则表达式以匹配转义的引号
                pattern = r'\\\"requestVerification\\\":{\\\"token\\\":\\\"([^\\\"]+)\\\",\\\"timestamp\\\":\\\"([^\\\"]+)\\\"}'
                match = re.search(pattern, resp.text)

                if match:
                    token = match.group(1)
                    timestamp = match.group(2)
                    return {
                        'token': token,
                        'timestamp': timestamp
                    }

                logger.error("无法在响应中找到 requestVerification 数据")
                return None
        except Exception as e:
            logger.error(f"获取 requestVerification 失败: {str(e)}")
            return None

    def generate_cookie(self):
        # 生成随机的GA ID
        ga_id = ''.join(random.choices(string.digits, k=10))

        # 获取当前时间戳
        current_time = int(time.time())
        session_count = random.randint(1, 50)  # 随机会话次数

        # 构建cookie各个部分
        cookie_parts = [
            # 基础cookie同意
            'monad-cookie-consent=%7B%22analytics%22%3Atrue%2C%22necessary%22%3Atrue%7D',

            # GA cookies
            f'_ga=GA1.1.{ga_id}.{current_time}',
            f'_ga_R1QQ1Z50QZ=GS1.1.{current_time}.{session_count}.0.{current_time}.0.0.0',

            # wagmi store
            'wagmi.store={"state":{"connections":{"__type":"Map","value":[]},"chainId":10143,"current":null},"version":2}',

            # 另一个GA cookie
            f'_ga_S28YGBT9Y4=GS1.1.{current_time}.{session_count}.1.{current_time + 1000}.0.0.0'
        ]

        return '; '.join(cookie_parts)

    def get_headers(self):
        # # 获取claim请求头
        # headers = {
        #     "accept": "*/*",
        #     "accept-encoding": "gzip, deflate, br, zstd",
        #     "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
        #     "content-type": "application/json",
        #     "origin": "https://testnet.monad.xyz",
        #     "referer": "https://testnet.monad.xyz/",
        #     "sec-ch-ua": '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
        #     "sec-ch-ua-mobile": "?0",
        #     "sec-ch-ua-platform": "macOS",
        #     "sec-fetch-dest": "empty",
        #     "sec-fetch-mode": "cors",
        #     "sec-fetch-site": "cross-site",
        #     "user-agent": 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        # }
        headers = get_headers("https://testnet.monad.xyz", int(self.browser_id))
        headers["sec-fetch-site"] = "cross-site"

        # 添加cookie

        return headers

    def claim(self, address):
        try:
            if not address:
                logger.info(f"🚫【{self.browser_id}】领水失败, address 为空")
                return False

            # 获取cloudflare token
            cf_token = self.nocaptcha.get_cloudflare_token()
            if not cf_token:
                logger.info(f"🚫【{self.browser_id}】{address} 领水失败, cf_token 为空")
                return False

            # request_verification = self.get_request_verification()
            # if not request_verification:
            #     logger.info(f"🚫【{self.browser_id}】{address} 领水失败, requestVerification 为空")
            #     return False
            #
            # request_token = request_verification['token']
            # request_timestamp = request_verification['timestamp']

            # 获取claim请求头
            claim_headers = self.get_headers()
            claim_headers["x-fingerprint"] = self.get_visitor_id()
            # claim_headers["X-Request-Verification-Token"] = request_token

            claim_payload = {
                "address": address,
                "cloudFlareResponseToken": cf_token,
                # "visitorId": self.get_visitor_id(),
            }
            # tls请求体
            # claim_url = "https://faucet-claim.monadinfra.com/"
            claim_url = "https://faucet-claim.molandak.org/"

            # 发起tls请求
            result = self.tls_client.post(
                claim_url,
                json=claim_payload,
                timeout=60,
                acceptable_statuses=[200],
                headers=claim_headers
            )
            message = result.get("message", "")
            if "Success" in message:
                logger.success(f"🎉【{self.browser_id}】{address} 领水成功")
                return True

            logger.error(f"🚫{self.browser_id}】{address} 领水失败: {message}")
            return False
        except Exception as e:
            logger.error(f"🚫【{self.browser_id}】{address} 领水异常")
            return False

    # @retry(tries=1, delay=1)
    def task(self) -> bool:
        evm_address = self.browser_config.evm_address

        # balance_evm = w3_get_balance(evm_address, rpc=ETH_RPC_URL)
        # if balance_evm < 0.01:
        #     logger.info(f"{evm_address} 余额不足0.01 ETH")
        #     return True

        balance_before = w3_get_balance(evm_address)
        retry_count = 5
        for attempt in range(retry_count):
            logger.info(f"【{self.browser_id}】{evm_address} 第 {attempt + 1}/{retry_count} 次尝试领水")
            if self.claim(evm_address):
                logger.success(f"🎉 【{self.browser_id}】领水之前 {evm_address} 余额: {balance_before} MON")
                balance_after = w3_get_balance(evm_address)
                logger.success(f"🎉 【{self.browser_id}】领水之后 {evm_address} 余额: {balance_after} MON")
                return True
            sleep(random.randint(5, 10))

        logger.info(f"【{self.browser_id}】{evm_address} {retry_count}次尝试领水后仍未成功")
        return False


def _faucet_task(type, index):
    try:
        return Faucet(type, index).task()
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _faucet_task(type, index)  # 确保返回布尔值表示成功或失败

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Monad_API-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)

        # 统计结果
        total_accounts = len(indices)
        successful_claims = sum(1 for result in results if result)
        failed_claims = total_accounts - successful_claims
        success_rate = (successful_claims / total_accounts) * 100 if total_accounts > 0 else 0

        logger.success(
            f"本次共领水 {total_accounts} 个账号, 成功 {successful_claims} 个, 失败 {failed_claims} 个, 成功率 {success_rate:.2f}%")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    cli()
    # _faucet_task(BrowserType.MORE, "1")

    # result = asyncio.run(nocaptcha.get_cloudflare_token())
    # print(f"Cloudflare Token Result: {result}")
    # print(get_kookeey_proxy())
