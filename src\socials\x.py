import json
import os
import random
import re
from time import sleep

import pyotp
from loguru import logger
from retry import retry

from src.browsers.operations import try_click
from src.emails.imap4.email_client import EmailClient, SearchCriteria
from src.utils import get_element
from src.utils.hhmail import HHMail, HHMailException
from src.utils.yescaptcha import YesCaptcha


class XEmailVerifier:
    def __init__(self, page, id):
        self.page = page
        self.id = id

    def get_verification_code(self, x_email, x_email_pwd, proxy_email=None):
        """统一的验证码获取入口"""
        if not x_email or not x_email_pwd:
            logger.error(f"{self.id} 邮箱或邮箱密码为空")
            return None

        # 按照成功率从高到低排序方法
        methods = [
            self._get_email_code_2,  # 优先使用最新的未读邮件方法
            self._get_email_code_last_mail,  # 其次使用最新邮件方法
            self._get_email_code,  # 最后使用旧方法作为备选
        ]

        for method in methods:
            try:
                code = method(x_email=x_email, x_email_pwd=x_email_pwd)
                if code:
                    logger.success(f"{self.id} 成功获取验证码: {code}")
                    return code
            except Exception as e:
                logger.warning(f"{self.id} 方法 {method.__name__} 获取验证码失败: {e}")
                continue

        logger.error(f"{self.id} 所有方法均未获取到验证码")
        return None

    def enter_verification_code(self, code):
        """统一的验证码输入入口"""
        methods = [self._enter_verification_code, self._enter_verification_code_2]

        for method in methods:
            try:
                if method(code):
                    return True
            except Exception as e:
                logger.warning(f"{self.id} 验证码输入方法失败: {e}")
                continue

        logger.error(f"{self.id} 所有验证码输入方法均失败")
        return False

    def verify_email(self, x_email, x_email_pwd):
        if not x_email or not x_email_pwd:
            logger.error(f"{self.id} 邮箱或邮箱密码为空")
            return False

        code = self.get_verification_code(x_email, x_email_pwd)
        if not code:
            return False

        return self._enter_verification_code(code)

    def _get_email_code(self, x_email, x_email_pwd, max_retries=3, delay=3):
        for _ in range(max_retries):
            sleep(delay)
            try:
                with HHMail(x_email, x_email_pwd).connect() as mail:
                    subject = "Your X confirmation code"
                    search_criteria = f'SUBJECT "{subject}" FROM "<EMAIL>"'
                    messages = mail.search(search_criteria)

                    if not messages:
                        logger.info(f"未找到未读的主题包含 '{subject}' 的邮件")
                        continue

                    # 获取最新的匹配邮件
                    latest_message = messages[-1]
                    mail_content = mail.get_mail_content(latest_message)

                    # 提取完整的主题
                    full_subject = mail_content.get("subject", "")

                    if full_subject:
                        logger.success(f"成功获取未读确认邮件的完整主题: {full_subject}")

                        # 使用正则表达式提取验证码
                        pattern = r"Your X confirmation code is (\w+)"
                        match = re.search(pattern, full_subject)

                        if match:
                            confirmation_code = match.group(1)
                            logger.success(f"成功提取验证码: {confirmation_code}")
                            return confirmation_code
                        else:
                            logger.warning("未能从主题中提取验证码")
                            continue
                    else:
                        logger.warning("邮件内容中没有主题")
                        continue
            except HHMailException as e:
                logger.error(f"搜索确认邮件时发生错误: {e}")
                continue
            except Exception as e:
                logger.error(f"搜索确认邮件时发生未预期的错误: {e}")
                continue
        else:
            return None

    def _get_email_code_last_mail(self, x_email, x_email_pwd):
        try:
            with HHMail(x_email, x_email_pwd).connect() as mail:
                latest_email = mail.get_latest_email()
                if not latest_email:
                    logger.warning(f"{self.id} 未找到最新邮件")
                    return None

                content = latest_email.get("content", "")
                timestamp = latest_email.get("date", "Unknown date")
                if not content:
                    logger.warning(f"{self.id} 最新邮件内容为空")
                    return None

                # 尝试匹配两种可能的验证码格式
                patterns = [
                    r"verification code to continue using X:\s*(\d+)",
                    r"Please enter this verification code on X when prompted\s*(\d{6})",
                ]

                for pattern in patterns:
                    match = re.search(pattern, content)
                    if match:
                        code = match.group(1)
                        logger.success(f"{self.id} 成功从邮件内容中提取验证码: {code}, 邮件时间: {timestamp}")
                        return code

                logger.warning(f"{self.id} 未能从邮件内容中提取验证码")
                return None

        except HHMailException as e:
            logger.error(f"{self.id} 搜索确认邮件时发生错误: {e}")
        except Exception as e:
            logger.error(f"{self.id} 搜索确认邮件时发生未预期的错误: {e}")

        return None

    def _get_email_code_2(self, x_email, x_email_pwd, max_retries=3, delay=3):
        for attempt in range(max_retries):
            sleep(delay)
            try:
                with HHMail(x_email, x_email_pwd).connect() as mail:
                    subject = "is your X verification code"
                    search_criteria = f'(UNSEEN SUBJECT "{subject}" FROM "<EMAIL>")'
                    messages = mail.search(search_criteria)

                    if not messages:
                        logger.warning(f"尝试 {attempt + 1}/{max_retries}: 未找到未读的主题包含 '{subject}' 的邮件")
                        continue

                    latest_message = messages[-1]
                    mail_content = mail.get_mail_content(latest_message)
                    if not mail_content:
                        logger.warning(f"尝试 {attempt + 1}/{max_retries}: 未能获取邮件内容，请检查邮箱配置")
                        continue

                    subject = mail_content["subject"]
                    code_match = re.search(r"^(\d{6})", subject)
                    if code_match:
                        logger.success(f"成功获取验证码: {code_match.group(1)}")
                        return code_match.group(1)
                    else:
                        logger.warning(f"尝试 {attempt + 1}/{max_retries}: 未在邮件主题中找到验证码，请确认验证码格式")
                        continue
            except HHMailException as e:
                logger.warning(f"尝试 {attempt + 1}/{max_retries}: 搜索确认邮件时发生错误: {e}")
            except Exception as e:
                logger.warning(f"尝试 {attempt + 1}/{max_retries}: 搜索确认邮件时发生未预期的错误: {e}")

        logger.error(f"{self.id} 在 {max_retries} 次尝试后未能获取验证码")
        return None

    @retry(tries=3, delay=3)
    def _enter_verification_code(self, code):
        try:
            input_field = self.page.latest_tab.ele("@data-testid=ocfEnterTextTextInput", timeout=10)
            input_field.input(code)
            sleep(1)
            next_button = self.page.latest_tab.ele("@data-testid=ocfEnterTextNextButton", timeout=10)
            next_button.click()
            sleep(8)
            return True
        except Exception as e:
            logger.error(f"验证过程中出错: {e}")
            raise

    def _enter_verification_code_2(self, code):
        try:
            token_ele = self.page.latest_tab.ele("@name=token", timeout=10)
            if not token_ele:
                logger.error(f"{self.id} 输入框未找到...")
                return False

            token_ele.input(code)
            submit_ele = self.page.latest_tab.ele("@type=submit", timeout=10)
            if not submit_ele:
                logger.error(f"{self.id} 未找到验证按钮...")
                return False

            if not submit_ele.states.is_clickable:
                logger.error(f"{self.id} 验证按钮不可点击...")
                return False

            submit_ele.click()
            sleep(8)
            return True
        except Exception as e:
            logger.error(f"验证过程中出错: {e}")
            return False


class X:
    def __init__(self, id, page) -> None:
        self.id = id
        self.page = page
        self.email_verifier = XEmailVerifier(page, id)

    def _get_auth_email_code(self, x_email, x_email_pwd, proxy_email):
        email_client = EmailClient(x_email, x_email_pwd)
        search_criteria = SearchCriteria(
            subject="Your X confirmation code is",
            # from_addr="<EMAIL>",
            to=proxy_email or x_email,
            is_read=False,
        )

        try:
            # 尝试获取未读邮件
            emails = email_client.search_emails_with_retry(search_criteria)
        except Exception as e:
            logger.error(f"{self.id} 获取未读邮件时发生错误: {e}")
            emails = []

        if not emails:
            last_email = email_client.get_latest_email()
            if not last_email:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None
            emails.append(last_email)

        email = emails[0]
        pattern = r"Your X confirmation code is (\w+)"
        match = re.search(pattern, email["subject"])

        if match:
            confirmation_code = match.group(1)
            logger.success(f"成功提取验证码: {confirmation_code}")
            return confirmation_code
        else:
            logger.warning("未能从主题中提取验证码")
            return None

    def _get_access_email_code(self, x_email, x_email_pwd, proxy_email):
        email_client = EmailClient(x_email, x_email_pwd)
        search_criteria = SearchCriteria(
            subject="confirm your email address",
            # from_addr="x.com",
            to=proxy_email or x_email,
            # is_read=False,
        )

        try:
            # 尝试获取未读邮件
            emails = email_client.search_emails_with_retry(search_criteria)
        except Exception as e:
            logger.error(f"{self.id} 获取未读邮件时发生错误: {e}")
            emails = []

        if not emails:
            last_email = email_client.get_latest_email()
            if not last_email:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None
            emails.append(last_email)

        email = emails[0]
        pattern = r"verification code to continue using X:\s*(\d+)"
        match = re.search(pattern, email["content"])

        if match:
            confirmation_code = match.group(1)
            logger.success(f"成功提取验证码: {confirmation_code}")
            return confirmation_code
        else:
            logger.warning("未能从主题中提取验证码")
            return None

    def login_with_auth(self, username, passwd, two_fa, x_email, x_email_pwd, proxy_email):
        latest_tab = self.page.latest_tab

        # 输入用户名
        sleep(3)
        ele = latest_tab.ele("@autocomplete=username", timeout=10)
        ele.clear(True)
        ele.input(username)

        # 点击下一步
        next_button = latest_tab.ele("x://button[2]", timeout=6)
        next_button.click()

        # 输入密码
        ele = latest_tab.ele("@autocomplete=current-password")
        ele.clear(True)
        ele.input(passwd)

        # 点击登录按钮
        login_button = latest_tab.ele("x://button[@data-testid='LoginForm_Login_Button']")
        login_button.click()

        # 需要输入邮箱验证码
        check_email = latest_tab.ele("x://span[contains(text(), '@') and contains(text(), '.***')]", timeout=5)
        if check_email:
            code = self._get_auth_email_code(x_email, x_email_pwd, proxy_email)
            if not code:
                return False
            latest_tab.ele("@data-testid=ocfEnterTextTextInput").input(code)
            latest_tab.wait.ele_displayed("x://button[@data-testid='ocfEnterTextNextButton']", timeout=5)
            latest_tab.ele("@data-testid=ocfEnterTextNextButton").click()
            sleep(8)
            return True

        if self._handle_two_factor_auth(two_fa):
            return False

        return "oauth2/authorize" in latest_tab.url

    def follow(self, username):
        """
        关注用户.
        """
        for attempt in range(3):
            latest_tab = None
            try:
                # 如果不是第一次尝试，添加等待时间
                if attempt > 0:
                    sleep(2)
                    logger.info(f"{self.id} 尝试第{attempt + 1}次关注{username}")

                # 打开用户页面
                latest_tab = self.page.new_tab()
                latest_tab.listen.start("/UserByScreenName?")
                latest_tab.get(f"https://x.com/{username}")

                res = latest_tab.listen.wait(timeout=30)
                if not res:
                    logger.warning(f"{self.id} 用户不存在或者网络异常")
                    continue

                if res.response.status == 200:
                    response_data = res.response.body
                    following = (
                        response_data.get("data", {})
                        .get("user", {})
                        .get("result", {})
                        .get("legacy", {})
                        .get("following", False)
                    )
                    if following:
                        logger.success(f"{self.id} 已经关注{username}")
                        return True

                # 点击关注按钮
                ele = get_element(
                    latest_tab,
                    "x://div[@data-testid='placementTracking']//button[.='Follow']",
                )
                if ele:
                    latest_tab.listen.start("https://x.com/i/api/1.1/friendships/create.json")
                    ele.click()
                    # 监听关注请求
                    res = latest_tab.listen.wait(timeout=30)
                    if not res:
                        logger.warning(f"{self.id} 关注请求未能成功发送")
                        continue  # 继续下一次尝试

                    response_data = res.response.body
                    if res.response.status == 200 and response_data.get("id"):
                        logger.success(f"{self.id} 关注{username}成功")
                        return True

                logger.warning(f"{self.id} 第{attempt + 1}次关注{username}失败")

            except Exception as e:
                logger.warning(f"{self.id} 第{attempt + 1}次关注用户{username}失败: {e}")
            finally:
                if latest_tab:
                    latest_tab.close()

        logger.error(f"{self.id} 经过多次尝试，关注{username}最终失败")
        return False

    def batch_follow(self, usernames: list[str]) -> dict[str, bool]:
        tab = None
        result = {}
        try:
            tab = self.page.new_tab("https://x.com/explore")
            sleep(2)
            search_selector = 'x://input[@data-testid="SearchBox_Search_Input"]'
            random.shuffle(usernames)
            for name in usernames:
                try:
                    logger.info(f"【{self.id}】搜索用户 {name}")
                    # 定位搜索框
                    search_box = tab.ele(search_selector, timeout=20)
                    if not search_box:
                        logger.error(f"【{self.id}】搜索框未加载")
                        result[name] = False
                        continue
                    # 输入用户名并搜索
                    search_box.clear()
                    tab.actions.move_to(search_box).click().type(name)
                    sleep(0.5)
                    tab.actions.key_down("ENTER")
                    sleep(1)

                    user_btn = get_element(tab, f'x://button[//a//span[text()="{name}"]]', 5)
                    if not user_btn:
                        result[name] = False
                        logger.error(f"【{self.id}】未搜索到 {name}, 请检查推特用户名是否正确")
                        continue

                    # 先判断是否已经是 Following
                    unfollow_btn = get_element(
                        tab,
                        f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                        ' and contains(@data-testid, "-unfollow")]',
                        5,
                    )
                    if unfollow_btn:
                        logger.info(f"【{self.id}】已经关注 {name} ,跳过")
                        result[name] = True
                        continue

                    follow_btn = get_element(
                        tab,
                        f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                        ' and contains(@data-testid, "-follow")]',
                        5,
                    )
                    if follow_btn:
                        follow_btn.click()
                        logger.info(f"【{self.id}】开始关注 {name}")
                        sleep(2)

                    unfollow_btn = get_element(
                        tab,
                        f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                        ' and contains(@data-testid, "-unfollow")]',
                        5,
                    )
                    if unfollow_btn:
                        logger.success(f"【{self.id}】关注 {name} 成功")
                        result[name] = True
                        wait_time = random.uniform(5, 10)
                        logger.info(f"【{self.id}】等待 {wait_time:.1f} 秒后继续")
                        sleep(wait_time)
                    else:
                        result[name] = False
                        logger.error(f"【{self.id}】关注 {name} 失败")
                except Exception as e:
                    logger.error(f"【{self.id}】关注 {name} 发生异常, error={str(e)}")
                    result[name] = False
            return result
        except Exception as e:
            logger.error(f"【{self.id}】批量关注异常, error={str(e)}")
            return result
        finally:
            if tab:
                tab.close()

    def batch_follow_for_mac(self, usernames: list[str]) -> dict[str, bool]:
        result = {}
        search_selector = 'x://input[@data-testid="SearchBox_Search_Input"]'
        random.shuffle(usernames)
        for name in usernames:
            try:
                tab = self.page.new_tab("https://x.com/explore")
                sleep(2)
                logger.info(f"【{self.id}】搜索用户 {name}")
                # 定位搜索框
                search_box = tab.ele(search_selector, timeout=5)
                if not search_box:
                    logger.error(f"【{self.id}】搜索框未加载")
                    result[name] = False
                    tab.close()
                    continue
                # 输入用户名并搜索
                search_box.clear(True)
                tab.actions.move_to(search_box).click().type(name)
                sleep(0.5)
                tab.actions.key_down("ENTER")
                sleep(1)

                user_btn = get_element(tab, f'x://button[//a//span[text()="{name}"]]', 5)
                if not user_btn:
                    result[name] = False
                    logger.error(f"【{self.id}】未搜索到 {name}, 请检查推特用户名是否正确")
                    tab.close()
                    continue

                # 先判断是否已经是 Following
                unfollow_btn = get_element(
                    tab,
                    f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                    ' and contains(@data-testid, "-unfollow")]',
                    5,
                )
                if unfollow_btn:
                    logger.info(f"【{self.id}】已经关注 {name} ,跳过")
                    result[name] = True
                    tab.close()
                    continue

                follow_btn = get_element(
                    tab,
                    f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                    ' and contains(@data-testid, "-follow")]',
                    5,
                )
                if follow_btn:
                    follow_btn.click()
                    logger.info(f"【{self.id}】开始关注 {name}")
                    sleep(2)

                unfollow_btn = get_element(
                    tab,
                    f'x://button[//a//span[text()="{name}"]][@data-testid="UserCell"]//button[contains(@aria-label,"{name}")'
                    ' and contains(@data-testid, "-unfollow")]',
                    5,
                )
                if unfollow_btn:
                    logger.success(f"【{self.id}】关注 {name} 成功")
                    result[name] = True
                    wait_time = random.uniform(5, 10)
                    logger.info(f"【{self.id}】等待 {wait_time:.1f} 秒后继续")
                    tab.close()
                    sleep(wait_time)
                else:
                    result[name] = False
                    logger.error(f"【{self.id}】关注 {name} 失败")
                    tab.close()
            except Exception as e:
                logger.error(f"【{self.id}】关注 {name} 发生异常, error={str(e)}")
                result[name] = False
        return result

    def _check_tweet_interacted(self, response_data, check_field=None, config=None):
        """
        检查推文是否已经进行过交互
        """
        tweet_data = (
            response_data.get("data", {})
            .get("threaded_conversation_with_injections_v2", {})
            .get("instructions", [{}])[0]
        )
        entries = tweet_data.get("entries", [])

        if entries and len(entries) > 0:
            tweet_result = (
                entries[0].get("content", {}).get("itemContent", {}).get("tweet_results", {}).get("result", {})
            )
            already_interacted = tweet_result.get("legacy", {}).get(check_field, False)

            if already_interacted:
                logger.success(f"{self.id} {config['already_msg']}")
                return True

        return False

    def _cancel_interaction(self, latest_tab, interaction_type):
        """
        取消交互
        """
        if interaction_type == "like":
            # unlike_ele = latest_tab.ele("x://button[@data-testid='unlike']", timeout=5)
            # if unlike_ele:
            latest_tab.listen.start("https://x.com/i/api/graphql/ZYKSe-w7KEslx3JhSIk5LA/UnfavoriteTweet")
            try_click(
                latest_tab,
                "x://button[@data-testid='unlike']",
                timeout=5,
                max_attempts=10,
            )
            # unlike_ele.click()
            sleep(1)
            res = latest_tab.listen.wait(timeout=30)
            if not res:
                logger.warning(f"{self.id} 取消点赞失败")
                return False
            if res.response.status == 200:
                logger.success(f"{self.id} 取消点赞成功")
                return True

            logger.warning(f"{self.id} 取消点赞失败")
            return False
        elif interaction_type == "retweet":
            # unretweet_ele = latest_tab.ele("x://button[@data-testid='unretweet']", timeout=5)
            # if unretweet_ele:
            latest_tab.listen.start("https://x.com/i/api/graphql/iQtK4dl5hBmXewYZuEOKVw/DeleteRetweet")
            try_click(
                latest_tab,
                "x://button[@data-testid='unretweet']",
                timeout=5,
                max_attempts=10,
            )

            # unretweet_ele.click()
            confirm_ele = latest_tab.ele("x://div[@data-testid='unretweetConfirm']", timeout=5)
            if confirm_ele:
                confirm_ele.click()

            sleep(1)
            res = latest_tab.listen.wait(timeout=30)
            if not res:
                logger.warning(f"{self.id} 取消转发失败")
                return False

    def _interact_with_tweet(self, url, interaction_type, check_field=None, force=False):
        """
        与推文交互的通用方法（点赞、转发等）

        参数:
            url: 推文URL
            interaction_type: 交互类型，如 'like' 或 'retweet'
            check_field: 检查是否已交互的字段名，如 'favorited' 或 'retweeted'
            force: 是否强制交互, 如果已经交互过，先取消再交互
        """
        interaction_config = {
            "like": {
                "button_testid": "like",
                "confirm_testid": None,
                "api_endpoint": "https://x.com/i/api/graphql/lI07N6Otwv1PhnEgXILM7A/FavoriteTweet",
                "check_field": "favorited",
                "success_msg": "点赞推文成功",
                "already_msg": "已经点赞过该推文",
            },
            "retweet": {
                "button_testid": "retweet",
                "confirm_testid": "retweetConfirm",
                "api_endpoint": "https://x.com/i/api/graphql/ojPdsZsimiJrUGLR1sjUtA/CreateRetweet",
                "check_field": "retweeted",
                "success_msg": "转发推文成功",
                "already_msg": "已经转发过该推文",
            },
        }

        config = interaction_config[interaction_type]
        check_field = check_field or config["check_field"]

        for attempt in range(3):
            latest_tab = None
            try:
                # 如果不是第一次尝试，添加等待时间
                if attempt > 0:
                    sleep(2)
                    logger.info(f"{self.id} 尝试第{attempt + 1}次{config['success_msg'].replace('成功', '')}{url}")

                # 打开推文页面
                latest_tab = self.page.new_tab()
                latest_tab.listen.start("/TweetDetail?")
                latest_tab.get(url)

                res = latest_tab.listen.wait(timeout=30)
                if not res:
                    logger.warning(f"{self.id} 推文不存在或者网络异常")
                    continue

                # 检查推文是否已经进行过交互
                if res.response.status == 200:
                    result = self._check_tweet_interacted(res.response.body, check_field=check_field, config=config)
                    if result and not force:
                        logger.success(f"{self.id} {config['already_msg']}")
                        return True

                    # 如果需要强制交互，先取消交互
                    if force and result:
                        result = self._cancel_interaction(latest_tab, interaction_type)
                        if not result:
                            logger.warning(f"{self.id} 取消交互失败")
                            continue

                # 点击交互按钮
                button = get_element(
                    latest_tab,
                    f"x:(//article[@data-testid='tweet'])[1]//button[@data-testid='{config['button_testid']}']",
                )
                if button:
                    # 先设置监听
                    latest_tab.listen.start(config["api_endpoint"])

                    # 如果有弹窗，先点击取消
                    confirmation_sheet_dialog = latest_tab.ele(
                        'x://div[@data-testid="confirmationSheetDialog"]', timeout=5
                    )
                    if confirmation_sheet_dialog:
                        confirmation_sheet_cancel = latest_tab.ele(
                            'x://button[@data-testid="confirmationSheetCancel"]', timeout=5
                        )
                        if confirmation_sheet_cancel:
                            confirmation_sheet_cancel.click()
                            sleep(1)

                    # 然后点击按钮
                    button.click()
                    sleep(1)

                    # 如果需要确认，点击确认按钮
                    if config["confirm_testid"]:
                        confirm_button = get_element(
                            latest_tab,
                            f"x://div[@data-testid='{config['confirm_testid']}']",
                        )
                        if confirm_button:
                            confirm_button.click()

                    # 监听请求
                    res = latest_tab.listen.wait(timeout=30)
                    if not res:
                        logger.warning(f"{self.id} {interaction_type}请求未能成功发送")
                        continue

                    if res.response.status == 200:
                        # 检查响应中是否包含成功标识
                        response_body = res.response.body
                        if "errors" not in response_body:
                            logger.success(f"{self.id} {config['success_msg']}")
                            return True

                logger.warning(f"{self.id} 第{attempt + 1}次{config['success_msg'].replace('成功', '')}失败")

            except Exception as e:
                logger.warning(f"{self.id} 第{attempt + 1}次{config['success_msg'].replace('成功', '')}失败: {e}")
            finally:
                if latest_tab:
                    latest_tab.close()

        logger.error(f"{self.id} 经过多次尝试，{config['success_msg'].replace('成功', '')}{url}最终失败")
        return False

    def favorite(self, url, force=False):
        """
        点赞推文
        """
        return self._interact_with_tweet(url, "like", force=force)

    def retweet(self, url, force=False):
        """
        转发推文
        """
        return self._interact_with_tweet(url, "retweet", force=force)

    def favorite_and_retweet(self, url, force=False):
        """
        点赞并转发推文
        """
        # 先点赞
        like_result = self.favorite(url, force=force)
        # 再转发
        retweet_result = self.retweet(url, force=force)

        # 两个操作都成功才返回True
        return like_result and retweet_result

    def login_by_user(self, username, passwd, two_fa, x_email, x_email_pwd, proxy_email):
        latest_tab = None
        try:
            latest_tab = self.page.new_tab("https://x.com/i/flow/login")

            # 输入用户名
            sleep(3)
            ele = latest_tab.ele("@autocomplete=username", timeout=10)
            ele.clear(True)
            ele.input(username)

            # 点击下一步
            next_button = latest_tab.ele("x://button[2]", timeout=6)
            next_button.click()

            # 输入密码
            ele = latest_tab.ele("@autocomplete=current-password")
            ele.clear(True)
            ele.input(passwd)

            # 点击登录按钮
            login_button = latest_tab.ele("x://button[@data-testid='LoginForm_Login_Button']")
            login_button.click()

            # 需要输入邮箱验证码
            check_email = latest_tab.ele("Check your email", timeout=5)
            if check_email:
                self._first_verify_email(x_email, x_email_pwd, proxy_email)
                sleep(8)
                return {"is_login": "x.com/home" in latest_tab.url, "is_lock": False}

            # 需要输入邮箱验证码
            verify_email_ele = latest_tab.ele("We sent your verification code", timeout=3)
            if verify_email_ele:
                code = self.email_verifier.get_verification_code(x_email, x_email_pwd, proxy_email)
                if not code:
                    return {"is_login": False, "is_lock": False}

                if not self.email_verifier.enter_verification_code(code):
                    return {"is_login": False, "is_lock": False}

            email_ele = latest_tab.ele("x://input[@inputmode='email']", timeout=3)
            if email_ele:
                email_ele.clear(True)
                _email = proxy_email or x_email
                email_ele.input(_email)
                sleep(1)

                # 点击下一步
                next_button = latest_tab.ele("x://button[@data-testid='ocfEnterTextNextButton']")
                next_button.click()
                sleep(8)

            if self._handle_two_factor_auth(two_fa):
                return {"is_login": False, "is_lock": False}

            return {"is_login": "x.com/home" in latest_tab.url, "is_lock": False}
        except Exception as e:
            logger.error(f"{self.id} 使用用户名密码登录失败: {e}")
            return {"is_login": False, "is_lock": False}
        finally:
            if latest_tab:
                latest_tab.close()

    def login_by_token(self, token):
        latest_tab = None
        try:
            latest_tab = self.page.new_tab("https://x.com/i/flow/login")
            if not token:
                logger.warning(f"{self.id} token参数传入为空")
                return False

            # token login
            script = self._generate_script(token)
            latest_tab.run_js(script, as_expr=True)

            sleep(3)
            _token = self.get_token()
            if token == _token:
                logger.success(f"{self.id} 使用token登录成功")
                return True

            logger.warning(f"{self.id} 使用token登录失败")
            return False
        except Exception as e:
            logger.error(f"{self.id} 使用token登录失败: {e}")
            return False
        finally:
            if latest_tab:
                latest_tab.close()

    def check_login_by_follow(self):
        last_tab = None
        try:
            last_tab = self.page.new_tab("https://x.com/i/connect_people?is_creator_only=false")
            sleep(5)

            if "/i/connect_people" not in last_tab.url:
                logger.error(f"{self.id} url 跳转失败...")
                return {"success": False, "message": "页面跳转异常..."}

            eles = last_tab.eles('x://button[contains(@data-testid, "-follow")]')
            if not eles:
                logger.warning(f"{self.id} 未找到关注按钮")
                return {"success": False, "message": "为找到关注按钮..."}

            last_tab.listen.start("https://x.com/i/api/1.1/friendships/create.json")

            # 随机选取两个关注按钮进行点击
            success_count = 0
            selected_eles = random.sample(eles, 2)
            for ele in selected_eles:
                if ele.states.is_clickable:
                    ele.click()
                    res = last_tab.listen.wait(timeout=30)
                    if not res:
                        logger.warning(f"{self.id} 关注请求未能成功发送")
                        return False

                    if res.response.status == 200:
                        success_count += 1
                    elif res.response.status == 403:
                        try:
                            message = res.response.body.get("errors", [])[0].get("message")
                            return {"success": False, "message": message}
                        except:
                            pass

                    sleep(random.randint(1, 3))

            return {"success": success_count == 2}

        except Exception as e:
            logger.error(f"{self.id} 执行关注任务异常: {e}")
            return {"success": False, "message": "执行关注任务异常..."}
        finally:
            if last_tab:
                last_tab.close()

    def _find_image_file(self, directory, image_type="image"):
        """
        在指定目录中查找对应ID的图片文件.

        Args:
            directory: 图片所在目录
            image_type: 图片类型描述(用于日志)

        Returns
        -------
            str|None: 找到的图片文件路径,未找到则返回None
        """
        possible_extensions = ["png", "jpg", "jpeg"]
        image_file = None

        for ext in possible_extensions:
            file_path = os.path.join(directory, f"{self.id}.{ext}")
            if os.path.exists(file_path):
                image_file = file_path
                break

        if not image_file:
            logger.warning(f"{self.id} 在目录 {directory} 中未找到对应的{image_type}文件")

        return image_file

    def _change_avatar(self, last_tab, avatar_path) -> bool:
        try:
            if not avatar_path:
                logger.warning(f"{self.id} 头像目录未指定")
                # 跳过头像选择
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfSelectAvatarSkipForNowButton']",
                )
                if ele:
                    ele.click()
                return True

            avatar_file = self._find_image_file(avatar_path, "头像")
            if not avatar_file:
                logger.warning(f"{self.id} 在目录 {avatar_path} 中未找到对应的头像文件")
                # 跳过头像选择
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfSelectAvatarSkipForNowButton']",
                )
                if ele:
                    ele.click()
                return True

            # 上传头像
            file = last_tab("tag:input@type=file")
            file.input(avatar_file)
            sleep(2)

            get_element(last_tab, "x://button[@data-testid='applyButton']").click()
            last_tab.wait.ele_displayed("x://button[@data-testid='ocfSelectAvatarNextButton']", timeout=5)
            sleep(1)

            get_element(last_tab, "x://button[@data-testid='ocfSelectAvatarNextButton']").click()
            logger.success(f"{self.id} 头像设置成功")
            return True

        except Exception as e:
            logger.error(f"{self.id} 头像上传失败: {str(e)}")
            return False

    def _set_header_image(self, last_tab, header_image_path):
        try:
            if not header_image_path:
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfSelectBannerSkipForNowButton']",
                )
                if ele:
                    ele.click()
                return True

            header_file = self._find_image_file(header_image_path, "头图")
            if not header_file:
                logger.warning(f"{self.id} 在目录 {header_image_path} 中未找到对应的头图文件")
                # 跳过头图选择
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfSelectBannerSkipForNowButton']",
                )
                if ele:
                    ele.click()
                return True

            file = last_tab("tag:input@type=file")
            file.input(header_file)
            sleep(2)

            get_element(last_tab, "x://button[@data-testid='applyButton']").click()
            last_tab.wait.ele_displayed("x://button[@data-testid='ocfSelectBannerNextButton']", timeout=5)
            sleep(1)

            get_element(last_tab, "x://button[@data-testid='ocfSelectBannerNextButton']").click()
            logger.success(f"{self.id} 头图设置成功")
            return True
        except Exception as e:
            logger.error(f"{self.id} 设置头图失败: {str(e)}")
            return False

    def _change_nickname(self, last_tab, nickname):
        ele = last_tab.ele("x://input[@name='displayName']")
        if ele:
            ele.clear(True)
            ele.input(nickname)

    def _set_bio(self, last_tab, bio):
        try:
            if not bio:
                logger.warning(f"{self.id} bio为空,跳过设置bio")
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfEnterTextSkipForNowButton']",
                    5,
                ) or get_element(last_tab, "x://button[@data-testid='ocfEnterTextNextButton']", 5)
                if ele:
                    ele.click()
                return

            ele = get_element(last_tab, "x://textarea[@data-testid='ocfEnterTextTextInput']", 5)
            ele.clear(True)
            ele.input(bio)
            last_tab.wait.ele_displayed("x://button[@data-testid='ocfEnterTextNextButton']", timeout=5)
            sleep(1)

            next_button = get_element(last_tab, "x://button[@data-testid='ocfEnterTextNextButton']", 5)
            if next_button:
                next_button.click()
        except Exception as e:
            logger.error(f"{self.id} 跳过设置bio失败: {e}")

    def _set_location(self, last_tab, location):
        try:
            if not location:
                logger.warning(f"{self.id} 位置为空,跳过设置位置")
                ele = get_element(
                    last_tab,
                    "x://button[@data-testid='ocfEnterTextSkipForNowButton']",
                    5,
                ) or get_element(last_tab, "x://button[@data-testid='ocfEnterTextNextButton']", 5)
                if ele:
                    ele.click()
                return

            input_ele = get_element(last_tab, "x://input[@data-testid='ocfEnterTextTextInput']", 5)

            input_ele.clear(True)
            input_ele.input(location)
            last_tab.wait.ele_displayed("x://button[@data-testid='ocfEnterTextNextButton']", timeout=5)
            sleep(1)

            next_button = get_element(last_tab, "x://button[@data-testid='ocfEnterTextNextButton']", 5)
            next_button.click()
        except Exception as e:
            logger.error(f"{self.id} 设置位置失败: {e}")

    def _save_profile(self, last_tab):
        # 点击完成按钮
        ele = get_element(last_tab, "x://button[@data-testid='OCF_CallToAction_Button']", 5)
        if ele:
            ele.click()
        sleep(2)
        if "https://x.com/home" in last_tab.url:
            logger.success(f"{self.id} 个人资料保存成功")
            return True
        else:
            return False

    def setup_profile(self, avatar_path, header_image_path, bio, location):
        try:
            last_tab = self.page.new_tab("https://x.com/i/flow/setup_profile")
            sleep(3)

            # 设置头像
            self._change_avatar(last_tab, avatar_path)

            # 设置头图
            self._set_header_image(last_tab, header_image_path)

            # 跳过设置bio
            self._set_bio(last_tab, bio)

            # 设置位置
            self._set_location(last_tab, location)

            # 保存设置
            return self._save_profile(last_tab)
        except Exception as e:
            logger.error(f"{self.id} 个人资料设置失败，error={str(e)}")
            return True

    # 获取用户信息 (粉丝数、关注数、昵称、名字、创建时间、生日)
    def get_user_info(self):
        last_tab = self.page.new_tab("https://x.com/home")
        sleep(3)
        if "flow/login" in last_tab.url or "account/access" in last_tab.url:
            return {"success": False, "message": "账号未登录"}

        last_tab.listen.start("/UserByScreenName?")
        last_tab.ele("@data-testid=AppTabBar_Profile_Link").click()
        res = last_tab.listen.wait(timeout=30)

        if not res:
            logger.warning(f"{self.id} 未能获取到用户数据响应")
            return {"success": False, "message": "未能获取到用户数据响应"}

        try:
            body = json.loads(res.response.raw_body)
            result_data = body.get("data", {}).get("user", {}).get("result", {})
            legacy_data = result_data.get("legacy", {})
            birthday_data = result_data.get("legacy_extended_profile", {}).get("birthdate", {})

            if legacy_data:
                # 解析时间字符串 (格式如: "Fri Aug 16 08:21:03 +0000 2024")
                from datetime import datetime

                created_at = datetime.strptime(legacy_data.get("created_at"), "%a %b %d %H:%M:%S %z %Y")
                formatted_date = created_at.strftime("%Y/%m/%d")

                user_info = {
                    "success": True,
                    "friends_count": legacy_data.get("friends_count"),
                    "followers_count": legacy_data.get("followers_count"),
                    "screen_name": legacy_data.get("screen_name"),
                    "name": legacy_data.get("name"),
                    "created_at": formatted_date,
                    "birthday": birthday_data,
                }
                return user_info
            else:
                return False
        except Exception as e:
            logger.error(f"{self.id} 解析用户数据失败: {e}")
            return False
        finally:
            if last_tab:
                last_tab.close()

    #
    def is_login(self):
        last_tab = None
        try:
            last_tab = self.page.new_tab("https://x.com/home")
            sleep(3)

            if any(path in last_tab.url for path in ["flow/login", "account/access", "logout"]):
                is_lock = "account/access" in last_tab.url
                return {"is_login": False, "is_lock": is_lock}

            # last_tab.listen.start(
            #     "https://x.com/i/api/graphql/-0XdHI-mrHWBQd8-oLo1aA/ProfileSpotlightsQuery"
            # )
            # last_tab.ele("@data-testid=AppTabBar_Profile_Link").click()
            # res = last_tab.listen.wait(timeout=30)

            # if not res:
            #     logger.warning(f"{self.id} 未能获取到用户数据响应")
            #     return {"is_login": False, "is_lock": False}

            # body = json.loads(res.response.raw_body)
            # if (
            #     body.get("data")
            #     .get("user_result_by_screen_name")
            #     .get("result")
            #     .get("legacy")
            # ):
            #     return {"is_login": True, "is_lock": False}

            return {"is_login": True, "is_lock": False}
        except Exception as e:
            logger.error(f"{self.id} 解析用户数据失败: {e}")
            return {"is_login": False, "is_lock": False}
        finally:
            if last_tab:
                last_tab.close()

    def _pass_2fa(self, latest_tab, two_fa):
        ele = latest_tab.ele("@data-testid=ocfEnterTextTextInput", timeout=5)
        if ele:
            logger.info(f"{self.id} 检测到需要过2FA...")
            if not two_fa:
                logger.error(f"{self.id} 需要配置 2fa ...")
                return False

            totp = pyotp.TOTP(two_fa)
            latest_tab.ele("@data-testid=ocfEnterTextTextInput").input(totp.now())
            latest_tab.ele("@data-testid=ocfEnterTextNextButton").click()
            # 等待重定向到主页
            if latest_tab.wait.url_change(text="x.com/home", timeout=30):
                return True

        return False

    def _pass_funcapcha(self, latest_tab):
        max_attempts = 1
        for attempt in range(1, max_attempts + 1):
            try:
                # 检查是否已经在主页
                if "x.com/home" in latest_tab.url and attempt > 1:
                    # logger.success(f"{self.id} 账号已解锁或不需要解锁")
                    return True

                is_unlocked = latest_tab.ele("Account unlocked", timeout=3)
                if is_unlocked:
                    # 点击最终的提交按钮
                    final_submit = latest_tab.ele("x://input[@type='submit']", timeout=10)
                    if final_submit:
                        final_submit.click()
                        if latest_tab.wait.url_change(text="x.com/home", timeout=30):
                            return True
                    continue

                # 等待 Arkose Labs 验证码框架出现
                iframe = latest_tab.ele("#arkose_iframe", timeout=20)
                if not iframe:
                    logger.warning(f"{self.id} 未检测到iframe，可能不需要验证或页面加载问题")

                    # 检查是否已经在主页
                    if "x.com/home" in latest_tab.url:
                        logger.success(f"{self.id} 账号已解锁或不需要解锁")
                        return True

                    latest_tab.refresh()
                    sleep(3)
                    continue

                logger.info(f"{self.id} 检测到需要过验证码...")
                is_exists = YesCaptcha.is_plugin_exists(latest_tab)
                if not is_exists:
                    logger.warning(f"{self.id} 未检测到YesCaptcha插件，需要手动验证")
                    return False

                # 这里利用了yescapcha插件的自动化处理验证码的能力
                # 等待"Account unlocked"文本出现，表示验证成功
                is_unlocked = latest_tab.ele("Account unlocked", timeout=30)
                if is_unlocked:
                    # 点击最终的提交按钮
                    final_submit = latest_tab.ele("x://input[@type='submit']", timeout=10)
                    if final_submit:
                        final_submit.click()

                        # 等待重定向到主页
                        if latest_tab.wait.url_change(text="x.com/home", timeout=30):
                            return True

                # 如果到这里还没有成功，刷新页面重试
                latest_tab.refresh()
                sleep(3)

            except Exception as e:
                logger.error(f"{self.id} 过验证码过程中发生错误: {str(e)}")
                latest_tab.refresh()
                sleep(3)

        logger.error(f"{self.id} 尝试 {max_attempts} 次后仍未能过验证码")
        return False

    def _pass_email_verify(self, latest_tab, x_email, x_email_pwd, proxy_email):
        # 检查是否需要邮箱检测
        try:
            check_email = latest_tab.ele("x://*[contains(text(), '@') and contains(text(), '***')]", timeout=5)
            if check_email:
                logger.info(f"{self.id} 检测到需要输入邮箱验证码...")

                send_email_ele = latest_tab.ele("x://input[@type='submit']", timeout=3)
                if send_email_ele:
                    send_email_ele.click()
                    sleep(5)

                code = self._get_access_email_code(x_email, x_email_pwd, proxy_email)
                if not code:
                    return False

                # 输入验证码
                latest_tab.ele("@name=token", timeout=10).input(code)
                latest_tab.ele("@type=submit", timeout=10).click()

                is_unlocked = latest_tab.ele("Account unlocked", timeout=30)
                if is_unlocked:
                    # 点击最终的提交按钮
                    final_submit = latest_tab.ele("x://input[@type='submit']", timeout=10)
                    if final_submit:
                        final_submit.click()
                        # 等待重定向到主页
                        if latest_tab.wait.url_change(text="x.com/home", timeout=30):
                            return True
            return False
        except Exception as e:
            logger.error(f"{self.id} 解锁邮箱验证码失败: {e}")
            return False

    def unlock(self, x_email, x_email_pwd, proxy_email, two_fa):
        """尝试解锁被锁定的Twitter/X账号"""
        latest_tab = self.page.new_tab("https://x.com/home")
        sleep(5)

        try:
            div_ele = latest_tab.ele(
                "x:(//div[@id='ppIS7']/div/div | //div[@style='display: grid;']/div/div)",
                timeout=10,
            )
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
            if checkbox:
                checkbox.wait.has_rect(timeout=20)
                checkbox.click()
                sleep(3)
        except Exception as e:
            logger.warning(f"【{self.id}】过CF盾失败， error={str(e)}")

        # 点击提交按钮
        # submit_button = latest_tab.ele("x://input[@type='submit']", timeout=10)
        # if submit_button:
        #     submit_button.click()
        try_click(latest_tab, "x://input[@type='submit']")

        # 邮箱
        result = self._pass_email_verify(latest_tab, x_email, x_email_pwd, proxy_email)
        if result:
            return True

        # 验证码
        result = self._pass_funcapcha(latest_tab)
        if result:
            return True

        # 2fa
        result = self._pass_2fa(latest_tab, two_fa)
        if result:
            return True

        return False

    def get_token(self):
        last_tab = None
        try:
            last_tab = self.page.new_tab(url="https://x.com/home")
            cookies = last_tab.cookies()

            for cookie in cookies:
                if cookie.get("name") == "auth_token":
                    return cookie.get("value")

            logger.warning(f"{self.id} 获取twitter token失败, 未找到auth_token")
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取auth_token失败: {e}")
            return None
        finally:
            if last_tab:
                last_tab.close()

    def get_last_post_url(self):
        last_tab = self.page.get_tab(0)
        url = last_tab.url
        if "x.com" not in url:
            logger.warning("不在推特url...")
            return

        ele = last_tab.ele("x://button[@data-testid='app-bar-close']", timeout=6)
        if ele:
            ele.click()

        last_tab.ele("@data-testid=AppTabBar_Profile_Link").click()
        ele = last_tab.ele("x://div[@data-testid='User-Name']/div[2]/div/div[3]/a")
        herf = ele.link_social

        return herf

    # 发布推文【未实现】
    def post_tweet(self, url, content):
        last_tab = self.page.new_tab(url)
        # 点击发布推文按钮
        last_tab.ele("x://div[@data-testid='tweetTextarea_0']").input(content)
        last_tab.ele("x://div[@data-testid='tweetButton']").click()
        last_tab.close()

    def get_follow_users(self, count):
        last_tab = self.page.new_tab("https://x.com/home")
        last_tab.ele("@data-testid=AppTabBar_Profile_Link").click()
        username = last_tab.url.replace("https://x.com/", "")

        last_tab.get(f"https://x.com/{username}/followers")

        divs = last_tab.eles('x://*[@data-testid="cellInnerDiv"]//a[@role="link" and @tabindex="-1"]')
        users = []
        if len(divs) < count:
            logger.warning(f"{self.id} {username} 关注的粉丝数量太少了，请增加粉丝数")
            return users

        eles = random.sample(divs, count)
        for ele in eles:
            herf = ele.attr("href")
            _x_name = herf.replace("https://x.com/", "")
            users.append(_x_name)
        last_tab.close()
        return users

    def _get_update_email_code(self, tw_email, tw_email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(tw_email, tw_email_pwd)

            search_criteria = SearchCriteria(
                subject="is your X verification code",
                to=proxy_email,
                is_read=False,
            )

            try:
                # 尝试获取未读邮件
                emails = email_client.search_emails_with_retry(search_criteria)
            except Exception as e:
                logger.error(f"{self.id} 获取未读邮件时发生错误: {e}")
                emails = []

            if not emails:
                last_email = email_client.get_latest_email()
                if not last_email:
                    logger.error(f"{self.id} 未找到验证码邮件")
                    return None
                emails.append(last_email)

            email = emails[0]
            subject = email["subject"]
            code_match = re.search(r"^(\d{6})", subject)
            if not code_match:
                logger.error(f"{self.id} 未找到验证码")
                return None

            # TODO: 需要删除邮件？
            # email_client.delete_email(email["id"], email["folder"])

            return code_match.group(1)
        except Exception as e:
            logger.error(f"{self.id} 获取验证码失败: {e}")
            return None

    # 更新推特邮箱
    def update_email(self, tw_pwd, tw_email, tw_email_pwd, proxy_email=None):
        # 1. 打开邮箱更新页面
        last_tab = self.page.new_tab("https://x.com/i/flow/add_email")
        sleep(6)
        try:
            pwd_input = last_tab("x://input[@type='password']", timeout=3)
            if not pwd_input:
                if last_tab("x://span[contains(text(), 'restricted temporarily')]", timeout=5):
                    logger.error(f"{self.id} 更新邮箱失败，账号暂时限制更新操作，请48小时后再试")
                    return False

                if last_tab(
                    "x://span[contains(text(), 'Oops, something went wrong. Please try again later.')]",
                    timeout=5,
                ):
                    logger.error(f"{self.id} 更新邮箱失败，请先登录推特")
                    return False

                logger.error(f"{self.id} 更新邮箱失败，网络异常请稍后再试")
                return False

            pwd_input.input(tw_pwd)
            sleep(3)

            last_tab("x://button[@data-testid='LoginForm_Login_Button']").click()
            sleep(2)

            # 优先使用代理邮箱
            email = proxy_email or tw_email
            email_input = last_tab("x://input[@type='email']")
            email_input.clear(True)
            email_input.input(email)
            sleep(2)

            last_tab("x://button[@data-testid='ocfEnterEmailNextLink']").click()
            sleep(10)

            # 获取验证码
            code = self._get_update_email_code(tw_email, tw_email_pwd, proxy_email)
            if not code:
                logger.error(f"{self.id} 修改推特邮箱失败，请检查邮箱配置或确认验证码是否已正确发送")
                return False

            input_field = last_tab("x://input[@name='verfication_code']")
            input_field.clear(True)
            input_field.input(code)
            sleep(2)

            last_tab("x://button[contains(., 'Verify')]").click()

            if self._check_email(last_tab, tw_pwd, email):
                logger.success(f"{self.id} 邮箱修改成功")
                return True
        except Exception as e:
            logger.error(f"{self.id} 邮箱修改失败: {e}")
            pass

        logger.error(f"{self.id} 邮箱修改失败，请手动处理")
        return False

    def _update_nickname_internal(self, last_tab, new_name, success_condition):
        """
        内部通用的昵称更新方法

        Args:
            last_tab: 浏览器标签页对象
            new_name: 要更新的新昵称
            success_condition: 用于验证更新是否成功的函数，接收更新后的name作为参数

        Returns
        -------
            bool: 更新是否成功
        """
        try:
            username_input = last_tab("x://input[@name='displayName']", timeout=3)
            if not username_input:
                logger.error(f"{self.id} 未找到用户名输入框")
                return False

            username_input.clear(True)
            username_input.input(new_name)
            sleep(1)

            last_tab.listen.start("https://api.x.com/1.1/account/update_profile.json")
            for _ in range(3):
                last_tab("x://button[@data-testid='Profile_Save_Button']").click()
                res = last_tab.listen.wait(timeout=30)
                if not res:
                    logger.error(f"{self.id} 更新用户名失败")
                    continue

                body = json.loads(res.response.raw_body)
                name = body.get("name")
                if not name:
                    logger.error(f"{self.id} 更新用户名失败")
                    continue

                if success_condition(name):
                    logger.success(f"{self.id} 更新用户名成功")
                    return True

            logger.error(f"{self.id} 更新用户名失败")
            return False

        except Exception as e:
            logger.error(f"{self.id} 更新用户名失败: {e}")
            return False

    # 移除推特昵称后缀
    def remove_nickname_suffix(self, suffix: str):
        last_tab = self.page.new_tab("https://x.com/settings/profile")
        sleep(6)
        try:
            username_input = last_tab("x://input[@name='displayName']", timeout=3)
            if not username_input:
                logger.error(f"{self.id} 未找到用户名输入框")
                return False

            username = username_input.value
            if suffix not in username:
                logger.success(f"{self.id} 用户名后缀已移除")
                return True

            new_name = username.replace(suffix, "")
            return self._update_nickname_internal(last_tab, new_name, lambda name: suffix not in name)
        except Exception as e:
            logger.error(f"{self.id} 移除用户名后缀失败: {e}")
            return False

    # 添加推特昵称后缀
    def add_nickname_suffix(self, suffix: str):
        last_tab = self.page.new_tab("https://x.com/settings/profile")
        sleep(6)
        try:
            username_input = last_tab("x://input[@name='displayName']", timeout=3)
            if not username_input:
                logger.error(f"{self.id} 未找到用户名输入框")
                return False

            username = username_input.value
            if suffix in username:
                logger.success(f"{self.id} 用户名后缀已存在")
                return True

            username = username + suffix
            return self._update_nickname_internal(last_tab, username, lambda name: suffix in name)
        except Exception as e:
            logger.error(f"{self.id} 添加用户名后缀失败: {e}")
            return False

    # 更新推特昵称
    def update_nickname(self, nickname: str):
        last_tab = self.page.new_tab("https://x.com/settings/profile")
        sleep(6)
        try:
            return self._update_nickname_internal(last_tab, nickname, lambda name: nickname == name)
        except Exception as e:
            logger.error(f"{self.id} 更新用户名失败: {e}")
            return False

    # 根据关键字删除推文
    def delete_tweet(self, keyword):
        last_tab = self.page.new_tab("https://x.com/home")
        try:
            ele = last_tab.ele("x://button[@data-testid='app-bar-close']", timeout=5)
            if ele:
                ele.click()

            last_tab.ele("@data-testid=AppTabBar_Profile_Link").click()

            # 找到accessible list
            accessible_list_ele = last_tab.ele("x://section[@aria-labelledby='accessible-list-2']", timeout=10)
            if not accessible_list_ele:
                logger.error(f"{self.id} 未找到推文元素")
                return False

            # 找到包含关键词的推文文章元素
            tweet = last_tab.ele(
                f"x://article[.//div[@data-testid='tweetText' and contains(., '{keyword}')]]",
                timeout=10,
            )
            if not tweet:
                logger.error(f"{self.id} 未找到包含关键词的推文")
                return True

            more_button = tweet.ele("x://button[@data-testid='caret']")
            if not more_button:
                logger.error(f"{self.id} 未找到更多按钮")
                return False

            more_button.click()
            sleep(1)

            # 点击删除选项
            delete_button = last_tab("x://div[@data-testid='Dropdown']//div[@role='menuitem'][1]", timeout=3)
            if not delete_button:
                logger.error(f"{self.id} 未找到删除按钮")
                return False

            delete_button.click()
            sleep(1)

            # 点击删除选项
            delete_button = last_tab("x://button[@data-testid='confirmationSheetConfirm']", timeout=3)
            if not delete_button:
                logger.error(f"{self.id} 未找到删除按钮")
                return False
            delete_button.click()
            sleep(1)

            confirm_button = last_tab("x://button[@data-testid='confirmationSheetConfirm']")
            if not confirm_button:
                logger.error(f"{self.id} 未找到确认删除按钮")
                return False
            confirm_button.click()

            logger.success(f"{self.id} 推文删除成功")
            return True
        finally:
            last_tab.close()

    # 更新推特密码
    def update_password(self, tw_pwd, tw_new_pwd):
        try:
            latest_tab = self.page.latest_tab
            latest_tab.get("https://x.com/settings/password")
            sleep(5)

            current_password = latest_tab.ele("x://input[@name='current_password']")
            current_password.clear(True)
            current_password.input(tw_pwd)
            sleep(2)

            new_password = latest_tab.ele("x://input[@name='new_password']")
            new_password.clear(True)
            new_password.input(tw_new_pwd)
            sleep(2)

            password_confirmation = latest_tab.ele("x://input[@name='password_confirmation']")
            password_confirmation.clear(True)
            password_confirmation.input(tw_new_pwd)
            sleep(2)

            latest_tab.listen.start("https://x.com/i/api/i/account/change_password.json")
            latest_tab.ele("x://button[@data-testid='settingsDetailSave']").click()
            res = latest_tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"{self.id} 更新密码失败")
                return False

            body = json.loads(res.response.raw_body)
            if body.get("status") == "ok":
                logger.success(f"{self.id} 更新密码成功")
                return True

            message = body.get("errors")[0].get("message")
            logger.error(f"{self.id} 更新密码失败: {message}")
            return False

        except Exception as e:
            logger.error(f"{self.id} 更新密码失败: {e}")
            return False

    def _check_email(self, tab, password, email):
        tab.get("https://x.com/settings/your_twitter_data/account")
        sleep(3)

        pwd_ele = tab("x://input[@name='current_password']", timeout=10)
        if pwd_ele:
            pwd_ele.input(password)
            sleep(2)

            tab("x://button[contains(., 'Confirm')]").click()
            sleep(2)

        return tab(f"{email}", timeout=5) is not None

    def _generate_script(self, auth_token):
        script = f"""
        function setCookie(name, value, options = {{}}) {{
            options = {{ path: "/", ...options }};
            if (options.expires instanceof Date) {{
                options.expires = options.expires.toUTCString();
            }}
            let updatedCookie =
                encodeURIComponent(name) + "=" + encodeURIComponent(value);
            for (let optionKey in options) {{
                updatedCookie += "; " + optionKey;
                let optionValue = options[optionKey];
                if (optionValue !== true) {{
                    updatedCookie += "=" + optionValue;
                }}
            }}
            document.cookie = updatedCookie;
        }}

        function deleteAllCookies() {{
            const cookies = document.cookie.split(";");
            for (let i = 0; i < cookies.length; i++) {{
                const cookie = cookies[i];
                const eqPos = cookie.indexOf("=");
                const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
            }}
        }}

        deleteAllCookies();

        setCookie("auth_token", "{auth_token}", {{
            secure: true,
            "max-age": 3600 * 24 * 365,
            domain: "x.com",
        }});

        setCookie("auth_token", "{auth_token}", {{
            secure: true,
            "max-age": 3600 * 24 * 365,
            domain: "twitter.com",
        }});

        setTimeout(() => {{
            window.location.href = "https://x.com";
        }}, 2500);
    """
        return script

    def _first_verify_email(self, x_email, x_email_pwd, proxy_email):
        # 检查用户名和密码是否为空
        if not x_email or not x_email_pwd:
            logger.error(f"{self.id} 邮箱或邮箱密码为空")
            return False

        code = self._get_auth_email_code(x_email, x_email_pwd, proxy_email)
        if not code:
            logger.error(f"{self.id} 无法获取验证码")
            return False

        try:
            input_field = self.page.latest_tab.ele("@data-testid=ocfEnterTextTextInput", timeout=10)
            input_field.input(code)
            sleep(1)

            next_button = self.page.latest_tab.ele("@data-testid=ocfEnterTextNextButton", timeout=10)
            next_button.click()
            return True
        except Exception as e:
            logger.error(f"验证过程中出错: {e}")
            return False

    def _handle_account_access(self, x_email, x_email_pwd, two_fa):
        """处理账号访问验证流程"""
        # 1. 首先检查账号是否被锁定
        if self._handle_account_locked():
            return False

        # 2. 然后处理邮箱验证
        if self._handle_email_verification(x_email, x_email_pwd):
            return False

        # 3. 最后处理两步验证
        if self._handle_two_factor_auth(two_fa):
            return False

        # 4. 验证成功后检查是否回到首页
        return "x.com/home" in self.page.url

    def _handle_account_locked(self):
        locked_ele = self.page.latest_tab.ele("Your account has been locked", timeout=3)
        if locked_ele:
            logger.warning(f"{self.id} 被锁定,需要解锁...")
            submit_ele = self.page.latest_tab.ele("@type=submit", timeout=5)
            if submit_ele:
                submit_ele.click()
            return True
        return False

    def _handle_email_verification(self, x_email, x_email_pwd):
        """处理邮箱验证"""
        if not x_email or not x_email_pwd:
            logger.error(f"{self.id} 邮箱或邮箱密码为空")
            return True

        verify_elements = {
            "Please verify your email address": self._handle_verify_email,
            "We sent your verification code": self._handle_verification_code,
            "Check your email": self._first_verify_email,  # 添加这个场景的处理
        }

        for text, handler in verify_elements.items():
            if self.page.latest_tab.ele(text, timeout=3):
                return not handler(x_email, x_email_pwd)

        return False

    def _handle_verify_email(self, x_email, x_email_pwd):
        """处理邮箱验证流程"""
        if not self._send_verification_email():
            return False
        return self.email_verifier.verify_email(x_email, x_email_pwd)

    def _handle_verification_code(self, x_email, x_email_pwd):
        """处理验证码输入流程"""
        code = self.email_verifier.get_verification_code(x_email, x_email_pwd)
        if not code:
            return False

        return self.email_verifier.enter_verification_code(code)

    def _send_verification_email(self):
        submit = self.page.latest_tab.ele("@type=submit", timeout=5)
        if not submit:
            logger.error(f"{self.id} 未找到发送邮箱按钮...")
            return False
        submit.click()
        return True

    def _handle_two_factor_auth(self, two_fa):
        latest_tab = self.page.latest_tab
        ele = latest_tab.ele("@data-testid=ocfEnterTextTextInput", timeout=5)
        if ele:
            if not two_fa:
                logger.error(f"{self.id} 需要配置 2fa ...")
                return True
            totp = pyotp.TOTP(two_fa)
            latest_tab.ele("@data-testid=ocfEnterTextTextInput").input(totp.now())
            latest_tab.ele("@data-testid=ocfEnterTextNextButton").click()
            sleep(8)
        return False

    def change_language_to_english(self):
        try:
            latest_tab = self.page.latest_tab
            latest_tab.get("https://x.com/settings/language")
            sleep(2)
            ele = get_element(
                latest_tab,
                "x://h2[@role='heading']/span[text()='Change display language']",
                timeout=3,
            )
            if ele:
                logger.info(f"{self.id} 已经是英文语言")
                return True

            try:
                get_element(latest_tab, "x://select[@id='SELECTOR_1']", timeout=10).click()
            except Exception as e:
                logger.error(f"{self.id} 未找到语言选择器: {e}")
                return False

            try:
                get_element(
                    latest_tab,
                    "x://option[@value='en']",
                    timeout=10,
                ).click()
            except Exception as e:
                logger.error(f"{self.id} 未找到英文选项: {e}")
                return False

            try:
                get_element(
                    latest_tab,
                    "x://button[@data-testid='settingsDetailSave']",
                    timeout=10,
                ).click()
            except Exception as e:
                logger.error(f"{self.id} 未找到保存按钮: {e}")
                return False

            ele = get_element(
                latest_tab,
                "x://h2[@role='heading']/span[text()='Change display language']",
                timeout=30,
            )
            if ele:
                return True

            return False
        except Exception as e:
            logger.error(f"{self.id} 切换语言失败: {e}")
            return False

    def get_emails(self):
        latest_tab = self.page.latest_tab
        latest_tab.listen.start("https://x.com/i/api/1.1/users/email_phone_info.json?include_pending_email=true")
        latest_tab.get("https://x.com/settings/email")
        sleep(2)
        res = latest_tab.listen.wait(timeout=30)
        if not res:
            logger.error(f"{self.id} 获取邮箱失败")
            return None
        body = json.loads(res.response.raw_body)
        emails = body.get("emails")
        if not emails:
            logger.error(f"{self.id} 获取邮箱失败")
            return None

        return emails

    def enable_2fa(self, password: str) -> str | None:
        fa_key = None
        try:
            latest_tab = self.page.latest_tab
            latest_tab.get("https://x.com/i/flow/two-factor-auth-app-enrollment")
            latest_tab.wait.ele_displayed("x://input[@type='password']", timeout=10)

            password_i = get_element(latest_tab, "x://input[@type='password']")
            if password_i:
                password_i.clear(True)
                password_i.input(password)

                latest_tab.wait.ele_displayed("x://button[@data-testid='LoginForm_Login_Button']", timeout=5)
                sleep(1)
                get_element(latest_tab, "x://button[@data-testid='LoginForm_Login_Button']").click()

            latest_tab.wait.ele_displayed("x://button[@data-testid='ActionListNextButton']")
            sleep(1)
            get_element(latest_tab, "x://button[@data-testid='ActionListNextButton']").click()

            latest_tab.wait.ele_displayed("x://div[@dir='ltr']//button[@type='button']", timeout=5)
            sleep(1)
            ele = get_element(latest_tab, "x://button[contains(., 'scan the QR code')]")
            if not ele:
                logger.error(f"{self.id} 请检查语言是否设置为英语")
                return fa_key
            ele.click()
            sleep(2)

            fa_span = get_element(
                latest_tab,
                "x://h1[@dir='ltr']/ancestor::div[2]/following-sibling::div/span",
            )
            fa_key = fa_span.text
            logger.info(f"{self.id} 获取到2FA Key: {fa_key}")
            totp = pyotp.TOTP(fa_key)

            get_element(latest_tab, "x://button[@data-testid='ocfShowCodeNextLink']").click()
            sleep(2)

            code_i = get_element(latest_tab, "x://input[@data-testid='ocfEnterTextTextInput']")
            code_i.clear(True)
            code_i.input(totp.now())

            next_btn = get_element(latest_tab, "x://button[@data-testid='ocfEnterTextNextButton']")
            next_btn.wait.enabled(timeout=10)
            next_btn.click()
            latest_tab.wait.ele_displayed("x://button[@data-testid='OCF_CallToAction_Button']", timeout=10)

            get_element(latest_tab, "x://button[@data-testid='OCF_CallToAction_Button']").click()
            sleep(2)
            if latest_tab.url == "https://x.com/home":
                logger.success(f"{self.id} 开启2FA成功")
                return fa_key
            else:
                logger.warning(f"{self.id} 获取到2FA Key={fa_key} 但验证失败")
                return None
        except Exception as e:
            logger.error(f"{self.id} 开启2FA失败，error={str(e)}")

    # 设置生日
    def set_birthday(self):
        latest_tab = None
        try:
            latest_tab = self.page.new_tab("https://x.com/settings/profile")
            sleep(10)
            ele = get_element(
                latest_tab,
                "x://button[@data-testid='pivot']",
                timeout=3,
            )

            user_info = self.get_user_info()
            if user_info.get("birthday"):
                logger.success("已经设置过生日，无需进入后续流程")
                return True

            try:
                ele.click()
                sleep(2)
                edit_ele = get_element(
                    latest_tab,
                    "x://button[@data-testid='confirmationSheetConfirm']",
                    timeout=3,
                )

                if edit_ele:
                    edit_ele.click()
                    logger.info("开始设置年月日...")

                    # 随机设置 月份
                    month_ele = get_element(
                        latest_tab,
                        "x://select[@data-testid='ProfileBirthdate_Month_Selector']",
                        timeout=3,
                    )
                    eles = month_ele.eles("x://option", timeout=10)[1:]
                    random.choice(eles).click()
                    sleep(2)
                    # 随机设置 天
                    month_day = get_element(
                        latest_tab,
                        "x://select[@data-testid='ProfileBirthdate_Day_Selector']",
                        timeout=3,
                    )
                    eles = month_day.eles("x://option", timeout=10)[1:]
                    random.choice(eles).click()
                    sleep(2)

                    # 随机设置 年
                    month_year = get_element(
                        latest_tab,
                        "x://select[@data-testid='ProfileBirthdate_Year_Selector']",
                        timeout=3,
                    )

                    random_number = random.randint(1985, 1999)
                    year_ele = month_year.ele(f"x://option[@value='{random_number}']")
                    year_ele.click()
                    sleep(2)

                    btn = get_element(
                        latest_tab,
                        "x://button[@data-testid='Profile_Save_Button']",
                        timeout=3,
                    )
                    btn.click()
                    sleep(3)

                    btn = get_element(
                        latest_tab,
                        "x://button[@data-testid='confirmationSheetConfirm']",
                        timeout=3,
                    )

                    latest_tab.listen.start("account/update_profile.json")
                    btn.click()
                    res = latest_tab.listen.wait(timeout=30)
                    if not res:
                        logger.error(f"{self.id} 生日设置失败")
                        return False

                    if res.response.status == 200:
                        logger.success(f"{self.id} 生日设置成功")
                        return True
                    else:
                        logger.error(f"{self.id} 生日设置失败")
                        return False

            except Exception as e:
                logger.error(f"{self.id} 未找到语言选择器: {e}")
                return False

            return False
        except Exception as e:
            logger.error(f"{self.id} 生日判断/设置失败: {e}")
            return False
        finally:
            if latest_tab:
                latest_tab.close()
