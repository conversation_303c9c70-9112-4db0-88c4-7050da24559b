from typing import Optional
import re

from loguru import logger

from .imap_client import IMAPClient, MailConfig, SearchCriteria
from ..exceptions import MailOperationError


class RamblerClient(IMAPClient):
    """Rambler 邮箱客户端"""

    SUPPORTED_DOMAINS = ["rambler.ru"]
    IMAP_SERVER = "imap.rambler.ru"
    IMAP_PORT = 993

    def __init__(self, email: str, password: str):
        """初始化 Rambler 邮箱客户端

        Args:
            email: Rambler 邮箱地址
            password: 密码
        """
        config = MailConfig(
            email=email,
            password=password,
            imap_server=self.IMAP_SERVER,
            imap_port=self.IMAP_PORT,
        )
        super().__init__(config)

    def _login(self) -> None:
        """登录 Rambler 邮箱"""
        try:
            self._connect()
            self._imap.login(self._email, self._password)
            logger.info("Rambler 邮箱登录成功")
        except Exception as e:
            logger.error(f"Rambler 邮箱登录失败: {str(e)}")
            raise

    def search_emails(self, criteria: SearchCriteria) -> list:
        """搜索邮件

        Args:
            criteria: 搜索条件

        Returns
        -------
            list: 符合条件的邮件列表
        """
        return super().search_emails(criteria)

    def get_latest_email(self) -> dict | None:
        """获取最新邮件

        Returns
        -------
            Optional[dict]: 最新邮件信息，如果没有则返回 None
        """
        return super().get_latest_email()

    def list_folders(self) -> list[str]:
        """获取邮箱中所有可用的文件夹列表并打印"""
        try:
            self._validate_connection()
            result, folder_list = self.server.list()

            if result != "OK":
                raise MailOperationError(f"获取文件夹列表失败: {result}")

            folders = []
            for folder_info in folder_list:
                if not folder_info:
                    logger.debug("跳过空文件夹信息")
                    continue

                try:
                    if isinstance(folder_info, bytes):
                        folder_str = folder_info.decode("utf-8", errors="replace")
                    else:
                        folder_str = str(folder_info)

                    # 使用正则表达式提取文件夹名称
                    # 匹配格式：(flags) "delimiter" folder_name
                    match = re.match(r'\((.*?)\) "([^"]+)" (.+)', folder_str)
                    if match:
                        flags, delimiter, folder_name = match.groups()
                        folders.append(folder_name.strip())
                        # logger.info(f"找到文件夹: {folder_name}")
                    else:
                        # 尝试简单提取最后一个部分作为文件夹名称
                        parts = folder_str.rsplit('"', 2)
                        if len(parts) >= 2:
                            folder_name = parts[-1].strip()
                            folders.append(folder_name)
                            # logger.info(f"找到文件夹（备用解析）: {folder_name}")
                        else:
                            logger.warning(f"无法解析文件夹名称: {folder_str}")
                except Exception as e:
                    logger.warning(f"处理文件夹信息失败: {folder_str}, 错误: {str(e)}")
                    continue

            if not folders:
                logger.warning("未找到任何文件夹")
            else:
                logger.info(f"共找到 {len(folders)} 个文件夹: {folders}")

            return folders

        except Exception as e:
            logger.error(f"获取文件夹列表失败: {str(e)}")
            return []
