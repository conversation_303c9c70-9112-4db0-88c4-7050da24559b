import click
import random
import os 
import json
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from time import sleep
from retry import retry
from src.browsers.operations import try_click
from src.utils.element_util import  get_elements, get_element
from src.utils.thread_executor import ThreadExecutor
from PIL import Image
from src.utils.mouse_trajectory import humanMouse
import io
from src.utils.element_util import get_element,get_elements
from DrissionPage.common import Actions
default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

class FaucetOKX:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        logger.info(f"Initialized FaucetOKX with ID: {self.id} and Browser Type: {self.browser_type}")

    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for i in range(try_count):
            connect_wallet_button = lasted_tab.ele(
                "x://button[.//span[contains(text(), '连接钱包') or contains(text(), 'Connect wallet')]]", timeout=5
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("x://button[.//span[text()='连接' or text()='Connect']]").click()
                sleep(2)
                self.browser_controller.okx_wallet_connect()
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _fake_task(self, tab):
        claimed_selector = "x://*[contains(@class, 'okds-success-circle-fill')]"
        u_selector = "x://div[.//span[contains(text(), '余额不足') or contains(text(), 'Insufficient balance')]]"
        if  get_elements(tab, u_selector, 3):
            logger.info("钱包余额小于 10 USD,请充值")
            return False
        sleep(2)
        # 循环5次，总共10秒去判断返回值
        for i in range(5):
            logger.info(f"【{self.id}】 完成{len(get_elements(tab, claimed_selector, 3))}个任务")
            if len(get_elements(tab, claimed_selector, 3))==4:
                return True        
            else:
                for ele in get_elements(tab, "x://*[contains(@class, 'index_task-item__xnaIH')]", 3):
                    ele.click()
                    sleep(1)
            tab.refresh()
            sleep(3)
        return False

    def _(self, tab):
        claimed_selector = "x://div[contains(text(), '当前 IP 最多支持 3 个地址领取')]"
        # 循环5次，总共10秒去判断返回值
        for attempt in range(20):
            if get_element(tab, claimed_selector, 1):
                return True
            sleep(1)
        return False

    def _claim(self, lasted_tab):
        for index,packet in enumerate(lasted_tab.listen.steps()):
            
                url = packet.request.url           
                # Check if the response body is a PNG image
                body =  packet.response.body

                if  "https://gcaptcha4.geetest.com/verify" in url :
                    try:
                        
    
                        # Handle JSONP response (extract JSON from function call)
                        if body.startswith('geetest_'):
                            # Extract the JSON part from JSONP response using regex
                            response_data = json.loads(body.split("(", 1)[1].rstrip(")"))
                        else:
                            return False
                        # Check for geetest response format
                        if 'status' in response_data and 'data' in response_data and 'result' in response_data['data']:
                            if response_data['status'] == 'success' and response_data['data']['result'] == 'success':
                                logger.success(f"【{self.id}】 验证码验证成功")
                                continue
                            else:
                                logger.error(f"【{self.id}】 验证码验证失败: {response_data['data']['result']}")
                                raise
                    except json.JSONDecodeError:
                        logger.warning("无法解析JSON响应")
                if  "https://web3.okx.com/priapi/v2/wallet/faucet/receive" in url :
                    try:
                        if isinstance(body, str):
                            response_data = json.loads(body)
                        else:
                            response_data = body
                        error_code = response_data.get('error_code')
                        
                        if error_code == "0":  # Check for error_code "0"
                            logger.success(f"【{self.id}】 领取成功: {response_data.get('data', {})}")
                            return True
                        else:
                            error_msg = response_data.get('msg') or response_data.get('error_message') or "未知错误"
                            logger.error(f"【{self.id}】 领取失败: {error_msg} (Error Code: {error_code})")
                            if "IP" in error_msg:
                                self.browser_controller.close_page()
                            return False
                    except json.JSONDecodeError:
                        logger.warning("无法解析JSON响应")
                            
        return False
    def _is_claim(self, lasted_tab):
         for index,packet in enumerate(lasted_tab.listen.steps()):
                url = packet.request.url           
                # Check if the response body is a PNG image
                body =  packet.response.body
                if  "https://web3.okx.com/priapi/v2/wallet/faucet/checkReceiveQualification" in url :
                    try:
                                    if isinstance(body, str):
                                        response_data = json.loads(body)
                                    else:
                                        response_data = body
                                    code = response_data.get('code')
                                    if not response_data.get('data').get('walletAssetEnough'):
                                        logger.error(f"【{self.id}】 不可领取: 资产不足 ")
                                        return False
                                    elif not response_data.get('data').get('canReceive'):
                                        logger.success(f"【{self.id}】 不可领取，下次领取时间{response_data.get('data').get('nextReceiveTime')}")
                                        return False
                                    elif response_data.get('data').get('canReceive'):
                                        logger.success(f"【{self.id}】 可以领取，剩余领取次数{response_data.get('data').get('remainReceiveNum')}")
                                        return True
                                    else:
                                        error_msg = response_data.get('msg') or response_data.get('error_message') or "未知错误"
                                        logger.error(f"【{self.id}】 不可领取: {error_msg} (Code: {code})")
                                        return False
                    except json.JSONDecodeError:
                        logger.warning("无法解析JSON响应")
    def execute_task(self):
        self.browser_controller.okx_wallet_login()
        logger.info(f"Executing task for {self.id} with browser type {self.browser_type}")
       
        lasted_tab = self.page.new_tab("https://www.okx.com/zh-hans/web3/faucet/monad?id=66")
        
        sleep(2)
        if lasted_tab.ele("x://div[@class='tip-title' ]"):
            logger.info(f"【{self.id}】 OKX Web3 服务在 EEA 地区暂不可用")
            return False
        lasted_tab.refresh()
        lasted_tab.listen.start(["https://static.geetest.com/captcha_v4/d2ce0cc595/slide","https://gcaptcha4.geetest.com/verify","https://web3.okx.com/priapi/v2/wallet/faucet/receive","https://web3.okx.com/priapi/v2/wallet/faucet/checkReceiveQualification"])
        sleep(2)
        if lasted_tab.ele("x://*[@data-testid='okd-dialog-confirm-btn']"):
            lasted_tab.ele("x://*[@data-testid='okd-dialog-confirm-btn']").click()
        if  lasted_tab.ele(
                "x://button[.//span[contains(text(), '连接钱包') or contains(text(), 'Connect wallet')]]", timeout=5
            ):
            self._connect_wallet(lasted_tab)

        logger.info(f"【{self.id}】连接钱包完成")
        if not self._is_claim(lasted_tab):
             return True

        #result = self._connect_wallet(lasted_tab)
        if not self._fake_task(lasted_tab):
            logger.error(f"【{self.id}】任务完成失败")
            return False
        logger.info(f"【{self.id}】4个任务完成")
        sleep(2)
        logger.info(f"【{self.id}】点击领取按钮")
        try_click(lasted_tab,"x://button[.='领取' or .='Claim']")
        sleep(6)
     
        # 新增方法调用
        result = self.get_and_move_slider(lasted_tab)
        return result

    @retry(tries=3, delay=1)
    def get_and_move_slider(self, lasted_tab):
        i=0
        for index,packet in enumerate(lasted_tab.listen.steps()):
            logger.info(f"【{self.id}】 获取网络数据包{index}")
            body = packet.response.body
            png_dir = 'examples/monad/png'
            if not os.path.exists(png_dir):
                os.makedirs(png_dir)
                logger.info(f"Created directory: {png_dir}")

            if packet.response.headers.get('Content-Type') == 'image/png':
                with open(os.path.join('examples', 'monad', 'png', f'{self.id}_{i}.png'), 'wb') as f:
                    f.write(body)
                    i=i+1
                    if i==2:
                        break
            else:
                logger.warning(f"【{self.id}】Received non-PNG image data.")
                raise Exception("geetest image not found, triggering retry.")
        logger.info(f"【{self.id}】开始图像识别")
        import ddddocr
        det = ddddocr.DdddOcr(det=False, ocr=False) 

        with open(rf'examples\monad\png\{self.id}_1.png', 'rb') as f:
            target_bytes = f.read()
        
        with open(rf'examples\monad\png\{self.id}_0.png', 'rb') as f:
            background_bytes = f.read()

        target_img = Image.open(io.BytesIO(target_bytes))
        bg_img = Image.open(io.BytesIO(background_bytes))
        
        # Compare sizes and assign accordingly
        if target_img.size > bg_img.size:
            background_bytes, target_bytes = target_bytes, background_bytes
        
        res = det.slide_match(target_bytes, background_bytes, simple_target=True)
        
        if res['target'][0]==0:
            raise Exception("image not found, triggering retry.")
        
        slider = get_element(lasted_tab, "x://div[contains(@class, 'geetest_btn')]")
        ac = Actions(lasted_tab)
        if slider.tag:
            logger.info(f"【{self.id}】 开始滑动滑块{slider.tag}")
            ac.move_to(slider, duration=0.5)
            ac.move_to(slider, duration=0.5).right(3).hold()
            ac.wait(0.4)
            prefix = random.uniform(36, 38)

            mouse = humanMouse().getRandomTrackSpacingArray((res['target'][0] + res['target'][2]) / 2 - prefix, 10, 10)
            for row in mouse:
                x, y = row  # 解包每一行的值
                ac.move(offset_x=x, offset_y=y, duration=random.uniform(0.14, 0.19))
            ac.release()


            sleep(3)
            # ele=lasted_tab.ele("x://div[contains(@class, 'geetest_btn')]")
            # if  ele:
            #     raise Exception("Element not found, triggering retry.")

            if self._claim(lasted_tab):
                return True
            logger.info(f"【{self.id}】领水失败")
            return False  
        else:
            logger.error(f"【{self.id}】找不到滑块")

    def faucet(self):
        """直接运行FaucetOKX任务。"""
        logger.info(f"直接运行任务 for {self.id} with browser type {self.browser_type}")
        return self.execute_task()  # 调用现有的执行任务方法

@click.group()
def cli():
    """CLI for FaucetOKX operations."""
    pass

@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,  # 根据需要设置默认值
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    """Run the faucet tasks."""
    try:
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):

            faucet = FaucetOKX(type, str(index))
            result=faucet.execute_task()          
            if result:
                successful_indices.append(index)  # 将成功的索引添加到成功列表
                faucet.page.quit()
            return result  # 返回结果
        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_okx-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)

        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        failed_indices = [str(index).strip() for index in failed_indices]  # 移除空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise
#python3 examples\monad\faucet_okx.py run -t bit -i 2-3
if __name__ == "__main__":
    # 直接运行示例
    cli()
    # faucet = FaucetOKX(BrowserType.BIT, "1")
    # faucet.faucet()  # 直接调用faucet方法 18
