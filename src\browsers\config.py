from loguru import logger
from functools import lru_cache
from src.utils import get_project_root_path
import json
import os


@lru_cache(maxsize=1)
def _load_extension_config():
    """加载浏览器配置文件（带缓存）

    Returns:
        dict: 浏览器配置信息

    Raises:
        FileNotFoundError: 配置文件不存在
        json.JSONDecodeError: JSON格式错误
    """
    config_path = f"{get_project_root_path()}/config/browser_config.json"

    try:
        # 检查文件是否存在
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        # 读取和解析配置文件
        with open(config_path, "r", encoding="utf-8-sig") as f:
            config = json.load(f)

        return config
    except json.JSONDecodeError as e:
        logger.error(f"配置文件JSON格式错误: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"读取配置文件失败: {str(e)}")
        raise


def get_browser_extension_id(name: str, browser_type: str) -> str:
    """获取浏览器扩展ID"""
    try:
        extension_id_map = _load_extension_config().get(browser_type)
        extension_id = extension_id_map.get(name)
        return extension_id if extension_id else None
    except Exception as e:
        logger.error(f"获取浏览器扩展ID失败: {str(e)}")
        return None


def get_browser_data_path(browser_type: str) -> str:
    """获取浏览器数据路径"""
    try:
        extension_id_map = _load_extension_config().get(browser_type)
        browser_data_path = extension_id_map.get("browser_data_path") or None
        return browser_data_path
    except Exception as e:
        logger.error(f"获取浏览器数据路径失败: {str(e)}")
        return None


def get_extensions(browser_type: str) -> str:
    """获取浏览器扩展"""
    try:
        extension_id_map = _load_extension_config().get(browser_type)
        extensions = extension_id_map.get("extensions") or None
        return extensions
    except Exception as e:
        logger.error(f"获取浏览器数据路径失败: {str(e)}")
        return None
