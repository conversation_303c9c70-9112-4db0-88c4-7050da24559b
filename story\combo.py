import random
from time import sleep, time
from typing import Optional

from loguru import logger
from retry import retry

from src.browsers import BrowserType
from .base import StoryBase, WalletConnectionError, TaskExecutionError


class Story1Combo(StoryBase):
    """1Combo任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "1combo"
        self.home_url = "https://odyssey.1combo.io/"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"
        self.nft_my_combo_url = "https://testnet.1combo.io/mywallet?tab=MyCombos"

    def _shorten_address(self) -> str:
        return f"{self.wallet_address[:4]}…{self.wallet_address[-4:]}"

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        short_address = self._shorten_address()
        if page.ele(f"x://button[text()='{short_address}']", timeout=5):
            return True

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page.latest_tab):
                return True

            connect_button = page.latest_tab.ele(
                "x://button[text()='Connect Wallet']", timeout=5
            )
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            connect_button.click()
            btn_okx_wallet = page.latest_tab.ele(
                "x://div[text()='OKX Wallet']", timeout=5
            )
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # # 检查连接状态
            if self._check_wallet_connected(page):
                return True

            return False

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _find_task_button(self, tab, task_name: str, timeout: int = 5):
        """查找任务卡片中的按钮元素

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            button_text: 按钮文本，如 'Claim'，不传则只匹配 cursor-pointer 类
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        xpath = f"x://button[contains(text(),'{task_name}')]"

        try:
            return tab.ele(xpath, timeout=timeout)
        except Exception as e:
            logger.error(f"未找到任务 '{task_name}' 的按钮: {str(e)}")
            return None

    def _click_task_button(self, tab, task_name: str, timeout: int = 5):
        """点击任务按钮

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        task_button = self._find_task_button(tab, task_name, timeout)
        if not task_button:
            raise TaskExecutionError(f"{self.browser_id} 查找 {task_name} 按钮失败")
        task_button.click()

    def _wait_for_okx_wallet(self, page, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == "OKX Wallet":
                return True
            sleep(1)
        return False

    def _request_get_signature(self, address: str) -> Optional[str]:
        """获取签名"""
        try:
            url = f"https://testnet.1combo.io/api/story/mint_signature/{address}"
            # data = {"address": address}

            headers = {
                "content-type": "application/json",
                "origin": "https://odyssey.1combo.io",
                "referer": "https://odyssey.1combo.io/",
            }
            success, response_data = self._make_get_request(url=url, headers=headers)

            if not success or "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )
                return None

            if response_data["code"]["code"] != 0:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败： {response_data['code']['message']}"
                )
                return None

            return response_data["data"]

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return None

    def _mint_badge(self, signature: str):
        """点击Mint Badge按钮并处理mint流程"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                self.badge_contract_address,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False

    def _check_step_completed(self, tab, step_name: str) -> bool:
        """检查任务是否完成"""
        step_xpath = f"x://div[text()='{step_name}']"
        ele = tab.ele(step_xpath, timeout=5)
        if ele:
            next_ele = ele.next()
            if next_ele:
                return True
        return False

    def _exec_task(
        self, page, task_name: str, step_name: str, close_tab: bool = True
    ) -> bool:
        """执行任务"""
        if self._check_step_completed(page.latest_tab, step_name):
            return True
        self._click_task_button(page.latest_tab, task_name)
        if close_tab:
            page.latest_tab.close()
        sleep(3)
        if self._check_step_completed(page.latest_tab, step_name):
            return True
        return False

    def _task_follow_twitter(self, page) -> bool:
        """关注推特"""
        task_name = "Follow 1Combo on X"
        step_name = "Step One"
        if not self._exec_task(page, task_name, step_name):
            logger.error(f"{self.browser_id} 关注推特失败")
            return False
        logger.info(f"{self.browser_id} 关注推特成功")
        return True

    def _task_join_discord(self, page) -> bool:
        """加入Discord Server"""
        task_name = "Join Discord Server"
        step_name = "Step Two"
        if not self._exec_task(page, task_name, step_name):
            logger.error(f"{self.browser_id} 加入Discord Server失败")
            return False
        logger.info(f"{self.browser_id} 加入Discord Server成功")
        return True

    def _task_like_twitter(self, page) -> bool:
        """点赞推特"""
        task_name = "Like & RT"
        step_name = "Step Three"
        if not self._exec_task(page, task_name, step_name):
            logger.error(f"{self.browser_id} 点赞推特失败")
            return False
        logger.info(f"{self.browser_id} 点赞推特成功")
        return True

    def _accept_and_sign(self, page) -> bool:
        """点击Accept and sign按钮"""
        try:
            ele = page.latest_tab.ele(
                "x://div[contains(text(),'Accept and sign')]", timeout=20
            )
            if ele:
                ele.click()
                if self._wait_for_okx_wallet(page, timeout=20):
                    self._sign_okx_wallet()
                return True
            else:
                logger.info(f"{self.browser_id} 未弹出Accept and sign按钮")
                return False
        except Exception as e:
            logger.error(f"{self.browser_id} 点击Accept and sign按钮失败: {str(e)}")
            return False

    def _connect_wallet_on_nft_page(self, tab) -> bool:
        """
        https://testnet.1combo.io/ 页面连接钱包
        1、
        """
        try:
            logger.debug("检查testnet.1combo.io是否登录")
            # 钱包地址的小写缩写
            short_address = self._shorten_address().lower().split("…")
            locator = f"x://a[contains(text(), '{short_address[0]}') and contains(text(), '{short_address[1]}')]"
            timeout = 30
            start_time = time()
            while time() - start_time <= timeout:
                try:
                    if tab(locator, timeout=2):
                        return True
                except Exception as e:
                    pass

                try:
                    tab(
                        "x://div[contains(text(), 'Connect Wallet')]", timeout=2
                    ).click()
                    sleep(1)
                except Exception as e:
                    pass

                try:
                    tab("x://div[text()='MetaMask']", timeout=2).click()
                    sleep(1)
                except Exception as e:
                    pass

                if self._wait_for_okx_wallet(self.browser.page, timeout=5):
                    self._connect_okx_wallet()

                try:
                    tab(
                        "x://div[normalize-space()='Accept and sign']", timeout=3
                    ).click()
                    sleep(1)
                except Exception as e:
                    pass

                if self._wait_for_okx_wallet(self.browser.page, timeout=10):
                    self._sign_okx_wallet()

                if tab(locator, timeout=2):
                    logger.debug(f"{self.browser_id} 1COMBO页面连接钱包成功")
                    return True
            return False
        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _is_mint_test_nft(self, address: str):
        """
        检查钱包地址是否mint了NFT
        """
        try:
            url = f"https://testnet.1combo.io/api/nft/owned/{address}"
            headers = {
                "accept-language": "en,en-US;q=0.9",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "Referer": "https://testnet.1combo.io/mywallet?tab=MyNFTs",
            }
            params = {"size": 100}

            success, response = self._make_get_request(
                url=url, headers=headers, params=params
            )
            if success:
                return len(response.get("data", [])) >= 1
            return False
        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 检查是否mint test nft失败: {str(e)}"
            )
            return False

    def _is_combo(self, address: str):
        """
        检查钱包地址是否combo
        """
        try:
            url = f"https://testnet.1combo.io/api/combo/owned/{address}"
            headers = {
                "accept-language": "en,en-US;q=0.9",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "Referer": "https://testnet.1combo.io/mywallet?tab=MyCombos",
            }
            params = {"size": 20}

            success, response = self._make_get_request(
                url=url, headers=headers, params=params
            )
            if success:
                return len(response.get("data", [])) >= 1
            return False
        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 检查是否combo失败: {str(e)}"
            )
            return False

    def _mint_for_beta_test(self, tab) -> bool:
        try:
            if self._is_mint_test_nft(self.wallet_address):
                logger.debug("已经Mint Test NFT...")
                return True

            for _ in range(5):

                logger.debug(f"{self.browser_id} 未Mint NFT, 开始Mint")
                tab("x://div[normalize-space()='Claim Test NFTs']").click()
                sleep(2)

                try:
                    tab(
                        "x://div[normalize-space()='Accept and sign']", timeout=5
                    ).click()
                    sleep(2)
                except Exception as e:
                    pass

                if self._wait_for_okx_wallet(self.browser.page, timeout=10):
                    self._sign_okx_wallet()

                # 随机选择collection
                logger.debug(f"{self.browser_id} 随机选择Collection")
                eles = tab.eles(
                    "x://div[@class='rowTd flex justify-between text-t2']//div[contains(text(), 'Mint for Beta Test')]",
                    timeout=5,
                )
                if len(eles) == 0:
                    sleep(3)
                    continue
                random.choice(eles).click()
                sleep(2)

                # 随机选择NFT
                logger.debug(f"{self.browser_id} 随机选择NFT to mint")
                mints = tab.eles(
                    "x://div[@class='p-dialog-content']//div[contains(text(),'Mint')]",
                    timeout=5,
                )
                if len(mints) == 0:
                    sleep(3)
                    continue
                random.choice(mints).click()
                self.browser.page.wait.new_tab(timeout=10)

                # 确认钱包
                self._approve_okx_wallet(gas_limit=1500)

                for _ in range(5):
                    if self._is_mint_test_nft(self.wallet_address):
                        logger.debug(f"{self.browser_id} mint test nft 成功")
                        tab.refresh()
                        return True
                    sleep(2)
                tab.refresh()
            return False
        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} mint test NFT 失败: {str(e)}")

    def _combo(self, tab):
        try:
            if self._is_combo(self.wallet_address):
                logger.debug("已经COMBO...")
                return True

            for _ in range(5):
                logger.debug(f"{self.browser_id} 未combo, 开始combo")
                tab("x://div[contains(text(),'My NFTs')]", timeout=5).click()
                sleep(2)

                try:
                    tab(
                        "x://div[normalize-space()='Accept and sign']", timeout=5
                    ).click()
                    sleep(2)
                except Exception as e:
                    pass

                if self._wait_for_okx_wallet(self.browser.page, timeout=10):
                    self._sign_okx_wallet()

                tab("x://div[contains(text(), 'Owned')]", timeout=5).click()
                sleep(2)

                eles = tab.eles(
                    "x://div[@class='my-nft-item text-t1 font-SF-700 cursor-pointer']",
                    timeout=3,
                )
                if len(eles) == 0:
                    sleep(2)
                    continue
                random.choice(eles).click()
                sleep(3)

                eles = tab.eles("x://div[@class='btnfront font-SF-900']", timeout=5)
                if len(eles) == 0:
                    sleep(2)
                    continue
                random.choice(eles).click()
                sleep(3)

                latest_tab = self.browser.page.latest_tab
                latest_tab("x://div[contains(text(), 'Mint Combo')]", timeout=5).click()

                latest_tab.wait.ele_displayed(
                    "x://div[@class='p-dialog-content']//div[normalize-space()='Confirm']",
                    timeout=40,
                )
                latest_tab(
                    "x://div[@class='p-dialog-content']//div[normalize-space()='Confirm']",
                    timeout=3,
                ).click()

                self._approve_okx_wallet(gas_limit=1500)

                for _ in range(5):
                    if self._is_combo(self.wallet_address):
                        logger.debug(f"{self.browser_id} combo 成功")
                        return True
                    sleep(2)

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} COMBO 失败: {str(e)}")

    def _mint_combo(self, page) -> bool:
        """mint combo"""
        # 点击第一个COMBO按钮
        ele = page.latest_tab.ele("x://div[@class='btnfront font-SF-900']", timeout=20)
        if ele:
            ele.click()
        else:
            logger.error(f"{self.browser_id} 查找 COMBO 按钮失败")
            return False
        # 点击Mint Combo按钮
        ele = page.latest_tab.ele("x://div[contains(text(),'Mint Combo')]")
        if ele:
            sleep(5)
            ele.click()
        else:
            logger.error(f"{self.browser_id} 查找 Mint Combo 按钮失败")
            page.latest_tab.close()
            return False
        # 等待nft准备好
        eles = page.latest_tab.eles("x://div[contains(text(),'Confirm')]", timeout=600)
        if eles:
            eles[1].click()
            if self._wait_for_okx_wallet(page, timeout=20):
                self._sign_okx_wallet()
                sleep(10)
                page.latest_tab.close()
                return True
            else:
                logger.error(f"{self.browser_id} 点击Mint COMBO按钮, 未弹出OKX钱包")
        else:
            logger.error(f"{self.browser_id} 查找 Confirm 按钮失败")
        page.latest_tab.close()
        return False

    @retry(tries=3, delay=1)
    def _mint_nft_and_combo(self) -> bool:
        try:
            # 打开My Combo页面
            self.browser.open_url(self.nft_my_combo_url, new_tab=True)

            # 链接钱包
            tab = self.browser.page.latest_tab
            if not self._connect_wallet_on_nft_page(tab):
                logger.error(f"{self.browser_id} 连接钱包失败")
                return False

            # 检查是否有combo记录
            if self._is_combo(self.browser.browser_config.evm_address):
                return True

            # mint nft
            if not self._mint_for_beta_test(tab):
                logger.error(f"{self.browser_id} Mint test NFT失败")
                return False

            if not self._combo(tab):
                logger.error(f"{self.browser_id} Mint Combo 失败")
                return False
            self.browser.page.close_tabs(others=True)
            return True
        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} combo 失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} combo任务失败")
        finally:
            self.browser.page.close_tabs(others=True)

    def _task_mint_combo(self, page) -> bool:
        """点击Mint a Combo按钮"""
        task_name = "Mint a Combo"
        step_name = "Step Four"
        if not self._exec_task(page, task_name, step_name, close_tab=False):
            logger.error(f"{self.browser_id} Mint a Combo失败")
            return False
        logger.info(f"{self.browser_id} Mint a Combo成功")
        return True

    def _task_share_nft(self, page) -> bool:
        """点击Share NFT按钮"""
        task_name = "Share NFT"
        step_name = "Step Five"
        # 生成随机的x分享链接，类似https://x.com/Official_1Combo/status/1623301116283822080
        x_share_link = f"https://x.com/Official_1Combo/status/{random.randint(1000000000000000000, 9999999999999999999)}"
        x_share_link_input = page.latest_tab.ele(
            'x://input[@placeholder="Enter link"]', timeout=10
        )
        if x_share_link_input:
            x_share_link_input.input(x_share_link)
        else:
            logger.error(f"{self.browser_id} 查找Share NFT输入框失败")
            return False
        if not self._exec_task(page, task_name, step_name, close_tab=False):
            logger.error(f"{self.browser_id} Share NFT失败")
            return False
        logger.info(f"{self.browser_id} Share NFT成功")
        return True

    @retry(tries=3, delay=1)
    def task(self, clear_other_tabs: bool = True) -> bool:
        """执行1Combo任务"""
        if self._check_task_completed():
            return True

        if self._check_badge_minted():
            self._update_task_status(True)
            return True

        balance = self.get_nft_contract().get_balance()
        if balance < 4:
            logger.warning(f"{self.browser_id} 余额 {balance} 不足，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.browser.page)

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 可能需要签名
            if self._wait_for_okx_wallet(self.browser.page, timeout=5):
                self._sign_okx_wallet()

            # 执行 Follow 1Combo on X 任务
            if not self._task_follow_twitter(self.browser.page):
                logger.error(f"{self.browser_id} 关注1Combo on X 任务失败")
                return False

            # 执行 Join Discord Server 任务
            if not self._task_join_discord(self.browser.page):
                logger.error(f"{self.browser_id} 加入Discord Server 任务失败")
                return False

            # 执行 Like & RT 任务
            if not self._task_like_twitter(self.browser.page):
                logger.error(f"{self.browser_id} Like & RT 任务失败")
                return False

            # 执行 Mint a Combo 任务
            if not self._mint_nft_and_combo() or not self._task_mint_combo(
                self.browser.page
            ):
                logger.error(f"{self.browser_id} Mint a Combo 任务失败")
                return False

            # 执行Share NFT任务
            if not self._task_share_nft(self.browser.page):
                logger.error(f"{self.browser_id} Share NFT任务失败")
                return False

            # 获取签名
            signature = self._request_get_signature(
                self.browser.browser_config.evm_address
            )
            if not signature:
                logger.error(f"{self.browser_id} 获取签名失败")
                return False

            # 铸造NFT
            if not self._mint_badge(signature):
                return False

            logger.success(f"{self.browser_id} 1combo任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} 1Combo任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} 1Combo任务失败")
