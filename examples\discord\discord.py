import os

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.socials.discord import Discord
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/discord.log", rotation="10MB", level="SUCCESS")

# Discord服务器配置
JOIN_SERVERS = [
    {"name": "Zama", "server_id": "1287736665103798433", "invite_code": "zama"},
    {"name": "Monad", "server_id": "1036357772826120242", "invite_code": "monad"},
]

LEAVE_SERVERS = [
    "WarpGate Official",
    "EnsoFi",
    "RubyScore",
    "<PERSON><PERSON><PERSON> Cop",
    "Variance",
]


class DiscordServer:
    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil = None) -> None:
        """
        初始化DiscordServer实例

        Args:
            browser_type: 浏览器类型
            index: 浏览器索引
            data_util: 数据工具类实例，如果为None则自动创建
        """
        self.index = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address

        # 如果没有传入data_util，则自动创建
        if data_util is None:
            data_dir = os.path.join(get_project_root_path(), "examples", "discord")
            csv_path = os.path.join(data_dir, f"discord_{browser_type.name.lower()}.csv")
            self.data_util = DataUtil(csv_path)
        else:
            self.data_util = data_util

        # 初始化数据
        self._init_data()

    def _init_data(self) -> None:
        """
        初始化数据记录

        如果数据不存在则创建新的数据记录，包含索引、浏览器类型和地址信息。
        """
        try:
            # 检查是否已存在数据
            existing_data = self.data_util.get(self.address)
            if existing_data:
                return

            # 创建新的数据记录
            new_data = {"index": self.index, "type": self.browser_type.value, "address": self.address}
            self.data_util.add(new_data)
            logger.success(f"【{self.index}】{self.address} 数据初始化成功")
        except Exception as e:
            logger.error(f"【{self.index}】{self.address} 初始化数据失败: {e}")

    def _handle_join_server(
        self,
        invite_code: str,
        need_wait_captcha: bool,
        server_id: str,
        name: str,
    ) -> dict:
        """
        处理加入服务器的逻辑

        Args:
            invite_code: 邀请码
            need_wait_captcha: 是否需要等待验证码
            server_id: 服务器ID
            name: 服务器名称

        Returns:
            dict: 包含success和message的结果字典
        """
        for _ in range(3):
            result = self.browser_controller.join_dc_server(invite_code, need_wait_captcha, server_id, name)
            is_success = result.get("success")
            if is_success:
                return {"success": True, "message": "加入成功"}

            result_code = result.get("code")
            if result_code == "NO_LOGIN":
                result = self.browser_controller.login_discord()
                if not result:
                    return {"success": False, "message": "dc未登录，登录失败"}
            elif result_code == "BANNED":
                return {"success": False, "message": ""}

            elif result_code == "NEED_CAPTCHA":
                self.leave_server(1, False)

        return {"success": False, "message": "加入失败"}

    def join_server(
        self,
        need_wait_captcha: bool = True,
        close_page: bool = True,
    ) -> list:
        """
        加入Discord服务器

        Args:
            need_wait_captcha: 是否需要等待验证码
            close_page: 是否关闭页面

        Returns:
            list: 失败的服务器邀请码列表
        """
        record = self.data_util.get(self.address) or {}
        fail_servers = []

        for server in JOIN_SERVERS:
            invite_code = server.get("invite_code")
            server_id = server.get("server_id")
            name = server.get("name")

            try:
                if record.get(f"{name}", "") == "1":
                    logger.success(f"【{self.index}】已加入 {name} 服务器")
                    continue

                result = self._handle_join_server(invite_code, need_wait_captcha, server_id, name)
                if not result.get("success"):
                    logger.error(f"【{self.index}】加入 {name} 服务器失败: {result.get('message')}")
                    fail_servers.append(invite_code)
                    continue

                self.data_util.update(self.address, {f"{name}": "1"})
                logger.success(f"【{self.index}】加入 {name} 服务器成功")

            except Exception as e:
                logger.error(f"{self.index} 加群 {name} 失败: {e}")
                fail_servers.append(invite_code)

        if close_page:
            self.browser_controller.close_page()

        return fail_servers

    def leave_server(self, count: int = 1, is_close: bool = True) -> bool:
        """
        离开Discord服务器

        Args:
            count: 需要成功离开的服务器数量
            is_close: 是否关闭页面

        Returns:
            bool: 是否成功离开指定数量的服务器
        """
        success_count = 0
        record = self.data_util.get(self.address) or {}
        for name in LEAVE_SERVERS:
            try:
                if record.get(f"{name}", "") == "-1":
                    logger.success(f"【{self.index}】已退出 {name} 服务器")
                    continue

                discord = Discord(self.index, self.browser_controller.page, self.browser_controller.browser_type)
                result = discord.leave_guild(name)
                if result:
                    logger.success(f"【{self.index}】退出群组 {name} 成功")
                    success_count = success_count + 1
                    if count == success_count:
                        if is_close:
                            self.browser_controller.close_page()
                        self.data_util.update(self.address, {f"{name}": "-1"})
                        return True
            except Exception as e:
                logger.error(f"【{self.index}】退出群组 {name} 失败: {e}")

        if is_close:
            self.browser_controller.close_page()
        return False


def get_data_util(browser_type: BrowserType) -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """
    data_dir = os.path.join(get_project_root_path(), "examples", "discord")
    csv_path = os.path.join(data_dir, f"discord_{browser_type.name.lower()}.csv")
    return DataUtil(csv_path)


@click.group()
def cli():
    pass


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--wait", is_flag=True, help="是否等待验证码")
@click.option("-c", "--close", is_flag=True, default=True, flag_value=False, help="是否关闭浏览器")
def join_server(index, type, wait, close):
    indices = parse_indices(index)
    fail_indices = []
    data_util = get_data_util(type)
    for _index in indices:
        try:
            discord_server = DiscordServer(type, str(_index), data_util)
            fail_servers = discord_server.join_server(wait, close)
            if fail_servers and len(fail_servers) > 0:
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"【{_index}】加群失败: {e}")
            fail_indices.append(str(_index))
    if fail_indices:
        idxs = ",".join(fail_indices)
        logger.error(f"加群失败：{idxs}")


@cli.command("leave")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def leave_server(index, type):
    indices = parse_indices(index)
    data_util = get_data_util(type)
    for _index in indices:
        try:
            discord_server = DiscordServer(type, str(_index), data_util)
            result = discord_server.leave_server()
            if result:
                logger.success(f"【{_index}】退出群组成功")
            else:
                logger.error(f"【{_index}】退出群组失败")
        except Exception as e:
            logger.error(f"【{_index}】退出群组失败: {e}")


# 使用示例:
# python3 examples/discord/discord.py join -t chrome -c -w -i 1
# python3 examples/discord/discord.py leave -t chrome -i 1

if __name__ == "__main__":
    cli()
    # 测试示例:
    # discord_server = DiscordServer(BrowserType.CHROME, "3")
    # discord_server.join_dc_server(False)
