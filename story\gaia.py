import json
from loguru import logger
from time import sleep
from retry import retry
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
import traceback


class StoryGaia(StoryBase):
    """Gaia任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "gaia"
        self.home_url = "https://gaianet.ai/reward?invite_code=RUsLH4"
        self.api_url = "https://www.gaianet.ai/setting/gaia-api-keys"
        self.wallet_address = self.browser.browser_config.evm_address

    def _check_wallet_connected(self, tab) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = tab.ele(
                "x://span[contains(text(),'...')]",
                timeout=10,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            tab = page.latest_tab
            if self._check_wallet_connected(tab):
                return True

            connect_button = page.latest_tab.ele(
                "x://button[text()='Connect']", timeout=30
            )
            if not connect_button:
                raise WalletConnectionError(f"{self.browser_id} 查找 Connect 按钮失败")

            connect_button.click()

            tab.ele("x://button[.='OKX Wallet']", timeout=10).click()

            self._connect_okx_wallet()

            # # 检查连接状态
            if self._check_wallet_connected(tab):
                return True

            return False

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _wait_for_x(self, page, title, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == title:
                return True
            sleep(1)
        return False

    def _execute_task(self, task_name: str):
        """执行任务"""
        task_label = self.browser.page.latest_tab.ele(
            f"x://span[text()='{task_name}']", timeout=5
        )
        if not task_label:
            raise TaskExecutionError(f"{self.browser_id} 找不到{task_name}任务")
        task_label_parent = task_label.parent().parent()
        task_button_parent = task_label_parent.next()
        task_button = task_button_parent.child(2)
        if not task_button:
            raise TaskExecutionError(f"{self.browser_id} 找不到{task_name}任务按钮")
        if task_button.text == "Completed":
            return True
        else:
            task_button.click()
            return False

    def save_key_to_json(self, key):
        try:
            import os

            # 检查文件是否存在，如果不存在则创建包含空字典的文件
            json_file_path = "story/gaianet.json"
            if not os.path.exists(json_file_path):
                with open(json_file_path, "w") as f:
                    json.dump({}, f)

            # 读取现有数据
            try:
                with open(json_file_path, "r") as f:
                    content = f.read()
                    data = json.loads(content) if content.strip() else {}
            except json.JSONDecodeError:
                # 如果文件内容损坏，创建新的空字典
                data = {}

            # 更新数据
            data[self.browser_id] = key

            # 写入更新后的数据
            with open(json_file_path, "w") as f:
                json.dump(data, f, indent=4)  # 使用indent参数使JSON文件更易读

            return True

        except Exception as e:
            logger.error(f"保存key到JSON文件失败: {str(e)}")
            return False

    def connect_twitter(self, max_retries=3):
        if self._execute_task("Connect Twitter"):
            return True

        result = self.page.latest_tab.wait.url_change(
            "api.twitter.com/oauth", timeout=30
        )
        if not result:
            raise TaskExecutionError(f"{self.browser_id} 连接twitter失败")

        for attempt in range(max_retries):
            try:
                logger.info(f"{self.browser_id} 尝试第 {attempt + 1} 次连接 Twitter")

                # 点击twitter按钮
                self.page.latest_tab.ele("x://input[@id='allow']", timeout=10).click()

                # 等待x对话框消失
                if not self._wait_for_x(
                    self.page,
                    "Gaia | Living Knowledge Network, Decentralized AI",
                    timeout=30,
                ):
                    raise TaskExecutionError(f"{self.browser_id} x链接未完成")
                    sleep(3)

                logger.info(f"{self.browser_id} Twitter 连接成功")
                return True  # 成功时返回

            except Exception as e:
                if attempt < max_retries - 1:  # 如果还没到最后一次重试
                    logger.warning(
                        f"{self.browser_id} 第 {attempt + 1} 次连接 Twitter 失败: {e}"
                    )
                    logger.warning(f"等待 3 秒后重试...")
                    sleep(3)  # 在重试之前等待一段时间
                    continue
                else:  # 最后一次尝试也失败了
                    logger.error(
                        f"{self.browser_id} 连接 Twitter 最终失败 (尝试 {max_retries} 次): {e}"
                    )
                    logger.error(traceback.format_exc())
                    return False

    def connect_discord(self, max_retries=3):
        if self._execute_task("Connect Discord"):
            return True

        result = self.page.latest_tab.wait.url_change(
            "discord.com/oauth2/authorize", timeout=30
        )
        if not result:
            raise TaskExecutionError(f"{self.browser_id} 连接discord失败")

        for attempt in range(max_retries):
            try:
                logger.info(f"{self.browser_id} 尝试第 {attempt + 1} 次连接 Discord")

                auth_ele = self.browser.page.latest_tab.ele(
                    "x:(//button)[2]", timeout=30
                )
                if not auth_ele:
                    raise TaskExecutionError(f"{self.browser_id} 找不到discord按钮")

                auth_ele.wait.has_rect(timeout=30)
                auth_ele.click()

                result = self.page.latest_tab.wait.url_change(
                    "reward-summary", timeout=30
                )
                if not result:
                    raise TaskExecutionError(f"{self.browser_id} discord链接未完成")

                sleep(3)
                logger.info(f"{self.browser_id} Discord 连接成功")
                return True

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"{self.browser_id} 第 {attempt + 1} 次连接 Discord 失败: {e}"
                    )
                    logger.warning("等待 3 秒后重试...")
                    sleep(3)
                    continue
                else:
                    logger.error(
                        f"{self.browser_id} 连接 Discord 最终失败 (尝试 {max_retries} 次): {e}"
                    )
                    logger.error(traceback.format_exc())
                    return False

    def follow_twitter(self, max_retries=3):
        for attempt in range(max_retries):
            try:
                logger.info(
                    f"{self.browser_id} 尝试第 {attempt + 1} 次验证 Twitter 关注"
                )

                if not self._execute_task("Follow Gaia on Twitter"):
                    sleep(3)
                    self.browser.page.latest_tab.close()
                    sleep(3)
                    verify_button = self.browser.page.latest_tab.ele(
                        "x://button[text()='Verify']", timeout=30
                    )
                    verify_button.click()
                    sleep(3)

                logger.info(f"{self.browser_id} Twitter 关注验证成功")
                return True

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"{self.browser_id} 第 {attempt + 1} 次验证 Twitter 关注失败: {e}"
                    )
                    logger.warning("等待 10 秒后重试...")
                    sleep(10)
                    continue
                else:
                    logger.error(
                        f"{self.browser_id} 验证 Twitter 关注最终失败 (尝试 {max_retries} 次): {e}"
                    )
                    logger.error(traceback.format_exc())
                    return False

    def verify_discord(self, max_retries=3):
        for attempt in range(max_retries):
            try:
                logger.info(
                    f"{self.browser_id} 尝试第 {attempt + 1} 次验证 Discord 加入"
                )

                if not self._execute_task("Join Gaia Discord"):
                    sleep(3)
                    self.browser.page.latest_tab.close()
                    sleep(3)
                    verify_button = self.browser.page.latest_tab.ele(
                        "x://button[text()='Verify']", timeout=30
                    )
                    verify_button.click()
                    sleep(3)

                logger.info(f"{self.browser_id} Discord 加入验证成功")
                return True

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"{self.browser_id} 第 {attempt + 1} 次验证 Discord 加入失败: {e}"
                    )
                    logger.warning("等待 10 秒后重试...")
                    sleep(10)
                    continue
                else:
                    logger.error(
                        f"{self.browser_id} 验证 Discord 加入最终失败 (尝试 {max_retries} 次): {e}"
                    )
                    logger.error(traceback.format_exc())
                    return False

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Gaia任务"""
        if self._check_task_completed():
            return True

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url, new_tab=True)

            # 设置全屏
            try:
                self.browser.page.latest_tab.set.window.max()
            except Exception as e:
                pass

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 点击accept按钮
            if ele := self.page.latest_tab.ele("x://button[.='Accept']", timeout=3):
                ele.click()
                sleep(1)
                self._sign_okx_wallet()

            # 点击accept all按钮
            if ele := self.page.latest_tab.ele(
                "x://button[.='Accept All']", timeout=10
            ):
                ele.click()
                sleep(1)

            # 先点击gaiaPoints tab以便刷新tasks
            latest_tab = self.browser.page.latest_tab
            latest_tab.ele("x://span[text()='gaiaPoints']", timeout=8).click()
            sleep(2)
            latest_tab.ele("x://span[text()='Tasks']", timeout=8).click()

            # 点击twitter按钮
            if not self.connect_twitter():
                raise TaskExecutionError(f"{self.browser_id} 连接twitter失败")

            # 点击Discord按钮
            if not self.connect_discord():
                raise TaskExecutionError(f"{self.browser_id} 连接discord失败")

            # 点击关注推特
            if not self.follow_twitter():
                raise TaskExecutionError(f"{self.browser_id} 关注推特失败")

            # 点击加入discord
            if not self.verify_discord():
                raise TaskExecutionError(f"{self.browser_id} 加入discord失败")

            # 点击加入discord，由于已经加过dc，只是验证一下
            if not self._execute_task("Join Gaia Discord"):
                sleep(3)
                self.browser.page.latest_tab.close()
                sleep(3)
                self.browser.page.latest_tab.ele(
                    "x://button[text()='Verify']", timeout=30
                ).click()
                sleep(3)

            # 切换到chat页面，创建api key
            self.browser.open_url(self.api_url)
            create_api_button = self.browser.page.latest_tab.ele(
                "x://button[text()='create api key']", timeout=30
            )
            if not create_api_button:
                raise TaskExecutionError(f"{self.browser_id} 未找到创建api按钮")
            create_api_button.click()
            sleep(1)
            api_name = self.browser.page.latest_tab.ele(
                "x://input[@placeholder='Enter a name']", timeout=30
            )
            if not api_name:
                raise TaskExecutionError(f"{self.browser_id} 未找到api名称输入框")
            api_name.input("test")
            sleep(1)
            create_button = self.browser.page.latest_tab.ele(
                "x://button[text()='Create']", timeout=30
            )
            if not create_button:
                raise TaskExecutionError(f"{self.browser_id} 未找到Create按钮")
            create_button.click()
            sleep(1)
            created = self.browser.page.latest_tab.ele(
                "x://span[text()='Your new API key has been created !']", timeout=30
            )
            if not created:
                raise TaskExecutionError(f"{self.browser_id} 创建api key失败")
            logger.info(f"{self.browser_id} 创建api key成功")
            key = self.browser.page.latest_tab.ele(
                "x://span[contains(text(),'gaia-')]", timeout=30
            )
            if not key:
                raise TaskExecutionError(f"{self.browser_id} 获取api key失败")
            logger.success(f"{self.browser_id} api key: {key.text}")
            self.save_key_to_json(key.text)

            logger.success(f"{self.browser_id} Gaia任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            # 输出traceback
            logger.error(traceback.format_exc())
            # raise
        except Exception as e:
            logger.error(f"{self.browser_id} Gaia任务失败: {e}")
            # 输出traceback
            logger.error(traceback.format_exc())
        # finally:
        #     self.browser.page.latest_tab.close()
