import re

from loguru import logger

from src.emails.imap4.first_client import First<PERSON>lient, SearchCriteria

email_client = FirstClient("<EMAIL>", "zvmhxujzY4946")
email_client._login()
search_criteria = SearchCriteria(
    subject="confirm your email address",
    # from_addr="x.com",
    # to=proxy_email or x_email,
    # is_read=False,
    sort_order="DESC",
)
emails = email_client.search_emails_with_retry(search_criteria)
if not emails:
    logger.error(f" 未找到验证码邮件")


email = emails[0]
pattern = r"verification code to continue using X:\s*(\d+)"
match = re.search(pattern, email["content"])

if match:
    confirmation_code = match.group(1)
    logger.success(f"成功提取验证码: {confirmation_code}")

    logger.warning("未能从主题中提取验证码")
