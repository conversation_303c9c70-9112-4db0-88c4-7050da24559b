from time import sleep

from loguru import logger
from retry import retry

from src.browsers.base_browser import BaseBrowser
from src.browsers.operations import try_click
from src.enums.wallet_enums import GasLevel

from .base_wallet import BaseWallet, WalletType


def check_okx_page(func):
    """检查是否为 OKX 钱包页面的装饰器."""

    def wrapper(self, *args, **kwargs):
        for _ in range(15):
            tab = self.page.latest_tab
            if "OKX Wallet" in tab.title:
                return func(self, *args, **kwargs)
            sleep(1)
        logger.warning(f"{self.id} 未唤起钱包页面...")
        return False

    return wrapper


class OKXWallet(BaseWallet):
    CHAIN_CONFIG = {
        "btc": {"coin_id": "22900", "name": "BTC"},
        "evm": {"coin_id": "3", "name": "EVM"},
        "solana": {"coin_id": "1800", "name": "Solana"},
    }

    ADDRESS_SELECTOR = "x://span[@class='new-coin-detail-address-content-black']"

    def __init__(self, browser: BaseBrowser):
        super().__init__(browser=browser)
        self.import_url = f"{self.home_url}#/import-with-seed-phrase-and-private-key"

    @property
    def wallet_type(self) -> WalletType:
        return WalletType.OKX

    @retry(tries=3, delay=1)
    def login(self, password: str) -> bool:
        """
        登录 OKX 钱包.

        Args:
            password: 钱包密码

        Returns
        -------
            bool: 登录是否成功
        """
        if not password:
            logger.error(f"{self.id} 未提供有效的钱包密码")
            return False
        tab = None
        try:
            # 打开钱包页面并关闭其他标签
            tab = self.page.new_tab(self.home_url)
            sleep(3)

            # 如果发现钱包头像，则已登录
            avatar_ele = self._get_wallet_avatar(tab, 1)
            if avatar_ele and avatar_ele.states.has_rect:
                logger.success(f"{self.id} OKX 钱包已登录")
                return True

            # 验证登录状态
            for _ in range(3):
                # 如果发现密码输入框，则需要登录
                if password_input := tab.ele("x://input[@data-testid='okd-input']", timeout=3):
                    password_input.clear(True)
                    sleep(1)
                    password_input.input(password)
                    if not (submit_btn := tab.ele("x://button[@type='submit']", timeout=3)):
                        raise Exception(f"{self.id} 未找到登录按钮")
                    submit_btn.click()

                    # 等待密码输入框消失
                    tab.wait.ele_deleted("x://input[@type='password']", timeout=5)
                    avatar_ele = self._get_wallet_avatar(tab, 5)
                    if avatar_ele:
                        logger.success(f"{self.id} OKX 钱包登录成功")
                        return True

                # 如果发现取消按钮，则点击取消
                for _ in range(30):
                    if cancel_btn := tab.ele(
                        "x://button[@data-testid='okd-button' and contains(@class, 'okui-btn btn-lg"
                        " btn-outline-primary')]",
                        timeout=3,
                    ):
                        logger.info(f"{self.id} 发现取消按钮，开始点击取消...")
                        cancel_btn.click()
                        sleep(1)
                    else:
                        break

                sleep(1)
                avatar_ele = self._get_wallet_avatar(tab, 5)
                if avatar_ele:
                    logger.success(f"{self.id} OKX 钱包登录成功")
                    return True

            raise Exception(f"{self.id} 登录验证失败")

        except Exception as e:
            logger.error(f"{self.id} OKX钱包登录失败: {str(e)}")
            return False
        finally:
            if tab:
                tab.close()

    def _get_wallet_avatar(self, tab, timeout=1):
        return tab.ele("x://img[@alt='wallet-avatar']", timeout=timeout) or tab.ele(
            "x://div[@data-testid='home-page-wallet-avatar']", timeout=timeout
        )

    def reset(self) -> bool:
        """重置OKX钱包."""
        url = f"chrome-extension://{self.extension_id}/popup.html#/forget-password"
        latest_tab = self.page.new_tab(url)
        sleep(3)

        if "initialize" in latest_tab.url:
            logger.success(f"{self.id} OKX钱包没有导入过助记词")
            return True

        inputs = latest_tab.eles("x://input[@type='checkbox']")
        for input in inputs:
            input.click()
            sleep(0.5)

        # 点击Reset按钮
        try_click(latest_tab, "x://button", id=self.id)
        sleep(1)

        reset_input = latest_tab.ele(
            "x://div[@data-testid='okd-dialog-scroll-box']//input[contains(@placeholder, 'RESET')]"
        )
        reset_input.input("RESET")
        sleep(1)
        latest_tab.ele("x://div[@data-testid='okd-dialog-scroll-box']//button[.='Reset']").click()

        ele = latest_tab.ele("Your portal to Web3")
        if ele:
            logger.success(f"{self.id} OKX钱包重置成功")
            return True
        else:
            logger.error(f"{self.id} OKX钱包重置失败")
            return False

    def setup(
        self,
        password: str,
        phrase_key: str,
        is_key: bool = False,
        wallet_name: str | None = None,
    ) -> bool:
        latest_tab = self.page.new_tab(self.home_url)
        sleep(3)
        latest_tab.close(others=True)

        # 判断是否已登录
        ele = latest_tab.ele("x://input[@type='password']", timeout=3)
        if ele:
            # 登录
            ele.input(password)
            latest_tab.ele("x://button[@type='submit']").click()
            logger.success(f"{self.id} 已经导入过助记词并且已经登录...")
            return True

        # 判断是否已导入助记词
        if latest_tab.url.endswith("home.html#/"):
            logger.success(f"{self.id} 已经导入过助记词并且已经登录...")
            return True

        # 导入助记词
        if is_key:
            return self._import_account_from_key(latest_tab, phrase_key, password, wallet_name)
        else:
            return self._import_account(latest_tab, phrase_key, password)

    @property
    def home_url(self) -> str:
        return f"chrome-extension://{self.extension_id}/home.html"

    def _import_account_from_key(self, latest_tab, key, password, wallet_name: str | None = None) -> bool:
        """
        首次导入私钥.

        Args:
            last_tab: 浏览器标签页对象
            key: 私钥字符串
            password: 钱包密码

        Returns
        -------
            bool: 导入是否成功
        """
        separator = "=" * 20
        logger.info(f"{separator} Wallet #{self.id} 开始导入私钥 {separator}")

        try:
            # 导航到导入页面
            latest_tab.get(self.import_url)
            sleep(2)
            key_tab = latest_tab.ele("x://div[@data-e2e-okd-tabs-pane='2']")
            key_tab.click()

            # 输入私钥
            textarea = latest_tab.ele("x://textarea[@type='password']")
            textarea.input(key)
            sleep(0.2)  # 减少等待时间

            # 点击确认
            try:
                confirm_btn = latest_tab.ele("x://button[@data-e2e-okd-button-loading='false']", timeout=10)
                latest_tab.actions.move_to(ele_or_loc=confirm_btn)
                confirm_btn.wait.not_covered(timeout=10)
                confirm_btn.click()
                sleep(1)
                logger.info(f"{self.id} 点击确认按钮")
            except Exception as e:
                logger.error(f"{self.id} 未找到确认按钮 {str(e)}")

            # 默认EVM网络，点击Confirm
            try:
                confirm_btn = latest_tab.ele("x://button[@data-testid='okd-button']", timeout=5)
                latest_tab.actions.move_to(ele_or_loc=confirm_btn)
                confirm_btn.wait.not_covered(timeout=10)
                confirm_btn.click()
                sleep(1)
            except Exception as e:
                logger.error(f"{self.id} 未找到网络确认按钮 {str(e)}")

            # 新版本需要选择是否开启安全保护
            try:
                next_btn = latest_tab.ele("x://button[@data-testid='okd-button']", timeout=5)
                # 选择祖父节点
                next_btn_grandparent = next_btn.parent().parent()
                # 祖父节点往回查找第二个兄弟节点
                type_btn = next_btn_grandparent.prev(2)
                # 点击type_btn的第二个div子节点
                logger.info(f"{self.id} 选择开启安全保护")
                type_btn.child(2).click()
                sleep(0.1)
                # 点击Next按钮
                next_btn.click()
                sleep(1)
            except Exception as e:
                logger.error(f"{self.id} 未找到安全保护 {str(e)}")

            # 输入密码
            password_inputs = latest_tab.eles("x://input[@data-testid='okd-input']", timeout=3)
            for pwd_input in password_inputs:
                pwd_input.input(password)
                sleep(1)
            # 点击Confirm按钮
            confirm_btn = latest_tab.ele("x://button[@data-testid='okd-button']", timeout=5)
            latest_tab.actions.move_to(ele_or_loc=confirm_btn)
            confirm_btn.wait.not_covered(timeout=10)
            confirm_btn.click()
            sleep(2)

            # # 去掉默认钱包选项
            # try:
            #     latest_tab.ele("x://input[@class='okui-checkbox-input']").click()
            # except Exception:
            #     logger.error(f"{self.id} 未找到默认钱包checkbox")

            # 点击Start your Web3 journey
            sleep(1)
            if not try_click(
                latest_tab,
                "x://button[@data-testid='onboarding-success-page-confirm-button']",
                id=self.id,
            ):
                raise Exception(f"{self.id} Start your Web3 journey按钮点击失败")
            sleep(1)
            # 验证导入结果
            if latest_tab.ele("x://div[contains(text(),'...')]", timeout=5):
                logger.success(f"{self.id} 导入私钥成功")
            else:
                raise Exception(f"{self.id} 未找到钱包标识")
            # 修改钱包名称
            # if wallet_name:
            #     self.change_wallet_name(wallet_name)
            return True

        except Exception as e:
            logger.error(f"{self.id} 导入私钥钱包失败: {str(e)}")
            return False

    def _import_account(self, latest_tab, phrase, password):
        """
        首次导入助记词.

        Args:
            last_tab: 浏览器标签页对象
            phrase: 助记词字符串
            password: 钱包密码

        Returns
        -------
            bool: 导入是否成功
        """
        separator = "=" * 20
        logger.info(f"{separator} Wallet #{self.id} 开始导入助记词 {separator}")

        try:
            # 导航到导入页面
            latest_tab.get(self.import_url)
            words = phrase.split()

            # 如果是24个长度助记词，切换成24位模式
            if len(words) == 24:
                latest_tab.ele(".okui-select select-text").click()
                word_options = latest_tab.eles(
                    "x://div[@class='okui-select-item mnemonic-words-inputs__length-type-item okui-dropdown-option']"
                )
                word_options[-1].click()  # 选择最后一个选项(24位)

            # 输入助记词
            inputs = latest_tab.eles("x://input[@class='mnemonic-words-inputs__container__input']")
            for word, input_field in zip(words, inputs):
                input_field.input(word)
                sleep(0.1)  # 减少等待时间

            # 点击确认
            try:
                logger.info(f"{self.id} 点击通知按钮")
                latest_tab.ele("x://div[@class='notification']", timeout=3).click()
            except Exception:
                logger.error(f"{self.id} 未找到通知按钮")

            try:
                logger.info(f"{self.id} 点击确认按钮")
                submit_btn = latest_tab.ele("x://button[@type='submit']", timeout=3)
                if submit_btn:
                    submit_btn.click()
                    latest_tab.wait.ele_deleted("x://button[@type='submit']", timeout=10)
            except Exception:
                logger.error(f"{self.id} 未找到确认按钮")

            # Import address
            try:
                ele = latest_tab.ele("Import address", timeout=2)
                if ele:
                    sleep(2)
                    logger.info(f"{self.id} 选择第一个地址")
                    latest_tab.ele("x://div[contains(@class,'_switchItem_1')]").click()
            except Exception:
                logger.error(f"{self.id} 未找到导入地址")

            # 新版本需要选择是否开启安全保护
            try:
                ele = latest_tab.ele("x://div[contains(@class,'_item_1')][2]", timeout=2)
                if ele:
                    sleep(2)
                logger.info(f"{self.id} 选择开启安全保护")
                latest_tab.ele("x://div[contains(@class,'_item_1')][2]", timeout=2).click()
            except Exception:
                logger.error(f"{self.id} 未找到安全保护")

            if not try_click(latest_tab, "x://button[@data-testid='okd-button']", id=self.id):
                raise Exception(f"{self.id} 确认按钮点击失败")

            # 输入密码
            password_inputs = latest_tab.eles("x://input[@data-testid='okd-input']", timeout=3)
            for pwd_input in password_inputs:
                pwd_input.input(password)
                sleep(0.2)

            # 提交并确认
            sleep(3)
            if not try_click(latest_tab, "x://button[@type='button']", id=self.id):
                raise Exception(f"{self.id} 提交按钮点击失败")
            sleep(3)

            # try:
            #     if not try_click(latest_tab, "x://button[@data-testid='okd-button']", id=self.id):
            #         logger.error(f"{self.id} 确认按钮点击失败, xpath=x://button[@data-testid='okd-button']")
            #         return False
            # except Exception as e:
            #     logger.error(f"{self.id} 确认按钮点击失败, xpath=x://button[@data-testid='okd-button'], error={str(e)}")

            try:
                if not try_click(
                    latest_tab, "x://button[@data-testid='onboarding-success-page-confirm-button']", id=self.id
                ):
                    logger.error(
                        f"{self.id} 确认按钮点击失败,"
                        " xpath=x://button[@data-testid='onboarding-success-page-confirm-button']"
                    )
                    return False
            except Exception as e:
                logger.error(f"{self.id} 确认按钮点击失败: {str(e)}")
            sleep(3)

            # 验证导入结果
            if latest_tab.ele("x://div[contains(@class, '_avatar')]", timeout=10):
                logger.success(f"{self.id} 导入助记词成功")
                return True

            raise Exception(f"{self.id} 未找到钱包标识")

        except Exception as e:
            logger.error(f"{self.id} 导入助记词失败: {str(e)}")
            return False

    def import_new_account(self, phrase, password):
        logger.info(f" ========== {self.id} 开始导入助记词 ========== ")

        last_tab = self.page.new_tab(f"{self.home_url}#/wallet-add/import-with-seed-phrase-and-private-key")

        w = phrase.split(" ")
        word_len = len(w)

        # 如果是24个长度助记词，切换成24
        if word_len == 24:
            last_tab.ele(".okui-select select-text").click()
            eles = last_tab.eles(
                "x://div[@class='okui-select-item mnemonic-words-inputs__length-type-item okui-dropdown-option']"
            )
            eles[len(eles) - 1].click()

        inputs = last_tab.eles("x://input[@class='mnemonic-words-inputs__container__input']")
        w = phrase.split(" ")

        for index, e in enumerate(inputs):
            item = w[index]
            e.input(item)
            sleep(0.2)

        sleep(0.5)
        last_tab.ele("x://div[@class='notification']").click()

        sleep(5)
        last_tab.ele("x://button[@type='submit']").click()
        sleep(1)
        try:
            last_tab.ele("x://button[@data-testid='okd-button']", timeout=5).click()
        except Exception as e:
            pass

        ele = last_tab.ele("x://div[contains(@class, '_avatar')]")
        if ele:
            logger.success(f"{self.id} 导入助记词成功...")
        else:
            logger.error(f"{self.id} 导入助记词失败...")

    def add_token(self, token):
        latest_tab = self.page.latest_tab
        latest_tab.get(self.url + "#manage-coin")
        latest_tab.ele("x://input[@class='okui-input-input']").input(token)
        latest_tab.ele(f"//div[text()='{token}']/parent::*/parent::*/following-sibling::div").click()

    def _get_chain_address(self, chain_type):
        """
        通用的获取链地址方法

        Args:
            chain_type: 链类型 ('btc'/'evm'/'solana')

        Returns
        -------
            str: 钱包地址，失败返回 None
        """
        chain_info = self.CHAIN_CONFIG.get(chain_type)
        if not chain_info:
            logger.error(f"{self.id} 不支持的链类型: {chain_type}")
            return None

        new_tab = None
        try:
            # 打开新标签页
            new_tab = self.page.new_tab(f"{self.home_url}#coin?coinId={chain_info['coin_id']}")
            sleep(3)

            # 获取地址
            addr = new_tab.ele(self.ADDRESS_SELECTOR).text
            if not addr:
                raise ValueError("地址为空")

            logger.success(f"{self.id} 获取{chain_info['name']}地址成功: {addr}")
            return addr

        except Exception as e:
            logger.error(f"{self.id} 获取{chain_info['name']}地址时出现异常: {str(e)}")
            return None

        finally:
            # 确保标签页被关闭
            if new_tab:
                new_tab.close()

    def get_btc_addr(self):
        """获取 BTC 地址."""
        return self._get_chain_address("btc")

    def get_evm_addr(self):
        """获取 EVM 地址."""
        return self._get_chain_address("evm")

    def get_solana_addr(self):
        """获取 Solana 地址."""
        return self._get_chain_address("solana")

    # 钱包连接
    @check_okx_page
    def connect(self, password: str | None = None):
        logger.info(f"{self.id} 准备开始连接okx钱包...")
        try:
            latest_tab = self.page.latest_tab
            sleep(3)

            pwd_ele = latest_tab.ele("x://input[@type='password']", timeout=3)
            if pwd_ele:
                logger.info(f"{self.id} 需要重新输入密码")
                pwd_ele.input(password)
                latest_tab.ele("x://button[@type='submit']", timeout=3).click()
                if latest_tab.wait.ele_deleted("x://input[@type='password']", timeout=5):
                    logger.success(f"{self.id} OKX 钱包重新登录成功")

            latest_tab.ele("x://button[2]").click()
            sleep(5)
            latest_tab = self.page.get_tab(0)
            if latest_tab.title != "OKX Wallet":
                return True

            return try_click(latest_tab, xpath="x://button[2]", id=self.id)

        except Exception as e:
            logger.error(e)
            return False

    def _select_gas_level(self, gas_level: GasLevel, tab):
        # 选择gas等级
        try:
            dialog_close_ele = tab.ele("x://i[@id='okdDialogCloseBtn']", timeout=5)
            if dialog_close_ele:
                dialog_close_ele.click()

            gas_level_ele = tab.ele(f"x://div[contains(text(), '{gas_level.value}')]", timeout=5)
            if gas_level_ele:
                return True

            # 如果没有找到gas等级，则点击选择gas等级
            tab.ele("x://div[contains(@class,'_networkFee__wrap')]/div").click()

            """选择gas等级"""
            scroll_box = tab.ele("x://div[@id='scroll-box']", timeout=5)
            if not scroll_box:
                raise Exception(f"{self.id} 未找到gas等级选择框")

            item = scroll_box.ele(
                f"x://div[text()='{gas_level.value}']/ancestor::div[@class='network-fee-item']",
                timeout=5,
            )
            if not item:
                raise Exception(f"{self.id} 未找到{gas_level.value}选项")
            item.click()

            # 关闭弹窗
            dialog_close_ele = tab.ele("x://i[@id='okdDialogCloseBtn']", timeout=3)
            if dialog_close_ele:
                dialog_close_ele.click()

            gas_level_ele = tab.ele(f"x://div[contains(text(), '{gas_level.value}')]", timeout=5)
            if not gas_level_ele:
                logger.error(f"{self.id} 选择gas等级失败")
                return False

            logger.success(f"{self.id} 选择gas等级成功")
            return True
        except Exception as e:
            logger.error(f"{self.id} 选择gas等级失败: {str(e)}")
            return False

    @check_okx_page
    def sign(self):
        try:
            lasted_tab = self.page.latest_tab
            risk_ele = lasted_tab.ele("x://div[contains(text(),'Continue on this network')]", timeout=3)
            if risk_ele:
                try_click(lasted_tab, xpath="x://div[contains(text(),'Continue on this network')]", id=self.id)
            result = try_click(lasted_tab, xpath="x://div[contains(@class, 'action-buttons')]//button[2]", id=self.id)
            sleep(2)
            if not result:
                logger.error(f"{self.id} 签名按钮未找到")
                return False

            tabs = self.page.get_tabs(title="OKX Wallet")
            if len(tabs) == 0:
                return True

            lasted_tab.wait.ele_deleted("x://div[contains(@class, 'action-buttons')]//button[2]", timeout=20)
            sleep(5)
            tabs = self.page.get_tabs(title="OKX Wallet")
            if len(tabs) == 0:
                return True

            logger.warning(f"{self.id} 签名按钮点完还在钱包页面，开始点击取消按钮")

            # 否则点击取消
            try_click(lasted_tab, xpath="x://div[contains(@class, 'action-buttons')]//button[1]", id=self.id)
            return False

        except Exception as e:
            logger.error(f"{self.id} 出现异常 {e}")
            return False

    @check_okx_page
    def cancel_sign(self):
        try:
            lasted_tab = self.page.latest_tab
            # 如果可点击则直接点击，不可点击会循环检测，15秒后如果还不可点击则超时退出
            if try_click(lasted_tab, xpath="x://div[contains(@class, 'action-buttons')]//button[1]", id=self.id):
                # 如果3s后发现okx钱包页面还在，则点击取消按钮
                sleep(3)
                tabs = self.page.get_tabs(title="OKX Wallet")
                if len(tabs) == 0:
                    return True

                # 否则点击取消
                try_click(lasted_tab, xpath="x://div[contains(@class, 'action-buttons')]//button[1]", id=self.id)
                return False

            return False

        except Exception as e:
            logger.error(f"{self.id} 出现异常 {e}")
            return False

    @check_okx_page
    def approve(self, gas_level: GasLevel = GasLevel.AVERAGE):
        try:
            lasted_tab = self.page.latest_tab
            self._select_gas_level(gas_level, lasted_tab)
            # 如果可点击则直接点击，不可点击会循环检测，15秒后如果还不可点击则超时退出
            if try_click(lasted_tab, xpath="x://button[2]", id=self.id):
                lasted_tab.wait.ele_deleted("x://div[@data-testid='okd-loader-circle']", timeout=20)
                try_click(lasted_tab, xpath="x://button[2]", id=self.id)

                lasted_tab.wait.ele_deleted("x://div[@data-testid='okd-loader-circle']", timeout=20)
                tabs = self.page.get_tabs(title="OKX Wallet")
                if len(tabs) == 0:
                    return True

                # 否则点击取消
                try_click(lasted_tab, xpath="x://button[1]", id=self.id)
                return False

            return False

        except Exception as e:
            logger.error(f"{self.id} 出现异常 {e}")
            return False

    def connect_and_sign(self, element=None, timeout=5):
        """连接并签名."""
        try:
            if element:
                element.click()

            self.page.wait.new_tab(timeout=timeout)
            tab = self.page.latest_tab

            if tab.title != "OKX Wallet":
                logger.warning(f"{self.id} 新标签页不是OKX钱包,标题为: {tab.title}")
                return False

            return try_click(tab, "x://button[2]", id=self.id)

        except Exception as e:
            logger.error(f"{self.id} 钱包连接异常: {str(e)}")
            return False

    def _check_site_connected(self, tab, site: str) -> bool:
        """检查是否已连接到指定站点."""
        try:
            if site_ele := tab.ele(f"x://div[contains(text(),'{site}')]", timeout=5):
                logger.success(f"{self.id} 已连接到{site}")
                return True
            return False
        except Exception:
            return False

    def _switch_to_site(self, tab, site: str) -> bool:
        """执行站点切换操作."""
        try:
            # 点击切换网络按钮
            if not tab.ele("x://div[contains(@class,'_switchNetwork')]").click():
                raise Exception("点击切换网络按钮失败")

            # 输入站点名称
            input_ele = tab.ele("x://input[@data-testid='okd-input']", timeout=5)
            if not input_ele:
                raise Exception("未找到站点输入框")
            input_ele.input(site)

            # 选择目标站点
            popup_selector = f"x://div[contains(@class, '_networkItem')]/div[contains(text(), '{site}')]"
            popup_ele = tab.ele(popup_selector, timeout=5)
            if not popup_ele:
                raise Exception("未找到目标站点选项")
            popup_ele.click()

            # 验证切换结果
            if site_ele := tab.ele(f"x://div[contains(text(),'{site}')]", timeout=5):
                logger.success(f"{self.id} 切换站点成功")
                return True

            raise Exception("站点切换验证失败")

        except Exception as e:
            logger.error(f"{self.id} 切换站点操作失败: {str(e)}")
            return False

    def disconnect_site_all(self) -> bool:
        """断开所有站点连接."""
        tab = self.page.new_tab(f"{self.home_url}#/connect-site")
        try:
            button_ele = tab.ele("x://button[@data-testid='okd-button']")
            button_ele.click()
            sleep(2)

            if tab.wait.ele_deleted("x://button[@data-testid='okd-button']", timeout=5):
                logger.success(f"{self.id} 断开所有站点连接成功")
                return True

            logger.error(f"{self.id} 断开所有站点连接失败")
            return False

        except Exception as e:
            logger.error(f"{self.id} 断开所有站点连接失败: {str(e)}")
            return False
        finally:
            if tab:
                tab.close()

    def change_connect_site(self, site: str) -> bool:
        """
        切换连接站点.

        Args:
            site: 目标站点名称

        Returns
        -------
            bool: 切换是否成功
        """
        last_tab = self.page.new_tab(f"{self.home_url}#/connect-site")
        try:
            # 检查是否已连接到目标站点
            if self._check_site_connected(last_tab, site):
                return True

            # 执行站点切换
            return self._switch_to_site(last_tab, site)

        except Exception as e:
            logger.error(f"{self.id} 切换站点失败: {str(e)}")
            return False
        finally:
            last_tab.close()

    def remove_custom_rpc(self, network_name: str):
        """移除自定义RPC."""
        last_tab = self.page.new_tab(f"{self.home_url}#/network-management?tab=2")
        try:
            xpath = (
                "x://div[contains(@class, '_typography-text') and"
                f" contains(text(),'{network_name}')]/ancestor::div[contains(@class,"
                " '_wallet-list__item')]//div[@class='okui-select select-text']"
            )
            more_ele = last_tab.ele(xpath, timeout=10)
            if not more_ele:
                logger.info(f"{self.id} 未找到自定义RPC")
                return True
                # raise Exception(f"{self.id} 未找到自定义RPC")

            more_ele.click()
            last_tab.ele("x://div[@data-e2e-okd-select-option-value='delete']", timeout=5).click()
            last_tab.ele("x://button[@data-testid='okd-dialog-confirm-btn']", timeout=5).click()

            sleep(5)

            ele = last_tab.ele(
                f"x://div[contains(@class, '_typography-text') and contains(text(),'{network_name}')]",
                timeout=3,
            )
            if not ele:
                logger.success(f"{self.id} 移除自定义RPC成功")
                return True

            raise Exception(f"{self.id} 移除自定义RPC失败")
        except Exception as e:
            logger.error(f"{self.id} 移除自定义RPC失败: {str(e)}")
            return False
        finally:
            last_tab.close()

    def change_network(self, target_network: str):
        """切换网络."""
        last_tab = self.page.new_tab(f"{self.home_url}#/network-management?tab=0")
        try:
            input_ele = last_tab.ele("x://input[@data-testid='okd-input']", timeout=5)
            if not input_ele:
                raise Exception(f"{self.id} 未找到网络输入框")
            input_ele.input(target_network)
            sleep(3)

            # Story Odyssey Testnet
            popup_ele = last_tab.ele(
                f"x://div[contains(@class, '_typography-text') and contains(text(), '{target_network}')]",
                timeout=5,
            )
            if not popup_ele:
                raise Exception(f"{self.id} 未找到目标网络")
            popup_ele.click()
            return True
        except Exception as e:
            logger.error(f"{self.id} 切换网络失败: {str(e)}")
            return False

    # 暂时无法切换语言
    def change_language(self, language: str):
        """切换语言."""
        try:
            self.page.latest_tab.get(f"{self.home_url}#/list-select-panel?pageType=Lang")
        except Exception as e:
            logger.error(f"{self.id} 切换语言失败: {str(e)}")
            return False

    def change_wallet_name(self, new_name: str):
        """修改钱包名称."""
        try:
            latest_tab = self.page.new_tab(f"{self.home_url}#/new-settings")
            # 第5个okd-popup就是钱包管理
            latest_tab.eles("x://div[@data-testid='okd-popup']")[4].click()

            latest_tab.ele("x://button[@data-testid='wallet-management-page-edit-wallet-button']").click()

            # 点击钱包地址
            latest_tab.ele("x://div[contains(text(),'...')]").click()
            sleep(0.1)
            # 输入钱包名称，这里input需要先清空
            latest_tab.ele("x://i[@data-testid='okd-input-clear-icon']").click()
            name_input = latest_tab.ele("x://input[@data-testid='okd-input']")
            sleep(1)
            name_input.input(new_name)
            # 点击Confirm按钮, 第3个okd-button
            latest_tab.eles("x://button[@data-testid='okd-button']")[2].click()
            sleep(1)

            ele = latest_tab.ele(
                f"x://div[contains(@class, '_name')]//div[contains(@class, '_typography-text') and .='{new_name}']"
            )
            if ele:
                logger.success(f"{self.id} 修改钱包名称成功: {new_name}")
                return True
            else:
                logger.error(f"{self.id} 修改钱包名称失败: 未找到新名称")
                return False

        except Exception as e:
            logger.error(f"{self.id} 修改钱包名称失败: {str(e)}")
            return False
