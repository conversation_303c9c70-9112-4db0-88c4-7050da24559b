import requests
from typing import Dict, Optional
from datetime import datetime
from loguru import logger
from dataclasses import dataclass


@dataclass
class GasPrice:
    slow: float
    average: float
    fast: float
    updated_at: datetime


class GasTracker:
    """Gas价格追踪器"""

    def __init__(self):
        self.api_url = "https://odyssey.storyscan.xyz/api/v2/stats"

    def get_gas_prices(self) -> Optional[GasPrice]:
        """获取当前gas价格"""
        try:
            response = requests.get(self.api_url)
            if response.status_code != 200:
                logger.error(f"获取gas价格失败: HTTP {response.status_code}")
                return None

            data = response.json()
            gas_prices = data.get("gas_prices")
            if not gas_prices:
                logger.error("返回数据中没有gas价格信息")
                return None

            updated_at = datetime.fromisoformat(
                data.get("gas_price_updated_at").replace("Z", "+00:00")
            )

            return GasPrice(
                slow=gas_prices["slow"],
                average=gas_prices["average"],
                fast=gas_prices["fast"],
                updated_at=updated_at,
            )

        except Exception as e:
            logger.error(f"获取gas价格异常: {str(e)}")
            return None

    def is_gas_price_reasonable(self, threshold: float = 1000) -> bool:
        """
        检查gas价格是否合理
        :param threshold: gas价格阈值，默认1000
        :return: 如果average gas价格低于阈值返回True
        """
        gas_prices = self.get_gas_prices()
        if not gas_prices:
            return False

        return gas_prices.average < threshold
