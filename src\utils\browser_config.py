from loguru import logger
from typing import Optional
from src.models.browser_config import BrowserConfig
from ..browsers.base_browser import BrowserType
from src.repositories.browser_repository import BrowserRepository, CSVBrowserRepository
from config import PROXY_URL
from src.utils.proxies import Proxies
from src.utils.secure_encryption import SecureEncryption


class BrowserConfigInstance:
    """浏览器实例配置，主要用于后端方式的任务

    负责管理浏览器实例配置信息。

    Attributes:
        browser_type: 浏览器类型
        id: 配置唯一标识符
        browser_config: 浏览器配置信息
    """

    def __init__(
        self,
        browser_type: BrowserType,
        id: str,
        repository: Optional[BrowserRepository] = None,
    ):
        """初始化浏览器控制器

        Args:
            browser_type: 浏览器类型
            id: 配置ID
            repository: 浏览器配置仓储实例，如果为空则使用默认的CSV仓储

        Raises:
            ValueError: 浏览器类型无效时抛出
            BrowserControllerError: 配置获取失败时抛出
        """
        if not isinstance(browser_type, BrowserType):
            raise ValueError(f"无效的浏览器类型: {browser_type}")
        if not id:
            raise ValueError("配置ID不能为空")

        self.browser_type = browser_type
        self.browser_id = str(id)
        self._repository = repository or CSVBrowserRepository(browser_type)

        # 初始化配置和浏览器实例
        self.browser_config: Optional[BrowserConfig] = self._get_browser_config()

    def _get_browser_config(self) -> Optional[BrowserConfig]:
        """获取浏览器配置信息

        Returns:
            Optional[BrowserConfig]: 浏览器配置对象，获取失败时返回 None
        """
        try:
            config = self._repository.get_by_id(self.browser_id)
            if not config:
                logger.warning(f"ID {self.browser_id} 未找到对应的配置")
            return config
        except Exception as e:
            logger.error(f"获取配置失败: {str(e)}")
            return None

    def get_private_key(self) -> Optional[str]:
        pk = self.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def _verify_proxy(self, proxy):
        """验证代理有效性"""
        try:
            if not proxy:
                return False
            return Proxies(proxy).verify()
        except Exception as e:
            logger.error(f"{self.browser_id} 代理验证失败: {str(e)}")
            return False

    def _get_valid_proxy(self):
        """获取有效代理"""
        proxy = self.browser_config.proxy
        if self._verify_proxy(proxy):
            return proxy

        proxy = {"http": PROXY_URL}
        if self._verify_proxy(proxy):
            return proxy
