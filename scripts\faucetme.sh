#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（假设脚本在 scripts 目录下）
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 切换到项目目录
cd "${PROJECT_DIR}" || exit 1

# 激活虚拟环境（假设虚拟环境在项目根目录的 venv 文件夹中）
source venv/bin/activate || {
    echo "无法激活虚拟环境，请确保虚拟环境已正确创建"
    exit 1
}

# 添加错误处理
handle_error() {
    echo "发生错误，脚本退出"
    exit 1
}

# 设置错误处理
trap handle_error ERR

while true; do
    echo "开始执行 story.py..."
    if ! python3 story.py fdc -t ads -i 1-100; then
        echo "执行失败，等待下次重试..."
    fi
    echo "等待300秒后重试..."
    sleep 300
done
