from os import environ
from dotenv import load_dotenv

load_dotenv()
BRAVE_USER_DATA_PATH = environ.get("BRAVE_USER_DATA_PATH")
BRAVE_EXE_PATH = environ.get("BRAVE_EXE_PATH")
USER_DATA_PATH = environ.get("USER_DATA_PATH")
OKX_WALLET_PASSWORD = environ.get("OKX_WALLET_PASSWORD")
METAMASK_WALLET_PASSWORD = environ.get("METAMASK_WALLET_PASSWORD")
BACKPACK_WALLET_PASSWORD = environ.get("BACKPACK_WALLET_PASSWORD")
KEPLR_WALLET_PASSWORD = environ.get("KEPLR_WALLET_PASSWORD")
RAZOR_WALLET_PASSWORD = environ.get("RAZOR_WALLET_PASSWORD")
ICLOUD_EMAIL = environ.get("ICLOUD_EMAIL")
ICLOUD_EMAIL_PASSWORD = environ.get("ICLOUD_EMAIL_PASSWORD")
MAX_GAS_PRICE = int(environ.get("MAX_GAS_PRICE") or 1000)
PROXY_URL = environ.get("PROXY_URL")
DEFAULT_BROWSER_TYPE = environ.get("DEFAULT_BROWSER_TYPE") or "CHROME"
ETH_RPC_URL = environ.get("ETH_RPC_URL")
ALCHEMY_API_KEY = environ.get("ALCHEMY_API_KEY")
