from time import sleep

from loguru import logger
from .base_wallet import BaseWallet, WalletType
from src.browsers.base_browser import BaseBrowser
from src.browsers.operations import try_click
from src.utils import get_element, get_elements
from retry import retry


def check_backpack_page(func):
    """检查是否为 Backpack 钱包页面的装饰器"""

    def wrapper(self, *args, **kwargs):
        for _ in range(15):
            tab = self.page.latest_tab
            if "Backpack" in tab.title:
                return func(self, *args, **kwargs)
            sleep(1)
        logger.warning(f"{self.id} 未唤起钱包页面...")
        return False

    return wrapper


class BackpackWallet(BaseWallet):

    def __init__(self, browser: BaseBrowser):
        super().__init__(browser=browser)
        self.import_url = f"{self.home_url}/options.html?onboarding=true"

    @property
    def wallet_type(self) -> WalletType:
        return WalletType.BACKPACK

    @property
    def home_url(self) -> str:
        return f"chrome-extension://{self.extension_id}"

    @retry(tries=3, delay=1)
    def login(self, password: str) -> bool:
        last_tab = self.page.new_tab(f"{self.home_url}/popup.html")
        sleep(3)
        if get_element(last_tab, "x://button[.='Tokens']", timeout=3):
            logger.success(f"{self.id} 钱包已经登录")
            last_tab.close()
            return True

        pwd_input = get_element(last_tab, "x://input[@type='password']", timeout=3)
        if not pwd_input:
            raise Exception(f"{self.id} Backpack加载失败")

        pwd_input.input(f"{password}\n")
        sleep(2)

        if get_element(last_tab, "x://button[.='Tokens']", timeout=10):
            logger.success(f"{self.id} 钱包登录成功")
            last_tab.close()
            return True

        return False

    @retry(tries=3, delay=1)
    def setup(self, password: str, phrase: str) -> bool:
        last_tab = self.page.new_tab(self.import_url)
        if last_tab.ele("x://p[.='Already setup']", timeout=3):
            logger.success(f"{self.id} 钱包已经导入")
            return True

        tab = last_tab

        # 导入钱包
        try_click(tab, "x://button[.='Import Wallet']")
        sleep(1)

        # 选择导入助记词
        try_click(tab, "x://button[.='Eclipse']")
        sleep(1)

        # 导入助记词
        try_click(tab, "x://button[.='Import secret recovery phrase']")
        sleep(1)

        words = phrase.split()
        # 如果是24个长度助记词，切换成24位模式
        if len(words) == 24:
            try_click(tab, "x://span[text()='Use 24 words']/..")
            sleep(1)

        # 输入助记词
        inputs = tab.eles("x://input")
        for word, input_field in zip(words, inputs):
            input_field.input(word)
            sleep(0.1)

        # 导入助记词
        try_click(tab, "x://button[.='Import']")
        sleep(3)

        # 选择导入钱包类型
        try_click(tab, "x://button[.='Funded Addresses']")
        sleep(1)

        # 选择backpack
        try_click(tab, "x://div[.='Backpack']")
        sleep(3)

        # 选择第一个
        try_click(tab, "x:(//div[@role='checkbox'])[1]")
        sleep(1)

        # 导入钱包
        try_click(tab, "x://button[.='Import Wallet']")
        sleep(1)

        if not tab.wait.ele_displayed("x://span[.='Create a Password']", timeout=10):
            logger.error(f"{self.id} 导入钱包失败")
            return False

        inputs = get_elements(tab, "x://input[@type='password']")
        for input in inputs:
            input.input(password)
            sleep(0.5)

        try_click(
            tab,
            "x://input[@type='checkbox' and contains(@class, 'PrivateSwitchBase-input')]",
        )
        sleep(1)

        try_click(tab, "x://button[.='Next']")
        sleep(1)

        if tab.ele("You're all good!", timeout=10):
            logger.success(f"{self.id} 钱包导入成功")
            return True

        return False

    def sign(self):
        pass

    def approve(self):
        pass

    # 钱包连接
    @check_backpack_page
    def connect(self, password: str) -> bool:
        logger.info(f"{self.id} 准备开始连接backpack钱包...")
        try:
            latest_tab = self.page.latest_tab
            password_ele = latest_tab.ele("x://input[@type='password']", timeout=5)
            if password_ele:
                password_ele.input(password)
                sleep(1)
                try_click(latest_tab, "x://button[.='Unlock']")
                sleep(1)

            approve_ele = latest_tab.ele("x://div[.='Approve']", timeout=6)
            if approve_ele:
                try_click(latest_tab, "x://div[.='Approve']")
                sleep(1)

            if self.page.latest_tab.title != "Backpack":
                return True

            return False
        except Exception as e:
            logger.error(e)
            return False
