from time import sleep

from DrissionPage import Chromium
from loguru import logger

from src.browsers.base_browser import BaseBrowser
from src.enums.browsers_enums import BrowserType
from src.models.browser_config import BrowserConfig
from src.repositories.browser_repository import BrowserRepository, CSVBrowserRepository
from src.utils.secure_encryption import SecureEncryption

from .exceptions import BrowserControllerError
from .mixins import BrowserMixin, DiscordMixin, GmailMixin, TelegramMixin, WalletMixin, XMixin


class BrowserController(BrowserMixin, WalletMixin, XMixin, DiscordMixin, TelegramMixin, GmailMixin):
    """浏览器控制器.

    负责管理浏览器实例、配置信息和相关操作的核心控制器.
    继承了浏览器操作、钱包操作和社交媒体操作的混入类.

    Attributes
    ----------
        browser_type: 浏览器类型
        id: 配置唯一标识符
        browser_config: 浏览器配置信息
        browser: 浏览器实例
    """

    def __init__(
        self,
        browser_type: BrowserType,
        id: str,
        repository: BrowserRepository | None = None,
    ):
        """初始化浏览器控制器.

        Args:
            browser_type: 浏览器类型
            id: 配置ID
            repository: 浏览器配置仓储实例，如果为空则使用默认的CSV仓储

        Raises
        ------
            ValueError: 浏览器类型无效时抛出
            BrowserControllerError: 配置获取失败时抛出
        """
        if not isinstance(browser_type, BrowserType):
            raise ValueError(f"无效的浏览器类型: {browser_type}")  # noqa: TRY004
        if not id:
            raise ValueError("配置ID不能为空")

        self.browser_type = browser_type
        self.id = str(id)
        self._repository = repository or CSVBrowserRepository(browser_type)
        self._page: Chromium | None = None
        self.browser_config: BrowserConfig | None = self._get_browser_config()
        self.browser: BaseBrowser | None = self._get_browser()
        self.load_extensions()

    @property
    def page(self) -> Chromium | None:
        """获取当前页面实例.

        Returns
        -------
            Chromium | None: 当前页面实例
        """
        if self._page:
            return self._page

        if self.browser:
            self._page = self.browser.page
            return self._page

        return None

    def load_extensions(self):
        """加载扩展插件."""
        # if self.browser_type == BrowserType.CHROME:
        from src.browsers.config import get_extensions

        extensions = get_extensions(self.browser_type)
        if not extensions:
            return
        logger.info(f"加载扩展插件: {extensions}")
        for extension in extensions:
            self.add_extension(extension["path"], extension["id"])
            sleep(1)

    def _get_browser_config(self) -> BrowserConfig | None:
        """获取浏览器配置信息.

        Returns
        -------
            Optional[BrowserConfig]: 浏览器配置对象，获取失败时返回 None
        """
        try:
            config = self._repository.get_by_id(self.id)
            if not config:
                logger.warning(f"ID {self.id} 未找到对应的配置")
            return config
        except Exception as e:
            logger.error(f"获取配置失败: {str(e)}")
            return None

    def _get_browser(self) -> BaseBrowser | None:
        """获取浏览器实例.

        Returns
        -------
            Optional[BaseBrowser]: 浏览器实例，初始化失败时返回 None

        Raises
        ------
            ValueError: 不支持的浏览器类型
            BrowserControllerError: 配置无效或缺失必要信息
        """
        from src.browsers.browser_map import BROWSER_MAP

        browser_class = BROWSER_MAP.get(self.browser_type)
        if not browser_class:
            raise ValueError(f"不支持的浏览器类型: {self.browser_type}")

        # 获取并验证配置信息
        if not self.browser_config:
            self.browser_config = self._get_browser_config()
            if not self.browser_config:
                raise BrowserControllerError(f"ID {self.id} 浏览器配置未找到")

        browser_id = self.browser_config.browser_id
        if not browser_id:
            raise BrowserControllerError(f"ID {self.id} 浏览器ID未配置")

        try:
            browser_instance = browser_class(str(self.id), str(browser_id), self.browser_config.__dict__)
            return browser_instance
        except Exception as e:
            logger.error(f"初始化浏览器失败: {str(e)}")
            return None

    def encrypt_column_data(self, column_name: str, password: str) -> bool:
        """加密指定列的数据.

        Args:
            column_name: 需要加密的列名

        Returns
        -------
            bool: 是否加密成功
        """
        try:
            # 获取用户密码
            if not password:
                logger.error("密码不能为空")
                return False

            # 创建加密器实例
            crypto = SecureEncryption(password)

            # 保存盐值（用于之后解密）
            crypto.save_salt()

            # 获取所有配置
            configs = self._repository.get_all()
            if not configs:
                logger.warning("没有找到任何配置数据")
                return False

            # 加密处理
            for config in configs:
                # 检查列是否存在
                if not hasattr(config, column_name):
                    raise ValueError(f"列名 {column_name} 不存在")

                # 获取原始值并加密
                value = getattr(config, column_name)
                if value:
                    encrypted_value = crypto.encrypt_data(str(value))
                    setattr(config, column_name, encrypted_value)

            # 保存更新后的配置
            # self._repository.save(configs)
            # logger.success(f"列 {column_name} 加密完成")
            return True

        except Exception as e:
            logger.error(f"加密过程出错: {str(e)}")
            return False

    def decrypt_column_data(self, column_name: str) -> bool:
        """解密指定列的数据.

        Args:
            column_name: 需要解密的列名

        Returns
        -------
            bool: 是否解密成功
        """
        try:
            # 获取用户密码
            password = input("请输入解密密码: ")
            if not password:
                logger.error("密码不能为空")
                return False

            # 加载保存的盐值
            salt = SecureEncryption.load_salt()
            if not salt:
                logger.error("未找到加密盐值")
                return False

            # 创建解密器实例
            crypto = SecureEncryption(password, salt)

            # 获取所有配置
            configs = self._repository.get_all()
            if not configs:
                logger.warning("没有找到任何配置数据")
                return False

            # 解密处理
            for config in configs:
                # 检查列是否存在
                if not hasattr(config, column_name):
                    raise ValueError(f"列名 {column_name} 不存在")

                # 获取加密值并解密
                value = getattr(config, column_name)
                if value:
                    decrypted_value = crypto.decrypt_data(str(value))
                    setattr(config, column_name, decrypted_value)

            # 保存更新后的配置
            # self._repository.save(configs)
            logger.success(f"列 {column_name} 解密完成")
            return True

        except Exception as e:
            logger.error(f"解密过程出错: {str(e)}")
            return False
