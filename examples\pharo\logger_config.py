import contextvars
import logging
import os
import sys

from config_pharo import (
    LOG_COMPRESSION,
    LOG_FILE,
    LOG_LEVEL,
    LOG_RETENTION,
    LOG_ROTATION,
)
from loguru import logger

# 定义上下文变量存储 TaskID
current_task_id = contextvars.ContextVar("task_id", default="main")


# 自定义日志格式（安全处理特殊字符）
def format_log(record):
    # 获取任务ID（默认值兜底）
    task_id = record["extra"].get("task_id", "main")

    # 对消息内容进行转义，避免特殊字符被解析
    message = str(record["message"]).replace("{", "{{").replace("}", "}}")

    # 基础格式
    log_format = (
        f"<green>{{time:YYYY-MM-DD HH:mm:ss.SSS}}</green> | "
        f"<level>{{level: <8}}</level> | "
        f"{task_id} | "
        f"<cyan>{{name}}</cyan>:<cyan>{{function}}</cyan>:<cyan>{{line}}</cyan> | "
        f"<level>{message}</level>\n"
    )

    # 异常信息处理
    if record["exception"]:
        log_format += "{exception}\n"

    return log_format


# 拦截器：处理标准库日志
class InterceptHandler(logging.Handler):
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 获取消息并转义
        message = record.getMessage().replace("{", "{{").replace("}", "}}")

        # 处理异常堆栈
        exception = None
        if record.exc_info:
            import traceback

            exception = "".join(traceback.format_exception(*record.exc_info))

        # 获取当前任务的 task_id，优先从上下文获取
        task_id = current_task_id.get("main")  # 确保默认值
        if hasattr(record, "task_id"):
            task_id = record.task_id
        elif hasattr(record, "extra") and "task_id" in record.extra:
            task_id = record.extra["task_id"]
        else:
            # 尝试从上下文或其他机制推断 task_id
            task_id = f"【PID:{os.getpid()}】【{task_id}】"  # 模拟你的格式

        # 使用 opt 记录日志
        logger.opt(
            depth=6,
            exception=exception,
            raw=True,
        ).bind(task_id=task_id).log(level, message)


# 日志初始化配置（安全兜底）
def configure_logger():
    global logger
    logger.remove()

    # 强制绑定默认字段（防 KeyError）
    logger = logger.bind(locator="", task_id="main")

    # 拦截标准库日志
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    # 控制台输出配置
    logger.add(
        sys.stdout,
        enqueue=True,
        colorize=True,
        format=format_log,
        level="INFO",
        catch=True,
        backtrace=True,  # 启用回溯
        diagnose=True,  # 启用诊断
    )
    # 文件输出配置（自动轮转）
    logger.add(
        LOG_FILE,
        rotation=LOG_ROTATION,
        retention=LOG_RETENTION,
        compression=LOG_COMPRESSION,
        format=format_log,
        level="INFO",
        enqueue=True,
        catch=True,
        backtrace=True,  # 启用回溯
        diagnose=True,  # 启用诊断
    )


# 初始化日志
configure_logger()
