#!/usr/bin/env python3
"""
检查项目依赖是否正确安装的脚本
"""

import importlib
import sys
from typing import List, <PERSON><PERSON>


def check_imports() -> List[Tuple[str, bool, str]]:
    """检查关键依赖包是否可以正常导入"""

    # 关键依赖包列表
    key_packages = [
        # 核心依赖
        ("loguru", "loguru"),
        ("click", "click"),
        ("requests", "requests"),
        # Web3相关
        ("web3", "web3"),
        ("eth_account", "eth_account"),
        ("eth_utils", "eth_utils"),
        ("mnemonic", "mnemonic"),
        ("bip_utils", "bip_utils"),
        # 浏览器自动化
        ("DrissionPage", "DrissionPage"),
        ("pyautogui", "pyautogui"),
        ("pyperclip", "pyperclip"),
        # 数据处理
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("bs4", "bs4"),
        # 加密相关
        ("cryptography", "cryptography"),
        ("Crypto", "pycryptodome"),
        # 其他重要依赖
        ("faker", "Faker"),
        ("retry", "retry"),
        ("pyotp", "pyotp"),
    ]

    results = []

    for import_name, package_name in key_packages:
        try:
            importlib.import_module(import_name)
            results.append((package_name, True, "✓"))
        except ImportError as e:
            results.append((package_name, False, f"✗ {str(e)}"))

    return results


def main():
    """主函数"""
    print("检查项目依赖...")
    print("=" * 50)

    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    if python_version < (3, 12, 6):
        print("⚠️  警告: 建议使用Python 3.12.6或更高版本")
    else:
        print("✓ Python版本符合要求")

    print("-" * 50)

    results = check_imports()

    success_count = 0
    total_count = len(results)

    for package_name, success, message in results:
        status = "成功" if success else "失败"
        print(f"{package_name:20} | {status:4} | {message}")
        if success:
            success_count += 1

    print("=" * 50)
    print(f"检查完成: {success_count}/{total_count} 个包可以正常导入")

    if success_count == total_count:
        print("🎉 所有关键依赖都已正确安装！")
        print("\n下一步:")
        print("1. 复制 .env.copy 为 .env 并填写配置")
        print("2. 配置 browser_config.json 文件")
        print("3. 根据需要修改 data 目录下的CSV文件")
        return 0
    else:
        print("⚠️  有些依赖包无法导入，请检查安装情况")
        print("\n建议:")
        print("1. 运行: pip install --upgrade pip")
        print("2. 重新安装: pip install -e .")
        print("3. 如果问题持续，请检查Python版本和虚拟环境")
        return 1


if __name__ == "__main__":
    sys.exit(main())
