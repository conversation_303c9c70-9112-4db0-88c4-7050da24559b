import imaplib
import re
import email
import socket
import time
import pytz
from enum import Enum
from typing import Optional, Dict, Any, List
from loguru import logger
from email.header import make_header, decode_header
from email.message import EmailMessage
from contextlib import contextmanager
from email.utils import parsedate_to_datetime
import warnings


class EmailProvider(Enum):
    GMAIL = "imap.gmail.com"  # gmail邮箱
    HOTMAIL = "outlook.office365.com"  # hotmail邮箱
    OUTLOOK = "outlook.office365.com"  # outlook邮箱
    GMX = "imap.gmx.com"  # gmx邮箱
    RAMBLER = "imap.rambler.ru"  # rambler邮箱
    ICLOUD = "imap.mail.me.com"  # icloud邮箱


class HHMailException(Exception):
    pass


class HHMail:
    warnings.warn(
        "这个类已经废弃，将在未来版本中移除。请使用 emails 文件夹下文件 替代",
        DeprecationWarning,
        stacklevel=2,
    )

    def __init__(self, email: str, pwd: str):
        self.email: str = email
        self.pwd: str = pwd
        self._server: Optional[imaplib.IMAP4_SSL] = None

    @contextmanager
    def connect(self):
        try:
            self.login_by_imap4()
            yield self
        finally:
            self.logout()

    @property
    def is_connected(self) -> bool:
        return self._server is not None

    def login_by_imap4(self) -> None:
        imap_host = self._get_imap_by_email()
        if not imap_host:
            raise HHMailException("你的邮箱还未配置 IMAP4, 请检查配置文件")

        try:
            self._server = imaplib.IMAP4_SSL(imap_host)
            result, _ = self._server.login(self.email, self.pwd)
            if result != "OK":
                raise HHMailException(f"登录失败: {result}")
            logger.success(f"{self.email} 登录成功")
        except Exception as e:
            logger.error(f"{self.email} 登录失败: {e}")
            raise HHMailException(f"登录失败: {e}")

    def logout(self) -> None:
        if self.is_connected:
            self._server.logout()
            self._server = None

    def search(self, search_criteria: str, folder: str = "INBOX") -> List[bytes]:
        if not self.is_connected:
            raise HHMailException("请先登录")

        self._server.select(folder)
        _, messages = self._server.search(None, search_criteria)

        # 添加检查
        if not messages or not messages[0]:
            return []

        return messages[0].split()

    def search_subject(
        self,
        subject: str,
        recipient: str = None,
        folder: str = "INBOX",
        max_retries: int = 3,
    ) -> List[bytes]:
        if not subject:
            raise ValueError("邮件主题不能为空")

        for attempt in range(max_retries):
            try:
                if not self.is_connected:
                    self.login_by_imap4()

                # 确保文件夹被选中
                _, folder_status = self._server.select(folder)
                if not folder_status[0]:  # 检查文件夹是否存在
                    raise ValueError(f"邮件文件夹 '{folder}' 不存在")

                # 清理和转义搜索条件中的特殊字符
                subject = re.sub(
                    r'[\\"\(\)\{\}\[\]]', lambda m: f"\\{m.group()}", subject
                )
                search_criteria = f'SUBJECT "{subject}"'

                if recipient:
                    # 更严格的邮件地址提取和验证
                    email_match = re.search(
                        r"[<\s]?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})[>\s]?",
                        recipient,
                    )
                    if not email_match:
                        raise ValueError(f"无效的收件人邮箱地址: {recipient}")

                    email = email_match.group(1)
                    email = re.sub(
                        r'[\\"\(\)\{\}\[\]]', lambda m: f"\\{m.group()}", email
                    )
                    search_criteria = (
                        f'(OR TO "{email}" CC "{email}" {search_criteria})'
                    )

                result = self.search(search_criteria, folder=folder)
                if not result:
                    wait_time = min(2**attempt, 10)
                    time.sleep(wait_time)
                    continue

                return result

            except (imaplib.IMAP4.error, EOFError, socket.error) as e:
                self._server = None  # 确保连接被完全重置
                if attempt == max_retries - 1:
                    raise HHMailException(
                        f"邮件搜索失败，已重试{max_retries}次: {str(e)}"
                    )
                wait_time = min(2**attempt, 30)
                logger.warning(
                    f"连接失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries})"
                )
                time.sleep(wait_time)

            except Exception as e:
                if attempt == max_retries - 1:
                    raise HHMailException(f"邮件搜索过程中发生错误: {str(e)}")
                continue

        return []

    def get_latest_email(self, folder: str = "INBOX") -> Dict[str, Any]:
        messages = self.search("ALL", folder=folder)
        if not messages:
            raise HHMailException("邮箱为空")
        return self.get_mail_content(messages[-1])

    def _extract_payload(self, part: EmailMessage, content_type: str) -> Optional[str]:
        """
        从邮件部分提取指定类型的内容

        Args:
            part: 邮件部分
            content_type: 内容类型 ('text/plain' 或 'text/html')

        Returns:
            Optional[str]: 解码后的内容，失败返回None
        """
        if part.get_content_type() != content_type:
            return None

        try:
            charset = part.get_content_charset() or "utf-8"
            payload = part.get_payload(decode=True)
            if not payload:
                return None
            return payload.decode(charset, errors="replace")
        except Exception as e:
            logger.warning(f"解码{content_type}内容时发生错误: {e}")
            return None

    def _get_text_from_email(self, msg: EmailMessage) -> str:
        """
        从邮件中提取纯文本内容

        Args:
            msg: EmailMessage对象

        Returns:
            str: 提取的文本内容
        """
        contents = []

        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content = self._extract_payload(part, "text/plain")
                    if content:
                        contents.append(content)
            else:
                content = self._extract_payload(msg, "text/plain")
                if content:
                    contents.append(content)

        except Exception as e:
            logger.error(f"提取文本内容时发生错误: {e}")

        return "\n".join(contents) if contents else ""

    def _get_html_from_email(self, msg: EmailMessage) -> str:
        """
        从邮件中提取HTML内容，只返回第一个有效的HTML内容

        Args:
            msg: EmailMessage对象

        Returns:
            str: 提取的HTML内容
        """
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content = self._extract_payload(part, "text/html")
                    if content:
                        return content
            else:
                content = self._extract_payload(msg, "text/html")
                if content:
                    return content

        except Exception as e:
            logger.error(f"提取HTML内容时发生错误: {e}")

        return ""

    def _parse_email_data(self, raw_email: bytes) -> Optional[EmailMessage]:
        """
        解析原始邮件数据

        Args:
            raw_email: 原始邮件数据

        Returns:
            Optional[EmailMessage]: 解析后的邮件对象，失败返回None
        """
        try:
            if not raw_email:
                return None
            return email.message_from_bytes(raw_email)
        except Exception as e:
            logger.error(f"邮件解析错误: {e}")
            return None

    def _process_attachments(self, msg: EmailMessage) -> List[str]:
        """
        处理邮件附件

        Args:
            msg: EmailMessage对象

        Returns:
            List[str]: 附件名称列表
        """
        attachments = []

        try:
            for part in msg.walk():
                if (
                    part.get_content_maintype() == "multipart"
                    or part.get("Content-Disposition") is None
                ):
                    continue

                filename = part.get_filename()
                if filename:
                    try:
                        attachments.append(self._get_header_value(part, "filename"))
                    except Exception as e:
                        logger.warning(f"处理附件名称时发生错误: {e}")

        except Exception as e:
            logger.error(f"处理附件时发生错误: {e}")

        return attachments

    def _get_received_time(self, email_data) -> str:
        received_headers = email_data.get_all("Received")
        if received_headers:
            # 获取最后一个 Received 头部（最终接收服务器的时间）
            last_received = received_headers[-1]
            # 提取分号后的时间戳部分
            timestamp = last_received.split(";")[-1].strip()
            return timestamp
        return None

    def get_mail_content(self, message_number: bytes) -> Dict[str, Any]:
        """
        获取指定邮件的完整内容

        Args:
            message_number: 邮件ID

        Returns:
            Dict[str, Any]: 包含邮件所有信息的字典

        Raises:
            HHMailException: 当邮件获取或解析失败时
        """
        if not self.is_connected:
            raise HHMailException("请先登录")

        try:
            # 添加日志记录邮件ID
            # logger.debug(f"正在获取邮件ID: {message_number}")

            # 首先获取邮件数据
            _, msg_data = self._server.fetch(message_number, "(BODY[])")

            # 详细记录获取到的数据
            # logger.debug(f"获取到的邮件数据类型: {type(msg_data)}")
            # logger.debug(f"邮件数据长度: {len(msg_data) if msg_data else 'None'}")

            if not msg_data or not isinstance(msg_data, list) or len(msg_data) == 0:
                raise HHMailException(f"无法获取邮件内容: msg_data={msg_data}")

            # 解析邮件内容
            raw_email = msg_data[0]
            # logger.debug(f"原始邮件数据类型: {type(raw_email)}")

            if isinstance(raw_email, tuple):
                raw_email = raw_email[1]
                # logger.debug("从元组中提取邮件内容")

            if not isinstance(raw_email, bytes):
                raise HHMailException(f"无效的邮件数据类型: {type(raw_email)}")

            email_data = email.message_from_bytes(raw_email)
            # 检查解析结果
            if not email_data:
                logger.error("邮件解析返回空值")
                raise HHMailException("邮件解析失败: 解析结果为空")

            if not isinstance(email_data, (email.message.Message, EmailMessage)):
                logger.error(f"邮件解析结果类型错误: {type(email_data)}")
                raise HHMailException(
                    f"邮件解析失败: 无效的邮件格式 ({type(email_data)})"
                )

            # 获取并格式化日期
            date_str = self._get_received_time(email_data)
            try:
                # 解析UTC时间
                date = parsedate_to_datetime(date_str)
                # 转换为北京时间
                beijing_tz = pytz.timezone("Asia/Shanghai")
                local_date = date.astimezone(beijing_tz)
                formatted_date = local_date.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logger.warning(f"日期格式化失败: {e}")
                formatted_date = date_str

            # logger.debug("返回邮件内容:")
            # logger.debug(f"主题: {self._get_header_value(email_data, 'subject')}")
            # # logger.debug(f"正文: {self._get_text_from_email(email_data)}")
            # # logger.debug(f"HTML内容: {self._get_html_from_email(email_data)}")
            # logger.debug(f"发件人: {self._get_header_value(email_data, 'from')}")
            # logger.debug(f"收件人: {self._get_header_value(email_data, 'to')}")
            # logger.debug(f"日期: {formatted_date}")

            return {
                "subject": self._get_header_value(email_data, "subject"),
                "content": self._get_text_from_email(email_data),
                "html_content": self._get_html_from_email(email_data),
                "sender": self._get_header_value(email_data, "from"),
                "to": self._get_header_value(email_data, "to"),
                "date": formatted_date,
            }

        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP操作错误: {e}")
            raise HHMailException(f"IMAP操作错误: {e}")
        except Exception as e:
            logger.error(f"获取邮件内容时发生错误: {e}")
            raise HHMailException(f"获取邮件内容时发生错误: {e}")

    def _get_html_from_email(self, msg):
        html = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/html":
                    try:
                        charset = part.get_content_charset() or "utf-8"
                        html = part.get_payload(decode=True).decode(
                            charset, errors="replace"
                        )
                    except Exception as e:
                        logger.warning(f"解码 HTML 内容时发生错误: {e}")
        else:
            try:
                charset = msg.get_content_charset() or "utf-8"
                html = msg.get_payload(decode=True).decode(charset, errors="replace")
            except Exception as e:
                logger.warning(f"解码 HTML 内容时发生错误: {e}")
        return html

    def _get_imap_by_email(self) -> Optional[str]:
        match = re.search(r"@(.+?)\.", self.email)
        domain = match.group(1) if match else None
        try:
            return EmailProvider[domain.upper()].value
        except KeyError:
            return None

    def _get_header_value(self, msg_or_part, header_name):
        value = msg_or_part.get(header_name)
        if value is None:
            return ""
        try:
            # 尝试使用 make_header 和 decode_header
            return str(make_header(decode_header(value)))
        except Exception:
            # 如果失败，直接返回原始值
            return str(value)

    def _get_text_from_email(self, msg):
        text = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    try:
                        charset = part.get_content_charset() or "utf-8"
                        text += part.get_payload(decode=True).decode(
                            charset, errors="replace"
                        )
                    except Exception as e:
                        logger.warning(f"解码邮件内容时发生错误: {e}")
        else:
            try:
                charset = msg.get_content_charset() or "utf-8"
                text = msg.get_payload(decode=True).decode(charset, errors="replace")
            except Exception as e:
                logger.warning(f"解码邮件内容时发生错误: {e}")
        return text


# 使用示例
if __name__ == "__main__":
    pass
