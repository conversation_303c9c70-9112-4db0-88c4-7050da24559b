from typing import List

from loguru import logger

from src.socials import Telegram
from src.utils.password import generate_pwd, generate_strong_password
from src.utils.secure_encryption import SecureEncryption

from ..decorators import require_page_and_config
from ..exceptions import BrowserControllerError


class TelegramMixin:

    # 更新Telegram密码
    @require_page_and_config
    def update_password_telegram(self) -> bool:
        telegram = Telegram(self.id, self.page)
        password = self.browser_config.tg_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        if not password:
            raise BrowserControllerError(f"{self.id} Telegram 密码未配置")

        new_password = generate_pwd()
        result = telegram.update_password(password, new_password)
        if result:
            logger.info(f"{self.id} Telegram 密码更新成功, 新密码: {new_password}")
            self.browser_config.tg_password = SecureEncryption.encrypt(new_password)
            self._repository.update(self.browser_config)
        return result

    # 更新Telegram邮箱
    @require_page_and_config
    def update_email_telegram(self) -> bool:
        telegram = Telegram(self.id, self.page)
        password = self.browser_config.tg_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        if not password:
            raise BrowserControllerError(f"{self.id} Telegram 邮箱密码未配置")

        recovery_mail = self.browser_config.tg_email
        recovery_mail_pwd = self.browser_config.tg_email_password
        if SecureEncryption.is_encrypted(recovery_mail_pwd):
            recovery_mail_pwd = SecureEncryption.decrypt(recovery_mail_pwd)

        if not recovery_mail or not recovery_mail_pwd:
            raise BrowserControllerError(f"{self.id} Telegram 邮箱信息未配置")

        recovery_proxy_mail = self.browser_config.tg_email_proxy

        if telegram.update_recovery_mail(recovery_mail_pwd, password, recovery_mail, recovery_proxy_mail):
            logger.info(f"{self.id} Telegram 邮箱更新成功, 新邮箱: {recovery_mail}")
            return True
        return False

    @require_page_and_config
    def join_channel(self, group_names: list[str]):
        telegram = Telegram(self.id, self.page)
        for group_name in group_names:
            telegram.join_channel(group_name)
