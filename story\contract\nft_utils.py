from typing import Dict, Optional
from web3 import Web3
from loguru import logger
from .base_contract import BaseContract


class NFTContract(BaseContract):

    def get_token_balance(self, contract_address: str) -> float:
        """获取代币余额"""
        return self._get_token_balance(contract_address)

    def is_minted(self, contract_address: str) -> bool:
        """检查NFT是否已铸造"""
        try:
            balance = self.web3_manager.get_nft_balance(
                contract_address, self.account_address
            )
            return balance > 0
        except Exception as e:
            logger.error(f"{self.id} 检查NFT铸造状态失败: {str(e)}")
            return False

    def write_contract(self, contract_address: str, input_data: str) -> bool:
        """调用合约写入方法

        Args:
            contract_address: 合约地址
            input_data: 调用数据

        Returns:
            bool: 是否成功
        """
        if not all([self.private_key, self.rpc_url]):
            raise ValueError(f"{self.id} 缺少必要参数")

        try:

            def _do_write():
                return self._execute_contract_call(
                    contract_address=contract_address,
                    input_data=input_data,
                )

            success = self._execute_with_retry(_do_write)
            return success

        except Exception as e:
            logger.error(f"{self.id} 合约写入失败: {str(e)}")
            return False

    def build_transaction_data(
        self, function_selector: str, wallet_address: str, signature: str
    ) -> str:
        """构建交易数据

        Args:
            function_selector: 函数选择器(带0x前缀)
            wallet_address: 钱包地址(带0x前缀)
            signature: 签名数据(带0x前缀)
        """
        try:
            # 移除所有0x前缀
            function_selector = function_selector.replace("0x", "")
            wallet_address = wallet_address.replace("0x", "")
            signature = signature.replace("0x", "")

            # 1. 函数选择器(4字节)
            if len(function_selector) != 8:  # 4字节 = 8个hex字符
                raise ValueError("函数选择器必须是4字节")

            # 2. 地址补齐到32字节
            padded_address = wallet_address.zfill(64)

            # 3. 动态数据位置指针(固定为0x40)
            dynamic_position = (
                "0000000000000000000000000000000000000000000000000000000000000040"
            )

            # 4. 签名数据长度(0x41 = 65字节)
            signature_length = (
                "0000000000000000000000000000000000000000000000000000000000000041"
            )

            # 5. 签名数据(补齐到32字节的倍数)
            padded_signature = signature.ljust(192, "0")

            # 拼接完整数据
            hex_data = (
                function_selector
                + padded_address
                + dynamic_position
                + signature_length
                + padded_signature
            )

            return "0x" + hex_data

        except Exception as e:
            raise Exception(f"构建交易数据失败: {str(e)}")

    def mint_nft(self, recipient: str, signature: str) -> bool:
        """铸造NFT

        Args:
            recipient: 接收地址
            signature: 签名数据

        Returns:
            bool: 是否成功
        """
        try:
            # 构建mint交易数据
            input_data = self.build_transaction_data(
                "0xb510391f", recipient, signature  # mint方法的函数选择器
            )

            # 使用已有的write_contract方法发送交易
            success = self.write_contract(
                contract_address=self.contract_address, input_data=input_data
            )

            if success:
                logger.success(f"{self.id} NFT mint成功")
            else:
                logger.error(f"{self.id} NFT mint失败")

            return success

        except Exception as e:
            logger.error(f"{self.id} NFT mint异常: {str(e)}")
            return False
