import os
from typing import Optional

from DrissionPage import Chromium
from loguru import logger

from src.fingerprints import (
    close_browser_with_morelogin_id,
    get_address_with_morelogin,
)
from src.utils import get_project_root_path

from .base_browser import BaseBrowser, BrowserType


class MoreLoginBrowser(BaseBrowser):

    def get_chromium_page(self) -> Chromium | None:
        """获取浏览器页面实例"""
        try:
            if not self.browser_id:
                logger.error(f"{self.id} browser_id 不存在")
                return None

            debugger_address = get_address_with_morelogin(self.browser_id)
            if not debugger_address:
                logger.error(f"{self.id} 获取调试地址失败")
                return None

            return Chromium(addr_or_opts=debugger_address)
        except Exception as e:
            logger.error(f"{self.id} 获取浏览器页面实例失败: {str(e)}")
            return None

    def close(self):
        """关闭浏览器"""
        try:
            if self.browser_id:
                close_browser_with_morelogin_id(self.browser_id)
                self.page = None  # 清理页面实例
            else:
                logger.warning(f"{self.id} 没有可关闭的浏览器ID")
        except Exception as e:
            logger.error(f"{self.id} 关闭浏览器失败: {str(e)}")

    def wallet_config_path(self) -> str:
        """钱包配置路径"""
        return os.path.join(get_project_root_path(), "data/more.csv")

    @property
    def browser_type(self) -> BrowserType:
        return BrowserType.MORE
