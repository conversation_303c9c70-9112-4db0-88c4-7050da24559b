import os
import random
import sys
from time import sleep
from src.utils.thread_executor import ThreadExecutor
import click
from loguru import logger
from typing import Optional
import time
from retry import retry
from src.utils.yescaptcha import Yes<PERSON>aptcha
# 使用完整的导入路径
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.element_util import get_element

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")


class FaucetAlchemy:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def _get_captcha_token(self, page) -> Optional[str]:
        return YesCaptcha.get_web_plugin_token_recaptcha(page, self.id)
    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for i in range(try_count):
            connect_wallet_button = lasted_tab.ele(
                "x://button[contains(., 'Connect Wallet') or contains(., '连接钱包')]",
                timeout=5,
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele(
                    "x://button[contains(., 'Connect Metamask') or contains(., '连接钱包')]"
                ).click()
                sleep(2)
                self.browser_controller.okx_wallet_connect()
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False
    def _check_connect_wallet(self, lasted_tab):
        if lasted_tab.ele(
            "x://button[contains(., 'Connect Wallet') or contains(., '连接钱包')]",
            timeout=5,
        ):
            self._connect_wallet(lasted_tab)
            return True
        return False
    def _is_claim(self, tab):
            for i in range(10):  # 最多等待10次，每次0.5秒
                # 检查是否有交易历史不足的提示
                time_error = get_element(tab, "x://span[contains(text(),'Please try again after 72 hours from your original request.')]", 0.5)
                if time_error:
                    logger.error(f"【{self.id}】 Please try again after 72 hours from your original request.")
                    return False
                cap_error = get_element(tab, "x://span[contains(text(),'not complete the reCAPTCHA as expected')]", 0.5)
                if cap_error:
                    logger.error(f"【{self.id}】 Please refresh the page and try again")
                    raise Exception((f"【{id}】 reCAPTCHA未通过,重试"))
                ref_error = get_element(tab, "x://span[contains(text(),'Please refresh the page and try again')]", 0.5)
                if ref_error:
                    logger.error(f"【{self.id}】 Please refresh the page and try again")
                    raise Exception((f"【{id}】 领水发生异常,重试"))
                
                already_claim_error = get_element(tab, "x://span[contains(text(),'we only send 0.1 Sepolia ETH every 72 hours.')]", 0.5)
                if already_claim_error:
                    logger.error(f"【{self.id}】 we only send 0.1 Sepolia ETH every 72 hours.")
                    return False
                claim_success = get_element(tab, "x://a[contains(@href, 'https://sepolia.etherscan.io/tx/')]", 0.5)
                if claim_success:
                    logger.success(f"【{self.id}】 Follow your transaction,领水成功")
                    time.sleep(1)
                    return True

                time.sleep(0.5)
            else:
                logger.error(f"【{self.id}】 Neither error message nor button appeared within timeout")
                return False
    @retry(tries=2, delay=1)
    def faucet(self):
        try:
            #self.browser_controller.okx_wallet_login()
            address = self.browser_controller.browser_config.evm_address
            logger.info(f"Executing task for {self.id} with browser type {self.browser_type} with address  {address}")

            url = "https://www.alchemy.com/faucets/ethereum-sepolia"
            tab = self.page.new_tab(url)
            sleep(3)
            #self._check_connect_wallet(tab)
            
            logger.info(f"【{self.id}】尝试领水")
            input = get_element(tab, "x://input[@placeholder='Enter Your Wallet Address (0x...) or ETH Mainnet ENS Domain']", 5)
            if input :
                input.input(address)
                
                sleep(2)
            captcha_token = self._get_captcha_token(tab)
            if not captcha_token:
                raise Exception(f"【{self.id}】 获取验证码失败")
            
            continue_btn = get_element(tab, "x://button[@type='submit']", 5)
            if continue_btn:
                logger.info(f"【{self.id}】 点击领水按钮 ")
                continue_btn.click()
                sleep(2)
           
            if self._is_claim(tab):
                return True
            return False
        except Exception as e:
            logger.error(f"【{id}】 领水发生异常，error={str(e)}")
            raise Exception(f"【{id}】 领水发生异常,重试")
        finally:
            try:
                pass
                #self.page.quit()
            except Exception as e:
                logger.error(e)
        return False


def _run_task(type, index):
    try:
        browser = FaucetAlchemy(type, str(index))
        browser.faucet()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        # 新增成功列表
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:

                browser = FaucetAlchemy(type, str(index))
                result = browser.faucet()  # 只调用一次
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    browser.page.quit()
                return result  # 返回结果
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_quicknode-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        failed_indices = [str(index).strip() for index in failed_indices]  # 移除空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/faucet_eth_alchemy.py run -t ads -i 1-50
# python3 examples/monad/faucet_eth_alchemy.py run -t bit -i 1-50

if __name__ == "__main__":
    cli()
    #_run_task(BrowserType.BIT, 14)
