#!/usr/bin/env python3
"""
简化的CSV处理运行脚本
提供简单的接口来运行CSV过滤和匹配功能
"""

import os
import sys

sys.path.append("/Users/<USER>/crypto/source/auto/dp_common/examples/okx/statistics")
from csv_processor_complete import CSVProcessor


def run_simple_processing():
    """简单的处理函数，使用默认参数"""
    print("🚀 开始CSV数据处理...")
    print("=" * 60)

    # 默认文件路径
    satlayer_file = "examples/okx/satlayer2_chrome.csv"
    chrome_file = "data/chrome.csv"

    # 检查文件是否存在
    if not os.path.exists(satlayer_file):
        print(f"❌ 错误：找不到文件 {satlayer_file}")
        return False

    if not os.path.exists(chrome_file):
        print(f"❌ 错误：找不到文件 {chrome_file}")
        return False

    # 创建处理器
    processor = CSVProcessor(log_level="INFO")

    # 执行处理
    success = processor.process_csv_files(
        satlayer_file=satlayer_file,
        chrome_file=chrome_file,
        output_prefix="filtered_matched_data",
    )

    if success:
        print("\n🎉 处理成功完成！")
        print("📁 输出文件已生成，可以进行后续分析。")
    else:
        print("\n❌ 处理失败，请检查日志信息。")

    return success


def run_custom_processing(satlayer_path, chrome_path, output_prefix="custom_output"):
    """自定义路径的处理函数"""
    print("🚀 开始自定义CSV数据处理...")
    print(f"📊 Satlayer文件: {satlayer_path}")
    print(f"🌐 Chrome文件: {chrome_path}")
    print(f"📁 输出前缀: {output_prefix}")
    print("=" * 60)

    # 创建处理器
    processor = CSVProcessor(log_level="INFO")

    # 执行处理
    success = processor.process_csv_files(
        satlayer_file=satlayer_path,
        chrome_file=chrome_path,
        output_prefix=output_prefix,
    )

    return success


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，使用默认处理
        success = run_simple_processing()
    elif len(sys.argv) == 3:
        # 两个参数：satlayer文件和chrome文件
        satlayer_path = sys.argv[1]
        chrome_path = sys.argv[2]
        success = run_custom_processing(satlayer_path, chrome_path)
    elif len(sys.argv) == 4:
        # 三个参数：satlayer文件、chrome文件和输出前缀
        satlayer_path = sys.argv[1]
        chrome_path = sys.argv[2]
        output_prefix = sys.argv[3]
        success = run_custom_processing(satlayer_path, chrome_path, output_prefix)
    else:
        print("使用方法:")
        print(
            "  python run_csv_processing.py                                    # 使用默认文件路径"
        )
        print(
            "  python run_csv_processing.py <satlayer.csv> <chrome.csv>        # 指定文件路径"
        )
        print(
            "  python run_csv_processing.py <satlayer.csv> <chrome.csv> <前缀>  # 指定文件路径和输出前缀"
        )
        print()
        print("示例:")
        print("  python run_csv_processing.py")
        print("  python run_csv_processing.py data/satlayer.csv data/chrome.csv")
        print(
            "  python run_csv_processing.py data/satlayer.csv data/chrome.csv my_output"
        )
        sys.exit(1)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
