import os
import platform
import random
from datetime import datetime
from time import sleep

import click
from account import So<PERSON><PERSON><PERSON><PERSON>unt
from faucet_web import <PERSON>au<PERSON><PERSON><PERSON><PERSON>
from loguru import logger
from quest import Quest

from config import DEFAULT_BROWSER_TYPE
from src.biz.nfts2 import NFTs2
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.config import get_browser_extension_id
from src.controllers import BrowserController
from src.utils.common import generate_email_content, get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.mail_notify import EmailSender
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/somnia.log", rotation="10MB", level="SUCCESS")


def _execute_task(type, index, data_util: DataUtil):
    controller = BrowserController(type, index)
    try:
        controller.window_max()
        controller.okx_wallet_login()

        # 更改yescaptcha插件状态
        extension_id = get_browser_extension_id("yescaptcha", type)
        controller.chrome_extension_status(extension_id, True)
        FaucetWeb(controller, data_util).faucet()

        account = SomniaAccount(controller, data_util)
        account.execute()
        Quest(controller, data_util).execute()
        account.get_points()
        controller.chrome_extension_status(extension_id, False)
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False
    else:
        return True
    finally:
        if controller:
            controller.close_page()


def _execute_reconnect_x_task(type, index):
    controller = BrowserController(type, index)
    try:
        controller.window_max()
        controller.okx_wallet_login()

        # 更改yescaptcha插件状态
        extension_id = get_browser_extension_id("yescaptcha", type)
        controller.chrome_extension_status(extension_id, True)

        data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
        csv_path = os.path.join(data_dir, f"somnia_{type.name.lower()}.csv")
        data_util = DataUtil(csv_path)

        account = SomniaAccount(controller, data_util)
        account.reconnect_x()

        controller.chrome_extension_status(extension_id, False)
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False
    else:
        return True
    finally:
        if controller:
            controller.close_page()


def safe_int(value, default=0):
    """安全地将值转换为整数，处理空字符串和其他异常情况."""
    if value == "":
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def _execute_gm(type, index, data_util: DataUtil):
    controller = BrowserController(type, index)
    try:
        # 获取当前日期，避免函数执行过程中跨日
        today = datetime.now().date()

        # 判断是否当天获取过
        data = data_util.get(controller.browser_config.evm_address) or {}
        streak_count = safe_int(data.get("streak_count", 0))
        last_streak_count = safe_int(data.get("last_streak_count", 0))
        streak_date_time = data.get("streak_date_time", "")

        if streak_count > 0 and streak_count > last_streak_count and streak_date_time:
            try:
                streak_date = datetime.strptime(streak_date_time, "%Y-%m-%d %H:%M:%S").date()
                if streak_date == today:
                    logger.info(f"【{index}】今天已经获取过积分了")
                    return True
            except ValueError:
                logger.warning(f"【{index}】日期格式错误: {streak_date_time}")

        controller.window_max()
        controller.okx_wallet_login()
        account = SomniaAccount(controller, data_util)
        account.execute()
        account.get_points()
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False
    else:
        return True
    finally:
        if controller:
            controller.close_page()


def _execute_get_social_name(type, index, data_util: DataUtil):
    controller = BrowserController(type, index)
    try:
        controller.window_max()
        controller.okx_wallet_login()

        account = SomniaAccount(controller, data_util)
        account.get_social_name()
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False
    else:
        return True
    finally:
        if controller:
            controller.close_page()


def _check_result(type: BrowserType, index, data_util: DataUtil):
    try:
        indices = parse_indices(index)
        if not indices:
            logger.warning("无效的索引")
            return False

        type_name = type.value
        for attempt in range(5):
            records = data_util.list()
            filtered_records = [r for r in records if r.get("type") == type_name and int(r.get("index")) in indices]
            if not filtered_records:
                logger.warning("未找到匹配的记录")
                return False

            # 统计记录
            failed_record_ids = []
            today = datetime.now().date()

            for record in filtered_records:
                streak_date_time = record.get("streak_date_time")
                record_index = record.get("index")
                try:
                    if not streak_date_time:
                        failed_record_ids.append(record_index)
                        continue

                    last_date = datetime.strptime(streak_date_time, "%Y-%m-%d %H:%M:%S").date()
                    if last_date != today:
                        failed_record_ids.append(record_index)
                except ValueError as e:
                    logger.error(f"日期格式错误: {streak_date_time}, {str(e)}")
                    failed_record_ids.append(record_index)

            if not failed_record_ids:
                logger.success("全部签到成功")
                return True

            # 处理失败的记录
            for record_id in failed_record_ids:
                try:
                    _execute_gm(type, record_id, data_util)
                except Exception as e:
                    logger.error(f"执行失败 record_id={record_id}: {str(e)}")

            # 如果不是最后一次尝试，等待后继续
            if attempt < 9:
                sleep(2 * (attempt + 1))

        logger.error("重试10次后仍有失败任务")
        return False

    except Exception as e:
        logger.error(f"检查结果时发生错误: {str(e)}")
        return False


def _summary(type: BrowserType, index, data_util: DataUtil):
    try:
        indices = parse_indices(index)
        if not indices:
            return

        type_name = type.value
        records = data_util.list()
        # records中过滤出type==type_name and index in indices
        filtered_records = [r for r in records if r.get("type") == type_name and int(r.get("index")) in indices]
        records = filtered_records
        if not records:
            return

        # 统计记录
        total_count = len(records)
        success_count = 0
        success_records = []  # 新增：存储成功的记录
        failed_records = []
        today = datetime.now().date()

        for record in records:
            streak_date_time = record.get("streak_date_time")
            if streak_date_time:
                last_date = datetime.strptime(streak_date_time, "%Y-%m-%d %H:%M:%S").date()
                if last_date == today:
                    success_count += 1
                    success_records.append(
                        {
                            "index": record.get("index"),
                            "type": record.get("type"),
                            "points": record.get("points", 0),
                            "streak_count": record.get("streak_count", 0),
                            "streak_date_time": streak_date_time,
                        }
                    )
                else:
                    failed_records.append(record)
            else:
                failed_records.append(record)

        failed_count = len(failed_records)
        device_name = platform.node()  # 获取网络名称/主机名

        # 生成邮件内容，添加成功记录信息
        email_content = generate_email_content(
            name=f"Somnia-{type_name}-{device_name}",
            total_count=total_count,
            success_count=success_count,
            failed_count=failed_count,
            failed_records=failed_records,
            success_fields={
                "index": "账号索引",
                "points": "积分",
                "streak_count": "连续签到天数",
                "streak_date_time": "签到时间",
            },
            success_records=success_records,
        )

        # 保存邮件内容到变量，供后续发送使用
        # logger.info(f"邮件内容已生成: {email_content}")
        return email_content
    except Exception as e:
        logger.error(e)


def send_email(email_content):
    try:
        sender = EmailSender()
        sender.send_mail(subject="somnia 签到报告", content=email_content, is_html=True)
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")


def _follow_x(type: BrowserType, index: str, data_util: DataUtil):
    browser = BrowserController(type, str(index))
    address = browser.browser_config.evm_address
    record = data_util.get(address) or {}
    usernames = ["@somniaGames_", "@playSparkball"]
    filter_usernames = []
    for username in usernames:
        if record.get(f"X_{username}", "0") == "1":
            continue
        filter_usernames.append(username)
    if len(filter_usernames) == 0:
        logger.success(f"【{index}】已全部关注成功")
        return True
    fail_usernames = []

    # 判断是否mac系统
    if platform.system() == "Darwin":
        result = browser.batch_follow_for_mac(filter_usernames)
    else:
        result = browser.batch_follow(filter_usernames)
    for key, value in result.items():
        if value:
            data_util.update(address, {f"X_{key}": "1"})
        else:
            fail_usernames.append(key)
    if fail_usernames:
        logger.error(f"【{index}】关注失败：{','.join(fail_usernames)}")
    else:
        logger.success(f"【{index}】全部关注成功")
    browser.close_page()
    return len(fail_usernames) == 0


def _nft_create_edition(type: BrowserType, index: str):
    try:
        browser = BrowserController(type, str(index))
        nft2 = NFTs2(browser)
        result = nft2.task_create_edition()
        browser.close_page()
        return result
    except Exception as e:
        logger.error(f"创建NFT失败: {e}")
        return False


def _execute_state(type: BrowserType, index: str, data_util: DataUtil, id: int):
    try:
        browser = BrowserController(type, str(index))
        browser.window_max()
        browser.okx_wallet_login()

        account = SomniaAccount(browser, data_util)
        result = account._login()
        if not result:
            logger.error(f"【{index}】登录失败")
            return False

        Quest(browser, data_util).check_state(id)
        browser.close_page()
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@click.group()
def cli():
    pass


@cli.command("x")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def x(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _execute_reconnect_x_task(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Somnia-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
        csv_path = os.path.join(data_dir, f"somnia_{type.name.lower()}.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            return _execute_task(type, index, data_util)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Somnia-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("gm")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def gm(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
        csv_path = os.path.join(data_dir, f"somnia_{type.name.lower()}.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            return _execute_gm(type, index, data_util)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Somnia-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise

    # 检查结果，如果有失败的进行重试
    try:
        _check_result(type, index, data_util)
    except Exception as e:
        logger.error(f"检查结果失败: {e}")

    try:
        result = _summary(type, index, data_util)
        if result:
            send_email(result)
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")


@cli.command("follow")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def follow_x(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _follow_x(type, _index)
        except Exception as e:
            logger.error(f"{_index} 关注推特失败: {e}")


@cli.command("nft2s")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def nft2s(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _nft_create_edition(type, _index)
        except Exception as e:
            logger.error(f"{_index} 创建NFT失败: {e}")


@cli.command("social")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def get_social_name(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _execute_get_social_name(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Somnia-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("state")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-id", "--id", type=int, prompt="请输入任务id", help="任务id")
def state(type, index, workers, id):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
        csv_path = os.path.join(data_dir, f"somnia_{type.name.lower()}.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            return _execute_state(type, index, data_util, id)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=3600,
            retries=3,
            interval=10,
            task_name=f"Somnia-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 批量关注推特
# python3 examples/somnia/index.py follow -t ads -i 1-100

# 执行任务
# python examples/somnia/index.py run -t ads -i 1-100

# 每日gm
# python examples/somnia/index.py gm -t ads -i 1-100
if __name__ == "__main__":
    cli()
    # data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
    # csv_path = os.path.join(data_dir, "somnia_chrome.csv")
    # data_util = DataUtil(csv_path)
    # _check_result(BrowserType.BIT, "1-100")
    # _follow_x(BrowserType.CHROME, "2")
    # _execute_gm(BrowserType.MORE, "1", data_util)
    # _execute_task(BrowserType.CHROME, "101", data_util)

    # data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
    # csv_path = os.path.join(data_dir, "somnia_chrome.csv")
    # data_util = DataUtil(csv_path)
    # _execute_state(BrowserType.CHROME, "1", data_util, 45)
