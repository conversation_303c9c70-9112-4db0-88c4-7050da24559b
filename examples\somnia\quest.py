import json
import os
import random
import re
from decimal import Decimal
from threading import Lock
from time import sleep

from dotenv import load_dotenv
from faker import Faker
from loguru import logger
from retry import retry

from bigint_quest_example_v2 import run_bigint_quest
from w3_manager import Web3Manager
from w3_tools import WebTools

from config import DEFAULT_BROWSER_TYPE
from src.biz.nfts2 import NFTs2
from src.browsers import BrowserType
from src.browsers.operations import try_click
from src.controllers import BrowserController
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.proxies import Proxies
from src.utils.secure_encryption import SecureEncryption

load_dotenv()

SOMNIA_RPC = os.getenv("SOMNIA_RPC")
PROXY_URL = os.getenv("PROXY_URL")
TWITTER_FOLLOW_CAMPAIGN_IDS = [15]

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/somnia.log", rotation="10MB", level="SUCCESS")


class Quest:
    _file_lock = Lock()

    def __init__(self, browser_controller: BrowserController, data: DataUtil):
        self.id = browser_controller.id
        self.browser_controller = browser_controller
        self.page = self.browser_controller.page
        self.address = self.browser_controller.browser_config.evm_address
        self.evm_private_key = self.get_private_key()
        self._setting_proxy()
        self.user_agent = self.browser_controller.browser_config.user_agent
        self.data = data
        self.twitter = None
        # 添加一个列表来跟踪已邀请的浏览器ID

    def _setting_proxy(self):
        proxies = Proxies(self.browser_controller.browser_config.proxy)
        if proxies.verify():
            self.proxy = self.browser_controller.browser_config.proxy
        else:
            self.proxy = PROXY_URL
        logger.info(f"【{self.id}】使用代理: {self.proxy}")

    def get_private_key(self) -> str | None:
        pk = self.browser_controller.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def generate_random_amount(self) -> float:
        """
        随机生成0.01 到 0.05 之间的随机数
        Returns:
            float: 生成的随机数.
        """
        # 生成 0.01 到 0.05 之间的随机数
        return round(random.uniform(0.01, 0.05), 2)

    def _connect_wallet(self, tab, browser_controller=None):
        logger.info(f"【{self.id}】 连接钱包")

        # 判断是否存在连接按钮
        wallet_element = get_element(tab, "x://button[contains(.,'0x')]", 8)
        if wallet_element:
            logger.info(f"【{self.id}】 钱包已连接")
            return True

        # 判断是否存在连接按钮
        # button = get_element(tab, "x://button[normalize-space()='Connect']", 8)
        button = get_element(tab, "x://span[@role='img' and @aria-label='poweroff' ]/parent::button", 3)
        if button:
            button.click()
            sleep(1)

            # 点击 OKX Wallet按钮
            okx_wallet_btn = get_element(tab, "x://button[contains(.,'OKX Wallet')]", 5)
            if okx_wallet_btn:
                okx_wallet_btn.click()
                sleep(1)

            if browser_controller is not None:
                browser_controller.okx_wallet_connect()
            else:
                self.browser_controller.okx_wallet_connect()

        latest_tab = self.page.latest_tab
        wallet_element = get_element(latest_tab, "x://button[contains(.,'0x')]", 5)
        if wallet_element:
            logger.info(f"【{self.id}】 钱包完成连接")
            return True

        # 如果链接失败就断开okx中所有链接
        # OKXWallet(self.browser_controller).disconnect_site_all()
        return False

    @retry(tries=3, delay=3)
    def _playground(self, id, type):
        try:
            tab = self.page.latest_tab
            tab.listen.start(f"https://quest.somnia.network/api/campaigns/{id}")
            tab.get(f"https://quest.somnia.network/campaigns/{id}")

            if not self._login():
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return False

            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 获取{type}任务失败，请检查网络")
                return False

            quests = res.response.body.get("quests", [])
            if not quests:
                logger.warning(f"【{self.id}】{self.address} {type} 任务列表为空")
                return True

            # 过滤出可执行的任务
            available_quests = [
                quest
                for quest in quests
                if not quest.get("isParticipated")
                and quest.get("visibility")
                and quest.get("eligibility")
                and quest.get("status") == "OPEN"
            ]

            if not available_quests:
                self.data.update(self.address, {type: "1"})
                logger.success(f"【{self.id}】{self.address} {type} 已全部完成")
                return True

            for quest in available_quests:
                title = quest.get("title", "")
                logger.info(f"执行任务：{title}")
                task_div = get_element(
                    tab,
                    f"x://div[@class='campaign-task   ' and contains(., '{title}')]",
                    3,
                )
                if not task_div:
                    logger.warning(f"【{self.id}】{self.address} 未找到 {title} 任务元素")
                    continue
                task_div.click()
                sleep(3)
                task_div = get_element(
                    tab,
                    f'x://div[@class="campaign-task   " and contains(., "{title}")]',
                    3,
                )
                task_div.click()
                sleep(3)
                switch = {
                    "Create one world on the Somnia Playground": lambda: self._task11_1(tab),
                    "Invite at least 2 people to your world": lambda: self._task11_2(),
                    # "Follow 0x_Playground on X": lambda: self._task11_3(tab), # 假任务，不需要完成
                    "Mint a Somnia Avatar for the Playground": lambda: self._task11_4(tab),
                    "Explore one world in the Somnia Playground": lambda: self._task11_5(),
                }
                if title in switch:
                    switch[title]()
                    logger.info(f"【{self.id}】{self.address} 执行完 {title} 任务")
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到对应的任务处理方法: {title}")

            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 执行 {type} 任务失败: {str(e)}")
            return False

    @retry(tries=3, delay=3)
    def receive_token(self, private_key: str):
        w3_manager = Web3Manager(self.id, private_key, SOMNIA_RPC, self.proxy, self.user_agent)
        balance = w3_manager.get_balance()
        if balance < 0.01:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        # 计算gas所需金额
        gas_limit = Decimal("21000")
        gas_params = w3_manager.get_gas_params()
        max_fee_per_gas = Decimal(str(gas_params.get("maxFeePerGas")))
        gas_fee = int(gas_limit * max_fee_per_gas * Decimal("2"))

        balance_wei = Decimal(str(w3_manager.to_wei(balance)))
        amount_wei = balance_wei - gas_fee

        result = w3_manager.send_transaction(self.address, int(amount_wei))
        is_success = result.get("success")
        if is_success:
            tx_hash = result.get("data").get("tx_hash")
            logger.success(f"【{self.id}】{self.address} 发送token成功: {tx_hash}")
            return "0x" + tx_hash

        logger.error(f"【{self.id}】{self.address} 发送token失败: {result.get('message')}")
        return False

    @retry(tries=3, delay=3)
    def send_token(self, address: str):
        balance = self.w3_manager.get_balance()
        if balance < 0.01:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        amount = self.generate_random_amount()
        if balance <= amount:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        amount_wei = self.w3_manager.to_wei(amount)
        result = self.w3_manager.send_transaction(address, amount_wei)
        is_success = result.get("success")
        if is_success:
            tx_hash = result.get("data").get("tx_hash")
            logger.success(f"【{self.id}】{self.address} 发送token成功: 0x{tx_hash}")
            return "0x" + tx_hash

        logger.error(f"【{self.id}】{self.address} 发送token失败: {result.get('message')}")
        return False

    def _generate_wallet(self):
        wallet_info = WebTools.generate_wallet()
        private_key = wallet_info.get("private_key")
        address = wallet_info.get("address")

        # 保存数据
        self.data.update(
            self.address,
            {
                "receive_pk": private_key,
                "receive_address": address,
            },
        )
        return address, private_key

    def _get_receive_address_and_private_key(self):
        datas = self.data.get(self.address)
        receive_address = datas.get("receive_address")
        receive_pk = datas.get("receive_pk")
        if not receive_address or not receive_pk:
            return self._generate_wallet()
        else:
            return receive_address, receive_pk

    def _submit_receive_tx(self, receive_tx, tab):
        try:
            # 提交 Receive STT TX
            xpath = "x://h2[.='Receive STT tokens from 1 or more members']"
            receive_ele = get_element(tab, xpath)
            if not receive_ele:
                logger.error(f"【{self.id}】{self.address} 提交receive tx失败")
                return False

            logger.success(f"【{self.id}】{self.address} 准备提交tx...")
            # 监听tx-hash
            tab.listen.start("https://quest.somnia.network/api/onchain/tx-hash")
            div_ele = receive_ele.parent()
            div_ele.ele("tag:input", timeout=5).input(receive_tx)
            div_ele.ele("x://button[.=' Submit ']", timeout=5).click()
            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 提交tx失败")
                return False

            body = json.loads(res.response.raw_body)
            is_success = body.get("success")
            if not is_success:
                logger.error(f"【{self.id}】{self.address} 提交receive tx失败")
                return False

            is_already_completed = body.get("isAlreadyCompleted")
            if not is_already_completed:
                logger.error(f"【{self.id}】{self.address} 提交receive tx失败")
                return False

            logger.success(f"【{self.id}】{self.address} 提交receive tx成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 提交receive tx失败: {str(e)}")
            return False

    def _submit_balance_check(self, tab):
        try:
            # 提交 Request and Get STT tokens
            xpath = "x://h2[.='Request and Get STT tokens']"
            balance_check_ele = get_element(tab, xpath)
            if not balance_check_ele:
                logger.error(f"【{self.id}】{self.address} 提交balance check失败")
                return False

            logger.success(f"【{self.id}】{self.address} 准备提交balance check...")
            # 监听tx-hash
            tab.listen.start("https://quest.somnia.network/api/onchain/native-token")
            div_ele = balance_check_ele.parent()

            completed_button = div_ele.ele("x://button[.='Completed']", timeout=5)
            if completed_button:
                logger.success(f"【{self.id}】{self.address} 已经完成任务")
                return True

            check_balance_button = div_ele.ele("x://button[.='Check Balance']", timeout=5)
            if not check_balance_button:
                logger.error(f"【{self.id}】{self.address} 找不到check balance按钮")
                return False

            check_balance_button.click()
            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 提交balance check失败")
                return False

            body = json.loads(res.response.raw_body)
            is_success = body.get("success")
            if not is_success:
                logger.error(f"【{self.id}】{self.address} 提交balance check失败")
                return False

            logger.success(f"【{self.id}】{self.address} 提交balance check成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 提交balance check失败: {str(e)}")
            return False

    def _submit_send_tx(self, send_tx, tab):
        try:
            # 提交 Send STT TX
            xpath = "x://h2[.='Send STT tokens to 1 or more members']"
            send_ele = get_element(tab, xpath)
            if not send_ele:
                logger.error(f"【{self.id}】{self.address} 提交send tx失败")
                return False

            logger.success(f"【{self.id}】{self.address} 准备提交tx...")
            # 监听tx-hash
            tab.listen.start("https://quest.somnia.network/api/onchain/tx-hash")
            div_ele = send_ele.parent()
            div_ele.ele("tag:input", timeout=5).input(send_tx)
            div_ele.ele("x://button[.=' Submit ']", timeout=5).click()
            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 提交send tx失败")
                return False

            body = json.loads(res.response.raw_body)
            is_success = body.get("success")
            if not is_success:
                logger.error(f"【{self.id}】{self.address} 提交send tx失败")
                return False

            logger.success(f"【{self.id}】{self.address} 提交send tx成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 提交send tx失败: {str(e)}")
            return False

    @retry(tries=3, delay=3)
    def _submit_task(self, send_tx, receive_tx, tab):
        if not send_tx:
            logger.error(f"【{self.id}】{self.address} send_tx is None")
            return False

        if not receive_tx:
            logger.error(f"【{self.id}】{self.address} receive_tx is None")
            return False

        self._submit_receive_tx(receive_tx, tab)
        try_click(tab, "x://button[@aria-label='next']", timeout=5)
        sleep(random.randint(1, 3))
        self._submit_balance_check(tab)
        try_click(tab, "x://button[@aria-label='next']", timeout=5)
        sleep(random.randint(3, 5))
        return self._submit_send_tx(send_tx, tab)

    def _campaigns7(self):
        tab = self.page.new_tab()
        tab.listen.start("https://quest.somnia.network/api/campaigns/7")
        tab.get("https://quest.somnia.network/campaigns/7")

        if not self._login():
            logger.error(f"【{self.id}】{self.address} 登录失败")
            return False

        try:
            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 获取任务失败，请检查网络")
                return False

            quests = res.response.body.get("quests", [])
            if not quests:
                logger.warning(f"【{self.id}】{self.address} 任务列表为空")
                return True

            # 过滤出可执行的任务
            available_quests = [
                quest
                for quest in quests
                if not quest.get("isParticipated") and quest.get("visibility") and quest.get("eligibility")
            ]

            if not available_quests:
                self.data.update(self.address, {"odyssey_share": "1"})
                logger.success(f"【{self.id}】{self.address} 已全部完成")
                return True

            address, private_key = self._get_receive_address_and_private_key()
            if not address or not private_key:
                logger.error(f"【{self.id}】{self.address} 获取钱包地址和私钥失败")
                return False

            self.w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

            # 发送token
            datas = self.data.get(self.address)
            send_tx = datas.get("send_tx")
            if not send_tx:
                send_tx = self.send_token(address)
                if not send_tx:
                    logger.error(f"【{self.id}】{self.address} 发送token失败")
                    return False

                # 成功后保存数据
                self.data.update(
                    self.address,
                    {
                        "send_tx": send_tx,
                    },
                )
                # 接收token
                sleep(random.randint(10, 20))

            # 接收token
            receive_tx = datas.get("receive_tx")
            if not receive_tx:
                receive_tx = self.receive_token(private_key)
                if not receive_tx:
                    logger.error(f"【{self.id}】{self.address} 接收token失败")
                    return False

                # 成功后保存数据
                self.data.update(
                    self.address,
                    {
                        "receive_tx": receive_tx,
                    },
                )

            # 提交任务
            if self._submit_task(send_tx, receive_tx, tab):
                self.data.update(self.address, {"odyssey_share": "1"})
            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 发送token失败: {str(e)}")
            return False

    def _twitter_task(self, quest):
        type = quest.get("type", "")
        title = quest.get("title", "")
        try:
            logger.info(f"【{self.id}】开始执行任务：{title}")

            if type == "RETWEET":
                tweetId = quest.get("customConfig", {}).get("tweetId", "")  # noqa: N806
                if not tweetId:
                    logger.warning(f"【{self.id}】{self.address} 未找到Twitter URL")
                    return False

                tweet_url = f"https://x.com/intent/retweet?tweet_id={tweetId}"
                # tweet_url = self._extract_tweet_url(description)
                if not tweet_url:
                    logger.warning(f"【{self.id}】{self.address} 未找到Twitter URL")
                    return False

                # return self.browser_controller.favorite_and_retweet(tweet_url, force=True)
                return self.browser_controller.retweet(tweet_url, force=False)

            if type == "TWITTER_FOLLOW":
                twitter_handle = quest.get("customConfig", {}).get("twitterHandle", "")
                if not twitter_handle:
                    return False

                if "Handsnftfun" == twitter_handle:
                    twitter_handle = "handsnftfun"

                twitter_name = f"@{twitter_handle}"
                result = self.browser_controller.batch_follow([twitter_name])
                return result.get(twitter_name, False)

        except Exception as e:
            logger.error(f"【{self.id}】执行任务：{title} 异常，error={str(e)}")
            return False

    def _extract_tweet_url(self, html_content):
        """从HTML内容中提取X.com推文链接"""
        # 匹配完整的X.com推文链接
        pattern = r"https?:\/\/(?:www\.)?x\.com\/([a-zA-Z0-9_-]+)\/status\/(\d+)"
        match = re.search(pattern, html_content)

        if match:
            return match.group(0)  # 返回完整链接
        return None

    def _extract_discord_invite(self, text):
        """
        从文本中提取Discord邀请链接.

        参数:
            text (str): 包含可能的Discord邀请链接的文本

        返回:
            str: 提取到的Discord邀请链接，如果没有找到则返回None
        """
        # 匹配Discord邀请链接的正则表达式
        # 可以匹配以下格式:
        # - https://discord.com/invite/XXXXXXX
        # - https://discord.gg/XXXXXXX
        # - discord.com/invite/XXXXXXX
        # - discord.gg/XXXXXXX
        pattern = r"(?:https?:\/\/)?(?:www\.)?discord(?:app)?\.(?:com\/invite|gg)\/([a-zA-Z0-9-]+)"

        # 查找匹配项
        match = re.search(pattern, text)

        if match:
            # 获取完整的邀请链接
            invite_code = match.group(1)
            full_url = f"https://discord.com/invite/{invite_code}"
            return full_url

        return None

    def _discord_task(self, quest):
        pass

    def _login(self):
        """
        登录Somnia并处理邀请码绑定.

        Returns
        -------
            Tab | None: 成功返回当前标签页，失败返回None
        """
        for _ in range(3):
            try:
                tab = self.page.latest_tab
                if self._connect_wallet(tab):
                    return True
            except Exception as e:
                logger.error(f"【{self.id}】{self.address} 登录失败: {str(e)}")
                continue
        return False

    def _connect_steam(self, tab):
        """
        连接Steam ID.
        """
        for _ in range(6):
            logger.info(f"【{self.id}】{self.address} 连接Steam ID")
            connect_ele = tab.ele("x://button[.='Connect Steam ID']", timeout=5)
            if not connect_ele:
                logger.success(f"【{self.id}】{self.address} 已经链接Steam ID")
                return True

            connect_ele.click()
            result = tab.wait.url_change(text="steamcommunity.com/openid/login", timeout=10)
            if not result:
                logger.error(f"【{self.id}】{self.address} 连接Steam ID失败")
                tab.get("https://quest.somnia.network/campaigns/47")
                sleep(5)
                continue

            sleep(3)
            login_ele = tab.ele("x://input[@type='submit' and @id='imageLogin']", timeout=10)
            if not login_ele:
                logger.error(f"【{self.id}】{self.address} 连接Steam ID失败")
                tab.refresh()
                sleep(5)
                continue

            login_ele.click()
            sleep(3)
            result = tab.wait.url_change(text="https://quest.somnia.network/campaigns", timeout=10)
            if not result:
                logger.error(f"【{self.id}】{self.address} 连接Steam ID失败")
                tab.get("https://quest.somnia.network/campaigns/47")
                sleep(5)
                continue

            connect_ele = tab.ele("x://button[.='Connect Steam ID']", timeout=5)
            if connect_ele:
                logger.error(f"【{self.id}】{self.address} 连接Steam ID失败")
                tab.get("https://quest.somnia.network/campaigns/47")
                sleep(5)
                continue

            return True

    def _visit_page(self, tab, title):
        try:
            task_btn = get_element(
                tab,
                f'x://div[@class="slide-container" and .//h2[text()="{title}"]]//button',
                5,
            )
            if task_btn:
                task_btn.click()
                sleep(2)
            latest_tab = self.page.latest_tab
            if "https://www.pixcape.games/playtest" in latest_tab.url:
                latest_tab.close()
        except Exception as e:
            logger.error(f"【{self.id}】执行任务 {title} 发生异常, error={str(e)}")

    def _task_campaigns(self, id, type):
        try:
            tab = self.page.latest_tab

            tab.listen.start(f"https://quest.somnia.network/api/campaigns/{id}")
            tab.get(f"https://quest.somnia.network/campaigns/{id}")

            if not self._login():
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return False

            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 获取{type}任务失败，请检查网络")
                return False

            quests = res.response.body.get("quests", [])
            if not quests:
                logger.warning(f"【{self.id}】{self.address} {type} 任务列表为空")
                return True

            # 过滤出可执行的任务
            available_quests = [
                quest
                for quest in quests
                if not quest.get("isParticipated")
                and quest.get("visibility")
                and quest.get("eligibility")
                and quest.get("status") == "OPEN"
            ]

            if not available_quests:
                self.data.update(self.address, {type: "1"})
                logger.success(f"【{self.id}】{self.address} {type} 已全部完成")
                return True

            record = self.data.get(self.address)
            if (
                len(available_quests) == 1
                and available_quests[0].get("title", "") == "Connect Telegram"
                and record["linked_tg"] == "-1"
            ):
                self.data.update(self.address, {"odyssey_socials": "1"})
                logger.success(f"【{self.id}】{self.address} Tg未配置，其他任务已全部完成")
                return True

            # if id == 47:
            #     result = self._connect_steam(tab)
            #     if not result:
            #         logger.error(f"【{self.id}】{self.address} 连接Steam ID失败")
            #         return False

            success = 0
            for quest in available_quests:
                title = quest.get("title", "")
                quest_type = quest.get("type", "")

                if "Like and Retweet Handsnft" in title:
                    continue

                if (
                    title == "Mint your unique Shannon ID"
                    or title == "Complete the Crazy Run 2.0"
                    or title == "Join Handsnft Discord"
                ):
                    continue

                if quest_type in ["RETWEET", "TWITTER_FOLLOW"]:
                    result = self._twitter_task(quest)
                    if not result:
                        logger.error(f"【{self.id}】{self.address} 执行{title}失败")
                        continue

                elif quest_type == "JOIN_DISCORD_SERVER":
                    # result = self._discord_task(quest)
                    # if not result:
                    #     logger.error(f"【{self.id}】{self.address} 加入Discord失败")
                    #     return False
                    pass
                elif quest_type == "TELEGRAM_GROUP_JOIN":
                    continue

                # elif quest_type == "ARBITRARY_API":
                #     if "Somnex Meme" not in title and "Quickswap" not in title and "NIA" not in title:
                #         continue

                logger.info(f"【{self.id}】开始验证任务：{title}")
                if id == 51:
                    mask_ele = get_element(
                        tab,
                        "x://div[@class='campaign-status-overlay']",
                        3,
                    )
                    if mask_ele:
                        tab.remove_ele(mask_ele)

                quest_id = quest.get("id", 0)

                task_div = get_element(
                    tab,
                    f'x://div[contains(@class,"campaign-task") and contains(text(), "{title}")]',
                    3,
                )
                if not task_div:
                    logger.warning(f"【{self.id}】{self.address} 未找到 {title} 任务元素")
                    continue

                task_div.click()
                sleep(5)
                task_div = get_element(
                    tab,
                    f'x://div[contains(@class,"campaign-task") and contains(text(), "{title}")]',
                    3,
                )
                task_div.click()
                sleep(3)

                if quest_id == 198:
                    self._visit_page(tab, title)

                tab.listen.start(
                    targets="https://quest\\.somnia\\.network/api/.*/.*",
                    is_regex=True,
                )
                quest_name_mapping = {
                    "Mint a Somnia Avatar for the Playground": "NFT Ownership",
                    "Mint an ForU Open EditionNFT": "NFT Ownership",
                    "Create your Somniac Appreciation Drop": "NFT Ownership",
                    "Create a unique NFT image of your childhood fear through NFTs2Me": "NFT Ownership",
                    "Mint LIFE to Start the Snake Frenzy and Claim Your Skin": "NFT Ownership",
                    "Create a  NFT art of your Quills playing Sparkball on NFTs2me": "NFT Ownership",
                    "Mint at least one Hand NFT Card": "NFT Ownership",
                }
                title = quest_name_mapping.get(title, title)

                task_btn = get_element(
                    tab,
                    f'x://div[@class="slide-container" and .//h2[text()="{title}"]]//button',
                )
                if not task_btn:
                    logger.warning(f"【{self.id}】未找到任务　{title}　的确认按钮")
                    continue

                task_btn.click()
                task_res = tab.listen.wait(timeout=90)
                if not task_res:
                    continue
                if task_res.response.body and task_res.response.body.get("success", False):
                    success += 1
                    logger.success(f"【{self.id}】 {type}-{title} 完成")
                else:
                    logger.warning(f"【{self.id}】 {type}-{title} 未完成")

                sleep(2)

            if len(available_quests) == success:
                self.data.update(self.address, {type: "1"})
                logger.success(f"【{self.id}】{self.address} {type} 已全部完成")
                return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 执行 {type} 任务失败: {str(e)}")
            return False

    def _campaigns8(self):
        """
        处理社交任务.
        """
        return self._task_campaigns(8, "odyssey_socials")

    def _campaigns10(self):
        return self._task_campaigns(10, "odyssey_darktable")

    def _campaigns11(self):
        # todo 实现Playground
        self._playground(11, "odyssey_playground")
        return self._task_campaigns(11, "odyssey_playground")
        # pass

    def _task11_1(self, tab):
        # return True
        start_ele = get_element(tab, "x://div[@class='slick-slide slick-active slick-current']//a[1]", 10)
        if not start_ele:
            logger.warning(f"【{self.id}】{self.address} 未找到任务入口元素")
            return False

        word_tab = start_ele.click.for_new_tab()
        sleep(3)

        for i in range(3):
            try:
                logger.info(f"【{self.id}】{self.address} 尝试第 {i + 1} 次加载世界")
                # 连接钱包
                self._connect_wallet(word_tab)

                # 等待页面加载
                provider_loaded = False
                try:
                    provider_loaded = word_tab.wait.ele_displayed("x://div[@class='somnia-provider-container ']", 180)
                except Exception as e:
                    logger.warning(f"【{self.id}】{self.address} 等待页面加载出错: {str(e)}")

                if not provider_loaded:
                    logger.warning(f"【{self.id}】{self.address} 页面加载超时，正在刷新")
                    word_tab.refresh()
                    sleep(5)
                    continue

                logger.info(f"【{self.id}】{self.address} 页面已加载完成")

                # 点击创建世界
                create_world_ele = get_element(
                    word_tab,
                    "x:(//span[contains(normalize-space(), 'Create World')])[1]",
                    30,
                )
                if create_world_ele:
                    logger.info(f"【{self.id}】{self.address} 点击创建世界")
                    create_world_ele.click()
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到创建世界按钮，重试")
                    continue

                # 选择世界框
                world_container_ele = get_element(
                    word_tab,
                    "x://div[@class='generate-selected-world-container null']//div[@class='generate-selected-world-content']",
                    10,
                )
                if world_container_ele:
                    logger.info(f"【{self.id}】{self.address} 点击选择世界框")
                    world_container_ele.click()
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到世界选择框，重试")
                    word_tab.refresh()
                    sleep(5)
                    continue

                # 选择世界类型
                somnia_village_ele = get_element(
                    word_tab,
                    "x://div[@class='generate-selected-worlds-item']//div[contains(text(),'Somnia Village')]",
                    10,
                )
                somnia_snow_ele = get_element(
                    word_tab,
                    "x://div[@class='generate-selected-worlds-item']//div[contains(text(),'Somnia Snow')]",
                    10,
                )
                somnia_city_ele = get_element(
                    word_tab,
                    "x://div[@class='generate-selected-worlds-item']//div[contains(text(),'Somnia City')]",
                    10,
                )

                # 收集所有可用的世界元素
                available_worlds = []
                if somnia_village_ele:
                    available_worlds.append((somnia_village_ele, "Somnia Village"))
                if somnia_snow_ele:
                    available_worlds.append((somnia_snow_ele, "Somnia Snow"))
                if somnia_city_ele:
                    available_worlds.append((somnia_city_ele, "Somnia City"))

                # 检查是否找到可用的世界选项
                if available_worlds:
                    # 随机选择一个世界，类似于gender_choice = random.choice(["man", "woman"])
                    selected_world, world_name = random.choice(available_worlds)
                    logger.info(f"【{self.id}】{self.address} 随机选择世界: {world_name}")
                    selected_world.click()
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到任何可用的世界选项，重试")
                    word_tab.refresh()
                    sleep(5)
                    continue

                # 点击创建世界按钮
                create_world_btn = get_element(word_tab, "x://span[normalize-space()='Create World']", 10)
                if create_world_btn:
                    create_world_btn.click()
                    logger.info(f"【{self.id}】{self.address} 点击创建世界按钮")
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到创建世界按钮，重试")
                    word_tab.refresh()
                    sleep(5)
                    continue

                # 点击Jump In按钮
                jump_in_btn = get_element(word_tab, "x://span[contains(text(),'Jump In')]", 150)
                if jump_in_btn:
                    jump_in_btn.click()
                    logger.info(f"【{self.id}】{self.address} 点击Jump In按钮")
                else:
                    logger.warning(f"【{self.id}】{self.address} 未找到Jump In按钮，可能创建中")
                    # 这里不刷新，可能是正在创建世界
                provider_loaded = False
                try:
                    provider_loaded = word_tab.wait.ele_displayed("x://div[@class='somnia-provider-container ']", 150)
                except Exception as e:
                    logger.warning(f"【{self.id}】{self.address} 等待页面加载出错: {str(e)}")

                if not provider_loaded:
                    logger.warning(f"【{self.id}】{self.address} 页面加载超时，正在刷新")
                    word_tab.refresh()
                    sleep(5)
                    continue

                # 保存世界URL
                if word_tab.wait.url_change(
                    "https://playground.somnia.network/mb-playground-2/world/",
                    timeout=60,
                ):
                    self.data.update(
                        self.address,
                        {
                            "world_url": word_tab.url,
                        },
                    )
                    logger.success(f"【{self.id}】{self.address} 成功创建世界，URL: {word_tab.url}")

                    sleep(30)
                    word_url = word_tab.url
                    word_tab.close()

                    self._task11_2(word_url)

                    return True
                else:
                    logger.warning(f"【{self.id}】{self.address} 页面加载超时，正在刷新")
                    word_tab.refresh()
                    sleep(5)
                    continue

            except Exception as e:
                logger.error(f"【{self.id}】{self.address} 页面加载失败: {str(e)}")
                try:
                    word_tab.refresh()
                    sleep(5)
                except:
                    pass

        logger.error(f"【{self.id}】{self.address} 创建世界失败，已达到最大重试次数")
        try:
            word_tab.close()
        except:
            pass
        return False

    def _task_get_world_url(self):
        tab = self.page.new_tab("")
        tab.listen.start(
            "https://items-builder-backend-prod-dot-somnia-testnet-projects.uc.r.appspot.com/accounts/login"
        )
        tab.get("https://playground.somnia.network/mb-playground-2")

        result = self._connect_wallet(tab)
        if not result:
            return None

        res = tab.listen.wait(timeout=60)
        if not res:
            return None

        if res.response.status == 200:
            worlds = res.response.body.get("worlds")
            if worlds:
                world = worlds[0]
                _id = world.get("_id")
                return f"https://playground.somnia.network/mb-playground-2/world/{_id}"

        return None

    def _task11_2(self, world_url=None):
        # 如果传入的 world_url 为 None，则从数据中获取
        if world_url is None:
            data = self.data.get(self.address)
            world_url = data.get("world_url")

        if not world_url:
            world_url = self._task_get_world_url()
            if world_url:
                data = self.data.update(self.address, {"world_url": world_url})
            else:
                logger.error(f"【{self.id}】{self.address} 未找到世界URL")
                return False

        logger.info(f"【{self.id}】{self.address} 开始邀请好友访问世界: {world_url}")

        # 需要邀请的次数
        needed_invites = 2
        successful_invites = 0
        invited_browser_ids = []
        # 尝试最多5次，以确保能找到2个不同的浏览器
        max_tries = 10
        tries = 0

        while successful_invites < needed_invites and tries < max_tries:
            tries += 1
            # 随机选择一个不同的浏览器ID，且不重复使用
            while True:
                index = random.randint(0, 99)
                if index != self.id and index not in invited_browser_ids:
                    break

            logger.info(f"【{self.id}】{self.address} 尝试邀请browser ID {index} 访问世界")

            browser = None

            try:
                # 记录这个ID已被使用
                invited_browser_ids.append(index)

                # 创建新的浏览器控制器
                browser = BrowserController(self.browser_controller.browser_type, str(index))
                browser.okx_wallet_login()
                tab = browser.open_url(world_url)

                # 连接钱包
                self._connect_wallet(tab, browser)

                # 等待页面加载
                provider_loaded = False
                try:
                    provider_loaded = tab.wait.ele_displayed("x://div[@class='somnia-provider-container ']", 300)
                    if provider_loaded:
                        logger.success(f"【{self.id}】{self.address} 成功邀请 browser ID {index} 访问世界")
                        successful_invites += 1
                    else:
                        logger.warning(f"【{self.id}】{self.address} 邀请 browser ID {index} 失败，页面未加载")
                except Exception as e:
                    logger.warning(f"【{self.id}】{self.address} 等待页面加载出错: {str(e)}")
                sleep(8)

            except Exception as e:
                logger.error(f"【{self.id}】{self.address} 邀请 browser ID {index} 异常: {str(e)}")
            finally:
                if browser:
                    browser.close_page()

        if successful_invites >= needed_invites:
            logger.success(f"【{self.id}】{self.address} 成功邀请 {successful_invites} 人访问世界")
            return True
        else:
            logger.warning(
                f"【{self.id}】{self.address} 只邀请了 {successful_invites} 人，未达到目标 {needed_invites} 人"
            )
            return False

    def _task11_3(self, tab):
        # return True
        start_ele = get_element(tab, "x://a[normalize-space()='here']", 10)
        if not start_ele:
            return False
        word_tab = start_ele.click.for_new_tab()
        sleep(10)
        word_tab.close()
        # get_element(word_tab,"x://button[normalize-space()='Connect']",10).click()

        pass

    def _task11_4(self, tab):
        w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

        balance = w3_manager.get_balance()
        if balance < 0.01:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        # 合约查询是否已经mint，有返回true
        nft_contract_address = "0x3da69Bbd46e7A9Ca151A930d0924DE9969D7562a"
        balance = w3_manager.get_balance_erc721(nft_contract_address, self.address)
        if balance > 0:
            logger.success("头像已经mint过了...")
            return True

        start_ele = get_element(
            tab,
            "x://p[contains(text(),'Mint your own avatar to use in the Somnia Playgrou')]//a[contains(text(),'Start here')]",
            10,
        )
        if not start_ele:
            return False
        laster_tab = start_ele.click.for_new_tab()

        laster_tab = self.page.get_tab(url="https://playground.somnia.network/avatar")

        for i in range(3):
            try:
                logger.info(f"【{self.id}】{self.address} 尝试第 {i + 1} 次加载网页")
                # 连接钱包
                self._connect_wallet(laster_tab)

                provider_loaded = get_element(laster_tab, "x://div[@class='loader-container ']", 30)

                if not provider_loaded:
                    logger.warning(f"【{self.id}】{self.address} 页面加载超时，正在刷新")
                    laster_tab.refresh()
                    sleep(5)
                    continue

                logger.info(f"【{self.id}】{self.address} 页面已加载完成")

                # get_element(word_tab,"x://button[normalize-space()='Connect']",10).click()
                self._connect_wallet(laster_tab)

                man_ele = get_element(
                    laster_tab,
                    "x://div[@class='create-new-avatar-gender active man']",
                    10,
                )
                women_ele = get_element(laster_tab, "x://div[@class='create-new-avatar-gender ']", 10)
                gender_choice = random.choice(["man", "woman"])
                if gender_choice == "man":
                    if man_ele:
                        man_ele.click()
                else:
                    if women_ele:
                        women_ele.click()

                logger.info(f"【{self.id}】 选择性别")
                create_avatar = get_element(
                    laster_tab,
                    "x://div[contains(@class,'create-new-avatar-footer')]//button[.//span[normalize-space()='Create New Avatar']]",
                    10,
                )
                if create_avatar:
                    create_avatar.click()

                logger.info(f"【{self.id}】 mint")
                nft_ele = get_element(
                    laster_tab,
                    "x://div[@class='mint-avatar-menu-item mint-update']//img[@src='/assets/img/nft-icon.svg'][1]",
                    10,
                )
                if nft_ele:
                    nft_ele.click()

                name_ele = get_element(laster_tab, "x://input[@id='name']", 10)
                if name_ele:
                    faker = Faker()
                    first_name = faker.first_name()
                    name_ele.input(vals=first_name, clear=True)
                mint_ele = get_element(laster_tab, "x://span[normalize-space()='Mint Avatar']", 10)

                if mint_ele:
                    mint_ele.click()
                    self.browser_controller.okx_wallet_connect()
                    sleep(2)
                    laster_tab.close()
            except Exception as e:
                logger.error(f"【{self.id}】执行创建徽章任务异常，error={str(e)}")
                continue

    def _task11_5(self):
        records = self.data.list()
        world_url = None
        for record in records:
            address = record.get("address")
            if address == self.address:
                continue

            world_url = record.get("world_url")
            if world_url:
                break

        if world_url:
            tab = None
            try:
                tab = self.page.new_tab(world_url)

                result = self._connect_wallet(tab)
                if not result:
                    return False

                input_ele = tab.ele("x://input[@placeholder='Type your message here...']", 120)
                if input_ele:
                    return True
                return False
            except Exception as e:
                logger.error(f"【{self.id}】{self.address} 访问世界失败: {str(e)}")
                return False
            finally:
                if tab:
                    tab.close()

        return False

    def _campaigns13(self):
        return self._task_campaigns(13, "odyssey_netherak_demons")

    def _campaigns14(self):
        return self._task_campaigns(14, "odyssey_onchain_gaming_frenzy")

    def _campaigns15(self):
        return self._task_campaigns(15, "odyssey_somnia_gaming_room")

    def _campaigns16(self):
        return self._task_campaigns(16, "odyssey_mullet_cop")

    def _campaigns17(self):
        return self._task_campaigns(17, "odyssey_intersection_of_defi")

    def _campaigns18(self):
        return self._task_campaigns(18, "odyssey_masks_of_the_void")

    def _campaigns19(self):
        return self._task_campaigns(19, "odyssey_socialfi_on_somnia")

    def _campaigns21(self):
        return self._task_campaigns(21, "odyssey_qrusader")

    def _campaigns23(self):
        return self._task_campaigns(23, "odyssey_ecosystem_on_the_horizon")

    def _campaigns25(self):
        w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

        balance = w3_manager.get_balance()
        if balance < 0.01:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        # 合约查询是否已经mint，有返回true
        nft_contract_address = "0x92A9207966971830270CB4886c706fdF5e98a38D"
        balance = w3_manager.get_balance_erc721(nft_contract_address, self.address)
        if balance == 0:
            result = w3_manager.send_transaction(
                to_address=nft_contract_address,
                value_in_wei=0,
                data="0x94bf804d00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000",
            )
            if result.get("success"):
                logger.success(f"【{self.id}】{self.address} 成功mint NFT")
            else:
                logger.error(f"【{self.id}】{self.address} 失败mint NFT")
                return False

        return self._task_campaigns(25, "odyssey_edition_nft_mint")

    def _campaigns26(self):
        return self._task_campaigns(26, "odyssey_somnia_yappers")

    def _campaigns36(self):
        if self.data.get(self.address).get("odyssey_nfts2_deploy1", "0") != "1":
            nft2 = NFTs2(self.browser_controller)
            result = nft2.task_create_edition()
            if not result:
                logger.error(f"【{self.id}】{self.address} 创建NFT失败")
                return False

        return self._task_campaigns(36, "odyssey_create_your_somniac_buddy")

    def _campaigns33(self):
        # nft2 = NFTs2(self.browser_controller)
        # result = nft2.task_create_edition()
        # if not result:
        #     logger.error(f"【{self.id}】{self.address} 创建NFT失败")
        #     return False

        return self._task_campaigns(33, "odyssey_gamers_lab")

    def _campaigns34(self):
        return self._task_campaigns(34, "odyssey_feed_the_dragon")

    def _campaigns40(self):
        return self._task_campaigns(40, "odyssey_somnia_horror")

    def _campaigns41(self):
        return self._task_campaigns(41, "odyssey_rubyscore")

    def _campaigns42(self):
        return self._task_campaigns(42, "odyssey_billion_quest")

    def _campaigns44(self):
        return self._task_campaigns(44, "odyssey_kazar_gaming_mayhem")

    def _campaigns45(self):
        return self._task_campaigns(45, "odyssey_hannonland_id")

    def _campaigns46(self):
        return self._task_campaigns(46, "odyssey_sparkball")

    def _campaigns47(self):
        return self._task_campaigns(47, "odyssey_sparkball_steamfest")

    def _campaigns48(self):
        return self._task_campaigns(48, "odyssey_play_sparkball")

    def _campaigns49(self):
        return self._task_campaigns(49, "odyssey_variance")

    def _campaigns50(self):
        return self._task_campaigns(50, "odyssey_sparkball_ignition")

    def _campaigns51(self):
        return self._task_campaigns(51, "odyssey_pixcape_games")

    def _campaigns53(self):
        w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

        balance = w3_manager.get_balance()
        if balance < 0.01:
            logger.error(f"【{self.id}】{self.address} 余额不足")
            return False

        # 合约查询是否已经mint，有返回true
        nft_contract_address = "0xA5bE101B3c7e61524B39d97105Ccb9D988Fa4632"

        balance = w3_manager.get_balance_erc721(nft_contract_address, self.address)
        if balance == 0:
            result = w3_manager.send_transaction(
                to_address=nft_contract_address,
                value_in_wei=w3_manager.to_wei(0.05),
                data="0x2db115440000000000000000000000000000000000000000000000000000000000000001",
            )
            if result.get("success"):
                logger.success(f"【{self.id}】{self.address} 成功mint NFT")
            else:
                logger.error(f"【{self.id}】{self.address} 失败mint NFT")
                return False

        return self._task_campaigns(53, "odyssey_handsnft")

    def _campaigns57(self):
        result = run_bigint_quest(self.browser_controller, self.id)
        if not result:
            logger.error(f"【{self.id}】{self.address} 执行bigint任务失败")
            return False

        part1 = result.get("Part1")
        part2 = result.get("Part2")

        # 检查每个 Part 是否所有类型都为 True
        def check_part_complete(part_data):
            """检查某个 Part 是否所有类型都完成"""
            if not part_data:
                return False
            # 检查是否所有值都为 True
            return all(part_data.values()) and len(part_data) == 4

        # 检查 Part1 是否完成
        part1_complete = check_part_complete(part1)
        # 检查 Part2 是否完成
        part2_complete = check_part_complete(part2)

        # 判断是否可以进行下一步
        if not part1_complete or not part2_complete:
            return False

        return self._task_campaigns(57, "odyssey_bigint")

    def _campaigns56(self):
        return self._task_campaigns(56, "odyssey_crazy_run")

    def execute(self):
        try:
            data = self.data.get(self.address)
            tasks = []
            # 检查任务是否需要执行
            # if (
            #         not data
            #         or not data.get("odyssey_share")
            #         or data["odyssey_share"] == "0"
            # ):
            #     tasks.append(self._campaigns7)

            #
            # if (
            #     not data
            #     or not data.get("odyssey_create_your_somniac_buddy")
            #     or data["odyssey_create_your_somniac_buddy"] == "0"
            # ):
            #     tasks.append(self._campaigns36)

            # if not data or not data.get("odyssey_somnia_horror") or data["odyssey_somnia_horror"] == "0":
            #     tasks.append(self._campaigns40)

            # if not data or not data.get("odyssey_rubyscore") or data["odyssey_rubyscore"] == "0":
            #     tasks.append(self._campaigns41)

            # if not data or not data.get("odyssey_billion_quest") or data["odyssey_billion_quest"] == "0":
            #     tasks.append(self._campaigns42)

            # if not data or not data.get("odyssey_kazar_gaming_mayhem") or data["odyssey_kazar_gaming_mayhem"] == "0":
            #     tasks.append(self._campaigns44)

            # if not data or not data.get("odyssey_sparkball") or data["odyssey_sparkball"] == "0":
            #     tasks.append(self._campaigns46)

            # if not data or not data.get("odyssey_sparkball_steamfest") or data["odyssey_sparkball_steamfest"] == "0":
            #     tasks.append(self._campaigns47)

            # if not data or not data.get("odyssey_variance") or data["odyssey_variance"] == "0":
            #     tasks.append(self._campaigns49)
            #

            # if not data or not data.get("odyssey_play_sparkball") or data["odyssey_play_sparkball"] == "0":
            #     tasks.append(self._campaigns48)

            if not data or not data.get("odyssey_hannonland_id") or data["odyssey_hannonland_id"] == "0":
                tasks.append(self._campaigns45)

            if not data or not data.get("odyssey_handsnft") or data["odyssey_handsnft"] == "0":
                tasks.append(self._campaigns53)

            if not data or not data.get("odyssey_crazy_run") or data["odyssey_crazy_run"] == "0":
                tasks.append(self._campaigns56)

            if not data or not data.get("odyssey_bigint") or data["odyssey_bigint"] == "0":
                tasks.append(self._campaigns57)

            # 随机打乱任务执行顺序
            random.shuffle(tasks)

            # 执行任务
            for task in tasks:
                task()

        except Exception as e:
            logger.error(f"【{self.id}】执行奥德赛任务异常，error={str(e)}")

    def check_state(self, id: int):
        try:
            tab = self.page.latest_tab

            tab.listen.start(f"https://quest.somnia.network/api/campaigns/{id}")
            tab.get(f"https://quest.somnia.network/campaigns/{id}")

            if not self._login():
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return False

            sleep(5)

            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 获取{type}任务失败，请检查网络")
                return False

            quests = res.response.body.get("quests", [])
            if not quests:
                logger.warning(f"【{self.id}】{self.address} {type} 任务列表为空")
                return True

            for quest in quests:
                title = quest.get("title")
                if quest.get("isParticipated"):
                    logger.success(f"【{self.id}】{self.address} {title} 任务已经完成")
                    self.data.update(self.address, {title: "1"})
                else:
                    logger.warning(f"【{self.id}】{self.address} {title} 任务未完成")

        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 执行 {type} 任务失败: {str(e)}")
            return False
