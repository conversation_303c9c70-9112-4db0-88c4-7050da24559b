import json
import os
import random
from datetime import datetime
from time import sleep

import click
from dotenv import load_dotenv
from loguru import logger
from w3_manager import Web3Manager

from config import DEFAULT_BROWSER_TYPE

# 使用完整的导入路径
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.proxies import Proxies
from src.utils.secure_encryption import SecureEncryption
from src.utils.thread_executor import ThreadExecutor
from src.wallets import OKXWallet

load_dotenv()

SOMNIA_RPC = os.getenv("SOMNIA_RPC")
PROXY_URL = os.getenv("PROXY_URL")

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/somnia_faucet_web.log", rotation="10MB", level="SUCCESS")


class FaucetWeb:
    def __init__(self, browser_controller: BrowserController, data_util: DataUtil):
        self.browser_controller = browser_controller
        self.id = browser_controller.id
        self.page = self.browser_controller.page
        self.data = data_util
        self.evm_private_key = self.get_private_key()
        self.user_agent = self.browser_controller.browser_config.user_agent
        self.address = self.browser_controller.browser_config.evm_address
        self._setting_proxy()

    def _setting_proxy(self):
        proxies = Proxies(self.browser_controller.browser_config.proxy)
        if proxies.verify():
            self.proxy = self.browser_controller.browser_config.proxy
        else:
            self.proxy = PROXY_URL
        logger.info(f"【{self.id}】使用代理: {self.proxy}")

    def get_private_key(self) -> str | None:
        pk = self.browser_controller.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def _connect_wallet(self, tab):
        logger.info(f"【{self.id}】 连接钱包")

        # 判断是否存在连接按钮
        wallet_element = get_element(tab, "x://button[contains(.,'0x')]", 5)
        if wallet_element:
            logger.info(f"【{self.id}】 钱包已连接")
            return True

        # 判断是否存在连接按钮
        button = get_element(tab, "x://button[.='Connect']", 5)
        if button:
            button.click()
            sleep(1)

            # 点击 OKX Wallet按钮
            get_element(tab, "x://button[contains(.,'OKX Wallet')]", 5).click()
            sleep(2)

            self.browser_controller.okx_wallet_connect()

        wallet_element = get_element(tab, "x://button[contains(.,'0x')]", 5)
        if wallet_element:
            logger.info(f"【{self.id}】 钱包完成连接")
            return True

        # 如果链接失败就断开okx中所有链接
        OKXWallet(self.browser_controller).disconnect_site_all()
        return False

    def faucet(self):
        # 获取最后领水时间, 不能低于 24 小时
        address_data = self.data.get(self.address)
        if address_data:
            last_faucet_date_time = address_data.get("faucet_last_date_time")
            if last_faucet_date_time:
                last_faucet_date_time = datetime.strptime(last_faucet_date_time, "%Y-%m-%d %H:%M:%S")
                # 检查是否在24小时内
                time_diff = datetime.now() - last_faucet_date_time
                if time_diff.total_seconds() < 24 * 3600:  # 24小时 = 24 * 3600秒
                    logger.info(
                        f"【{self.id}】 最后领水时间未到，还需等待 {24 - time_diff.total_seconds() / 3600:.1f} 小时"
                    )
                    return True

        w3_manager = Web3Manager(self.id, self.evm_private_key, SOMNIA_RPC, self.proxy, self.user_agent)

        balance = w3_manager.get_balance()
        if balance > 0.4:
            logger.info(f"【{self.id}】{self.address} 余额充足")
            return True

        for _ in range(3):
            try:
                # 链接页面
                url = "https://testnet.somnia.network/"
                tab = self.page.new_tab(url)
                if not self._connect_wallet(tab):
                    continue

                sleep(3)

                # 点击 Request Tokens
                get_element(tab, "x://button[.='Request Tokens']", 5).click()
                sleep(1)

                # 点击 Claim
                tab.listen.start("https://testnet.somnia.network/api/faucet")
                get_element(tab, "x://div[@role='dialog']//button[.='Get STT']", 5).click()
                sleep(1)

                res = tab.listen.wait(timeout=30)
                if not res:
                    logger.error(f"【{self.id}】 领水失败")
                    continue

                body = json.loads(res.response.raw_body)
                if body.get("success"):
                    logger.info(f"【{self.id}】 领水成功")
                    # 存储领水时间
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.data.update(self.address, {"faucet_last_date_time": current_time})
                    return True

                error = body.get("error")
                if "Bot detected" in error:
                    logger.error(f"【{self.id}】 领水失败: 主网余额不足0.001e")
                    return False

                if "Please wait 24 hours between requests" in error:
                    logger.info(f"【{self.id}】 领水失败: 已领取过")
                    return False

                return False

            except Exception as e:
                logger.error(f"【{self.id}】 领水失败: {e}")
                continue

        return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        failed_indices = []

        def process_task(index):
            try:
                browser = FaucetWeb(type, str(index))
                return browser.faucet()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_web-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/somnia_faucet_web.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
