# 自动化工具使用说明

## 1. 依赖安装

### 1.1 安装项目依赖

#### 环境要求

- Python 3.12.6 或更高版本
- pip 包管理器

#### 安装步骤

1. **克隆项目到本地**
   ```bash
   <NAME_EMAIL>:huhood/dp_common.git
   cd dp_common
   ```

2. **创建虚拟环境（推荐）**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate  # Linux/macOS
   # 或
   venv\Scripts\activate     # Windows
   ```

3. **安装项目依赖**
   ```bash
   # 安装基础依赖
   pip3 install -e .

   # 或者安装包含开发工具的完整依赖
   pip3 install -e ".[dev]"
   ```

4. **验证安装**
   ```bash
   # 检查关键依赖是否正确安装
   python3 check_dependencies.py
   ```

#### 使用Makefile（可选）

项目提供了Makefile来简化安装过程：

```bash
# 安装基础依赖
make install

# 安装开发依赖（包含Black、isort、pre-commit等）
make install-dev

# 格式化代码
make format

# 检查代码格式
make check

# 清理缓存文件
make clean
```

#### 依赖说明

项目包含以下主要依赖类别：

- **核心依赖**: loguru, click, requests等基础库
- **Web3相关**: web3, eth-account, eth-utils等区块链库
- **浏览器自动化**: DrissionPage, pyautogui, opencv-python等
- **数据处理**: pandas, numpy, beautifulsoup4等
- **加密相关**: cryptography, pycryptodome等
- **开发工具**: black, isort, pre-commit等（仅在dev依赖中）

#### 故障排除

如果遇到安装问题：

1. **确保Python版本正确**
   ```bash
   python3 --version  # 应该显示 3.12.6 或更高版本
   ```

2. **升级pip到最新版本**
   ```bash
   pip3 install --upgrade pip
   ```

3. **清理pip缓存**
   ```bash
   pip3 cache purge
   ```

4. **在macOS上可能需要安装额外的系统依赖**
   ```bash
   # 如果遇到编译错误，可能需要安装Xcode命令行工具
   xcode-select --install
   ```

## 2. 环境配置

### 2.1 配置文件设置

- 复制 `.env.copy` 为 `.env`，并填写以下配置信息：
    - OKX 钱包密码
    - icloud 邮箱
    - icloud 邮箱密码
    - 代理设置
    - 其他环境变量
- 配置 `browser_config.json` 文件
- 根据使用的浏览器类型，修改 `data` 目录下对应的 CSV 文件：
    - 删除文件名中的 `.copy` 后缀
    - 按格式填充必要数据

## 3. 钱包管理

### 3.1 批量生成钱包

```bash
python3 cli/evm.py g -n 1000 -e False
```

参数说明：

- `-n`: 生成钱包数量
- `-e`: 是否加密（True/False）

## 4. Twitter 账号管理

### 4.1 账号操作

```bash
# 登录账号
python3 x.py login -i 1

# 更新密码
python3 x.py up -i 1

# 更新邮箱
python3 x.py ue -i 1
```

参数说明：

- `-i`: 账号索引 ID

### 4.2 用户名操作

```bash
# 添加用户名后缀
python3 x.py aus -i 1 -s ꧁IP꧂

# 移除用户名后缀
python3 x.py rus -i 1 -s ꧁IP꧂

# 修改用户名
python3 x.py un -i 1
```

## 5. 数据加密功能

### 5.1 密码设置

```bash
# 设置加密密码
python3 cli/encrypt.py sp

# 导出密钥数据
python3 cli/encrypt.py ep -f "文件目录"
```

### 5.2 数据加解密

```bash
# 加密指定列数据
python3 cli/encrypt.py ec -c "列名"

# 解密指定列数据
python3 cli/encrypt.py dc -c "列名"

# 解密单条数据
python3 cli/encrypt.py d -t "密文"

# 加密单条数据
python3 cli/encrypt.py e -t "明文"
```

## 注意事项

1. 请确保所有配置文件的权限设置正确
2. 建议定期备份重要数据
3. 密码和密钥相关信息请妥善保管
4. 使用代理时请确保网络稳定性

## 常见问题

1. 如遇到权限问题，请检查文件权限设置
2. 配置文件格式错误会导致程序无法正常运行
3. 加密操作前请务必备份原始数据
