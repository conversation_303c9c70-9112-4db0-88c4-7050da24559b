import os
import sys
import time

import click
from loguru import logger

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dotenv import load_dotenv

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.config import get_browser_extension_id
from src.controllers import BrowserController
from src.utils.common import generate_username, parse_indices

# 加载.env文件
load_dotenv()

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


def _update_email(type, index):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        result = browser.update_email_x()
        if result:
            logger.success(f"{index} 更新X邮箱成功")
        else:
            logger.error(f"{index} 更新X邮箱失败")
        return result
    except Exception as e:
        logger.error(f"{index} 更新X邮箱失败: {e}")
        return False
    finally:
        browser.close_page()

def _update_password(type, index):
    try:
        browser = BrowserController(type, str(index))
        result = browser.update_password_x()
        if result:
            browser.close_page()
            logger.success(f"{index} 更新X密码成功")
        else:
            logger.error(f"{index} 更新X密码失败")
    except Exception as e:
        logger.error(f"{index} 更新X密码失败: {e}")


def _add_nickname_suffix_x(type, index, suffix):
    try:
        browser = BrowserController(type, str(index))
        result = browser.add_nickname_suffix_x(suffix)
        if result:
            browser.close_page()
            logger.success(f"{index} 添加X用户名后缀{suffix}成功")
        else:
            logger.error(f"{index} 添加X用户名后缀{suffix}失败")
    except Exception as e:
        logger.error(f"{index} 添加X用户名后缀{suffix}失败: {e}")


def _remove_nickname_suffix_x(type, index, suffix):
    try:
        browser = BrowserController(type, str(index))
        result = browser.remove_nickname_suffix_x(suffix)
        if result:
            browser.close_page()
            logger.success(f"{index} 移除X用户名后缀{suffix}成功")
        else:
            logger.error(f"{index} 移除X用户名后缀{suffix}失败")
    except Exception as e:
        logger.error(f"{index} 移除X用户名后缀{suffix}失败: {e}")


def _update_nickname_x(type, index, nickname):
    try:
        browser = BrowserController(type, str(index))
        result = browser.update_nickname_x(nickname)
        if result:
            browser.close_page()
            logger.success(f"{index} 修改X用户名为{nickname}成功")
        else:
            logger.error(f"{index} 修改X用户名为{nickname}失败")

    except Exception as e:
        logger.error(f"{index} 修改X用户名为{nickname}失败: {e}")


def _change_language_to_english_x(type, index):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        if browser.change_language_to_english_x():
            logger.success(f"{index} 切换X语言到英语成功")
            return True
        else:
            logger.error(f"{index} 切换X语言到英语失败")
            return False
    except Exception as e:
        logger.error(f"{index} 切换X语言到英语失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()


def _update_token(type, index):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        if browser.browser_config.x_is_suspended == "1":
            logger.error(f"{index} 更新推特token失败, 推特is suspended")
            return False
        browser.window_max()
        # 更改yescaptcha插件状态
        extension_id = get_browser_extension_id("yescaptcha", type)
        browser.chrome_extension_status(extension_id, True)
        result = browser.update_token()
        browser.chrome_extension_status(extension_id, False)
        if result:
            logger.success(f"{index} 更新X token成功")
            return True
        else:
            logger.error(f"{index} 更新X token失败")
            return False
    except Exception as e:
        logger.error(f"{index} 更新X token失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()


def _login_x(type, index, close=False, force=False):
    try:
        browser = BrowserController(type, str(index))
        browser.window_max()

        if force:
            browser.clear_site_data("x.com")
            browser.clear_site_data("twitter.com")

        # 更改yescaptcha插件状态
        extension_id = get_browser_extension_id("yescaptcha", type)
        browser.chrome_extension_status(extension_id, True)
        result = browser.login_x()
        browser.chrome_extension_status(extension_id, False)
        if result and close:
            browser.close_page()
            logger.success(f"{index} 登录X成功")
    except Exception as e:
        logger.error(f"{index} 登录X失败: {e}")


def _clear_data(type, index):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        config = browser.browser_config
        config.x_email = ""
        config.x_password = ""
        config.x_token = ""
        config.x_user = ""
        config.x_two_fa = ""
        config.x_email_password = ""
        config.x_proxy_email = ""
        config.x_is_suspended = ""
        browser._repository.update(config)
        browser.page
        browser.clear_site_data("x.com")
        browser.clear_site_data("twitter.com")
        logger.success(f"{index} 清除X数据成功")
        return True
    except Exception as e:
        logger.error(f"{index} 清除X数据失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()


def _setup_birthday(type, index):
    try:
        browser = BrowserController(type, str(index))
        result = browser.set_birthday()
        if result:
            browser.close_page()
            logger.success(f"{index} 设置x生日成功")
        else:
            logger.error(f"{index} 设置x生日失败")
    except Exception as e:
        logger.error(f"{index} 设置x生日失败: {e}")


def _setup_profile(type, index):
    browser = None
    try:
        avatar_path = os.getenv("X_AVATAR_PATH")
        header_image_path = os.getenv("X_HEADER_IMAGE_PATH")
        bio = None
        location = None
        browser = BrowserController(type, str(index))
        result = browser.setup_profile_x(avatar_path, header_image_path, bio, location)
        if result:
            logger.success(f"{index} 设置X个人资料成功")
        else:
            logger.error(f"{index} 设置X个人资料失败")
        return result
    except Exception as e:
        logger.error(f"{index} 设置X个人资料失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()



def _get_emails(type, index):
    try:
        browser = BrowserController(type, str(index))
        result = browser.get_emails_x()
        is_verified, email = _check_icloud_verified(result)
        if is_verified:
            browser.close_page()
            logger.success(f"{index} 获取X邮箱成功: {email}")
        else:
            logger.error(f"{index} 获取X邮箱失败")
    except Exception as e:
        logger.error(f"{index} 获取X邮箱失败: {e}")


def _check_icloud_verified(email_list):
    """
    检查邮箱列表中是否有已验证的icloud邮箱.

    Parameters
    ----------
        email_list: list

    Returns
    -------
        tuple: (bool, str)
    """
    for item in email_list:
        email = item.get("email", "").lower()
        is_verified = item.get("email_verified", False)

        # 检查是否是icloud邮箱且已验证
        if "@icloud.com" in email and is_verified:
            return True, email

    return False, None


def _get_2fa(type, index):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        result = browser.two_fa_x()
        if result:
            logger.success(f"{index} 开启推特2FA成功，2FA Key={result}")
            return True
        else:
            logger.error(f"{index} 开启推特2FA失败")
            return False
    except Exception as e:
        logger.error(f"{index} 开启推特2FA失败: {e}")
        return False
    finally:
        if browser:
            browser.close_page()


def _follow(type, index, username):
    browser = None
    try:
        browser = BrowserController(type, str(index))
        return browser.follow(username)
    except Exception as e:
        logger.error(f"{index} 关注X用户{username}失败: {e}")
        return False
    finally:
        if browser:
            try:
                browser.close_page()
            except Exception:
                pass


def _favorite(type, index, url, force=False):
    try:
        browser = BrowserController(type, str(index))
        result = browser.favorite(url, force)
        if result:
            browser.close_page()
            logger.success(f"{index} 点赞X推文 {url} 成功")
            return True
        else:
            logger.error(f"{index} 点赞X推文 {url} 失败")
            return False
    except Exception as e:
        logger.error(f"{index} 点赞X推文 {url} 失败: {e}")
        return False


def _retweet(type, index, url, force=False):
    try:
        browser = BrowserController(type, str(index))
        result = browser.retweet(url, force)
        if result:
            browser.close_page()
            logger.success(f"{index} 转发X推文 {url} 成功")
            return True
        else:
            logger.error(f"{index} 转发X推文 {url} 失败")
            return False
    except Exception as e:
        logger.error(f"{index} 转发X推文 {url} 失败: {e}")
        return False


def _favorite_and_retweet(type, index, url, force=False):
    try:
        browser = BrowserController(type, str(index))
        result = browser.favorite_and_retweet(url, force)
        if result:
            browser.close_page()
            logger.success(f"{index} {url} 点赞并转发X推文成功")
            return True
        else:
            logger.error(f"{index} {url} 点赞并转发X推文失败")
            return False
    except Exception as e:
        logger.error(f"{index} {url} 点赞并转发X推文失败: {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("login")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-c", "--close", is_flag=True,help="是否关闭浏览器")
@click.option("-f", "--force", is_flag=True,help="是否强制登录")
def login(index, type, close, force):
    indices = parse_indices(index)
    for _index in indices:
        _login_x(type, _index, close, force)


@cli.command("cd")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def clear_data(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        if not _clear_data(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"清除推特站点信息失败ID: {idxs}")


@cli.command("up")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_password(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _update_password(type, _index)
        except Exception as e:
            logger.error(f"{_index} 更新X密码失败: {e}")


@cli.command("ue")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_email(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        if not _update_email(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"更新X邮箱失败:{idxs}")



# 设置生日
@cli.command("sb")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-cl", "--change_language", is_flag=True, default=False, help="是否切换语言")
def setup_birthday(index, type, change_language):
    indices = parse_indices(index)
    for _index in indices:
        if change_language:
            _change_language_to_english_x(type, _index)
            time.sleep(5)
        _setup_birthday(type, _index)


@cli.command("sp")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def setup_profile(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        if not _setup_profile(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"设置个人资料失败: {idxs}")


@cli.command("aus")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-s", "--suffix", type=str, prompt="请输入用户名后缀", help="用户名后缀")
def add_nickname_suffix(index, type, suffix):
    indices = parse_indices(index)
    for _index in indices:
        _add_nickname_suffix_x(type, _index, suffix)


@cli.command("rus")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-s", "--suffix", type=str, prompt="请输入用户名后缀", help="用户名后缀")
def remove_nickname_suffix(index, type, suffix):
    indices = parse_indices(index)
    for _index in indices:
        _remove_nickname_suffix_x(type, _index, suffix)


@cli.command("un")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-s", "--suffix", type=str, prompt="请输入用户名后缀", help="用户名后缀")
def update_nickname(index, type, suffix):
    indices = parse_indices(index)
    for _index in indices:
        nickname = generate_username(digits=0) + suffix
        _update_nickname_x(type, _index, nickname)


@cli.command("cl")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def change_language_to_english(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result  = _change_language_to_english_x(type, _index)
        if not result:
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"切换语言失败: {idxs}")

# 获取token更新
@cli.command("token")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def token(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _update_token(type, _index)
        if not result:
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"更新X token失败ID: {idxs}")


@cli.command("emails")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def emails(index, type):
    indices = parse_indices(index)
    for _index in indices:
        _get_emails(type, _index)


@cli.command("2fa")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def two_fa(index, type):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _get_2fa(type, _index)
        if not result:
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"开启2FA失败ID: {idxs}")


@cli.command("follow")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--username", type=str, prompt="请输入用户名", help="用户名")
def follow(index, type, username):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _follow(type, _index, username)
        if not result:
            failed_indices.append(_index)

    if len(failed_indices) > 0:
        logger.error(f"关注X用户{username}失败IDS: {failed_indices}")


@cli.command("favorite")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--url", type=str, prompt="请输入推文URL", help="推文URL")
@click.option(
    "-f",
    "--force",
    is_flag=True,
    default=False,
    help="是否强制点赞, 如果点赞过先取消再点赞",
)
def favorite(index, type, url, force):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _favorite(type, _index, url, force)
        if not result:
            failed_indices.append(_index)

    if len(failed_indices) > 0:
        logger.error(f"点赞X推文{url}失败IDS: {failed_indices}")


@cli.command("retweet")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--url", type=str, prompt="请输入推文URL", help="推文URL")
@click.option(
    "-f",
    "--force",
    is_flag=True,
    default=False,
    help="是否强制转发, 如果转发过先取消再转发",
)
def retweet(index, type, url, force):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _retweet(type, _index, url, force)
        if not result:
            failed_indices.append(_index)

    if len(failed_indices) > 0:
        logger.error(f"转发X推文{url}失败IDS: {failed_indices}")


@cli.command("far")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--url", type=str, prompt="请输入推文URL", help="推文URL")
@click.option(
    "-f",
    "--force",
    is_flag=True,
    default=False,
    help="是否强制点赞并转发, 如果点赞过先取消再点赞, 如果转发过先取消再转发",
)
def favorite_and_retweet(index, type, url, force):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _favorite_and_retweet(type, _index, url, force)
        if not result:
            failed_indices.append(_index)

    if len(failed_indices) > 0:
        logger.error(f"点赞并转发X推文{url}失败IDS: {failed_indices}")


# 登录
# python3 cli/x.py login -i 1-10

# 更新邮箱
# python3 cli/x.py ue -i 1-10

# 添加用户名后缀
# python3 cli/x.py aus -i 1-10 -s ꧁IP꧂

# 移除用户名后缀
# python3 cli/x.py rus -i 1-10 -s ꧁IP꧂

# 修改用户名
# python3 cli/x.py un -i 1-10

# 切换语言到英语
# python3 cli/x.py cl -i 1-10

# 更新token
# python3 cli/x.py token -i 1-10

# 获取邮箱
# python3 cli/x.py emails -i 1-10

# 关注用户
# python3 cli/x.py follow -i 1-10 -u elonmusk

if __name__ == "__main__":
    cli()
    # _get_emails(BrowserType.ADS, "100")
    # _update_token(BrowserType.ADS, "24")
    # _favorite_and_retweet(BrowserType.BIT, "1", "https://x.com/MulletCopGame/status/1912536593144967388", True)

    # _login_x(BrowserType.ADS, 81)
    # _change_language_to_english_x(default_browser_type, 16)
    # _update_password(default_browser_type, 23)
    # _update_email(default_browser_type, 37)
    # _add_nickname_suffix_x(default_browser_type, 15, "꧁IP꧂")
    # _update_nickname_x(default_browser_type, 21, generate_username(digits=0))
    # _remove_nickname_suffix_x(default_browser_type, 17, "꧁IP꧂")
    # _setup_profile(BrowserType.ADS, "89")
    # _get_2fa(BrowserType.BIT, "20")
    # _setup_birthday(default_browser_type,"18")
    # _clear_data(BrowserType.ADS, "70")
