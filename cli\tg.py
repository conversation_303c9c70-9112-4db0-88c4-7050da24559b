from typing import List

import click
from loguru import logger
from src.utils.common import parse_indices
from src.controllers import BrowserController
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


def _update_password(type: BrowserType, index: int):
    browser = BrowserController(type, str(index))
    browser.update_password_telegram()

def _join_group(type: BrowserType, index: int, group_names: List[str]):
    browser = BrowserController(type, str(index))
    browser.join_channel(group_names)


@click.group()
def cli():
    pass


@cli.command("up")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_password(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            _update_password(type, _index)
        except Exception as e:
            logger.error(f"{_index} 更新X密码失败: {e}")

@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-n","--name",type=str,prompt="请输入群组名字", help="群组名字")
def join_group(index, type, name):
    indices = parse_indices(index)
    if "," in name:
        names = name.split(",")
    else:
        names = [name]
    for _index in indices:
        try:
            _join_group(type, _index, names)
        except Exception as e:
            logger.error(f"{_index} 更新X密码失败: {e}")


# 更新telegram密码
# python3 cli/tg.py up -i 1-10

# 更新telegram邮箱
# python3 cli/tg.py ue -i 1-10

# 更新telegram用户名
# python3 cli/tg.py un -i 1-10


# 加群
# python3 cli/tg.py join -t ads -i 1-10 -n linera_official


if __name__ == "__main__":
    cli()
