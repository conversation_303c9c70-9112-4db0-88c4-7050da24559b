from loguru import logger
import re

from .base import BaseWebMailClient
from src.repositories import CSVHotmailRepository
from time import sleep
from DrissionPage import Chromium


class HotmailWebClient(BaseWebMailClient):
    def __init__(self, page: Chromium, id: str):
        super().__init__(page, id)
        self.login_url = "https://outlook.live.com/owa/"

    @staticmethod
    def is_email_match(masked_email: str, full_email: str) -> bool:
        """
        检查带星号的邮箱是否与完整邮箱匹配

        Args:
            masked_email: 带星号的邮箱 (如 "ma*****@gmail.com")
            full_email: 完整邮箱 (如 "<EMAIL>")

        Returns:
            bool: 是否匹配
        """
        # 将星号转换为正则表达式
        pattern = masked_email.replace("*", ".").replace("+", "\\+")
        # 将正则中的点号替换为对应数量的 .+?
        star_count = masked_email.count("*")
        if star_count > 0:
            pattern = pattern.replace("." * star_count, ".+?")
        # 确保完全匹配
        pattern = f"^{pattern}$"

        return bool(re.match(pattern, full_email))

    @staticmethod
    def generate_new_password(old_password: str) -> str:
        """
        在原密码中随机插入1-3个#号
        Args:
            old_password: 原密码
        Returns:
            str: 新密码
        """
        import random

        new_password = list(old_password)
        num_hashtags = random.randint(1, 3)
        positions = random.sample(range(len(old_password)), num_hashtags)
        for pos in sorted(positions):
            new_password.insert(pos, "#")
        return "".join(new_password)

    def clear_cache(self) -> bool:
        try:
            # 这里有两个域名，删除2次
            self.browser.clear_site_data("microsoft.com")
            self.browser.clear_site_data("live.com")
            return True
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
            return False

    def login(self, email: str, password: str) -> bool:
        try:
            self.page.get(self.login_url)

            if not email or not password:
                logger.error(f"{self.id} 邮箱或密码为空")
                return False

            logger.info(f"登录邮箱: {email}")
            logger.info(f"登录密码: {password}")

            page = self.browser.page
            page.close_tabs(others=True)

            sleep(3)
            sign_in_button = page.ele(
                "x://a[@class='c-uhf-nav-link' and contains(@href, 'LinkID=2125442')]"
            )

            if not sign_in_button:
                logger.error(f"{self.browser.id} 未找到登录按钮")
                return False

            sign_in_button.click(True)
            sleep(3)
            lasted_tab = page.get_tab(0)
            email_input = lasted_tab.ele("x://input[@type='email']")
            if not email_input:
                logger.error(f"{self.browser.id} 未找到邮箱输入框")
                return False

            email_input.clear(True)
            email_input.input(email)
            sleep(1)
            email_input.clear(True)
            email_input.input(email)

            submit_button = lasted_tab.ele("x://button[@type='submit']")
            if not submit_button:
                logger.error(f"{self.browser.id} 未找到提交按钮")
                return False

            submit_button.click()
            sleep(3)

            password_input = lasted_tab.ele("x://input[@type='password']")
            if not password_input:
                logger.error(f"{self.browser.id} 未找到密码输入框")
                return False

            password_input.clear(True)
            password_input.input(password)
            sleep(1)
            password_input.clear(True)
            password_input.input(password)

            submit_button = lasted_tab.ele("x://button[@type='submit']")
            if not submit_button:
                logger.error(f"{self.browser.id} 未找到提交按钮")
                return False

            submit_button.click()
            ele = lasted_tab.ele("x://input[@type='email']", timeout=10)
            if ele:
                logger.warning(f"{self.browser.id} 需要保护账户, 请手动操作")
                return False

            sleep(10)
            if "https://outlook.live.com/mail/0/" in lasted_tab.url:
                logger.info(f"{self.browser.id} 登录成功: {email}")
                return True
            else:
                logger.error(f"{self.browser.id} 登录失败")
                return False

        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            return False

    def logout(self) -> bool:
        try:

            logger.info("登出成功")
            return True

        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            return False

    def change_password(self, email: str, password: str, secondary_email: str) -> bool:
        try:
            if not email or not password or not secondary_email:
                logger.error(f"{self.id} 邮箱或密码或备用邮箱为空")
                return False

            self.page.get(
                "https://account.live.com/password/change?refd=account.microsoft.com&fref=home.banner.changepwd"
            )

            page = self.page
            page.close_tabs(others=True)

            email_model = self._repository.get_by_browser_id(self.browser.id)
            if not email_model:
                logger.error(f"未找到邮箱配置: {self.browser.id}")
                return False

            logger.info(f"登录邮箱: {email}")
            logger.info(f"登录密码: {password}")
            logger.info(f"备用邮箱: {secondary_email}")

            new_password = self.generate_new_password(password)
            logger.info(f"新密码: {new_password}")

            page.ele("x://input[@type='password']").input(password)
            page.ele("x://button[@type='submit']").click()

            bind_email = page.ele("x://div[@data-bind='text: display']").text
            bind_email = bind_email.replace("Email ", "").replace("\u200e ", "")

            if not self.is_email_match(bind_email, secondary_email):
                logger.error(f"{self.browser.id} 备用邮箱不一致")
                return False

            logger.info("密码修改成功")
            return True

        except Exception as e:
            logger.error(f"修改密码失败: {str(e)}")
            return False

    def update_password(self, old_password: str, new_password: str) -> bool:
        try:
            if not self.is_logged_in:
                logger.error("未登录")
                return False

            logger.info("密码修改成功")
            return True

        except Exception as e:
            logger.error(f"修改密码失败: {str(e)}")
            return False

    def update_email(self, email: str) -> bool:
        pass
