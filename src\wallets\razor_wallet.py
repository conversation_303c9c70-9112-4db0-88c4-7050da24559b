from time import sleep
from typing import Optional

from loguru import logger
from retry import retry

from src.browsers.base_browser import BaseBrowser
from src.browsers.operations import try_click

from .base_wallet import BaseWallet, WalletType


class RazorWallet(BaseWallet):
    def __init__(self, browser: BaseBrowser):
        super().__init__(browser=browser)
        self.import_url = f"{self.home_url}account/initialize/import/mnemonic"


    def _import_account(self, last_tab, phrase, password):

        logger.info(f" ========== {self.id} 开始导入助记词 ========== ")

        service_btn = last_tab.ele("x://input[@id='terms-of-service']", timeout=3)
        if service_btn:
            service_btn.click()
            sleep(1)
        start_btn = last_tab.ele("x://button[.='Start']", timeout=3)
        if start_btn:
            start_btn.click()

        last_tab.ele("x://input[@name='name']").input(str(self.id))
        sleep(1)
        last_tab.ele("x://input[@name='mnemonic']").input(phrase)
        sleep(1)
        last_tab.ele("x://button[@type='submit']").click()
        sleep(1)
        last_tab.ele("x://input[@name='password']").input(password)
        sleep(1)
        last_tab.ele("x://input[@name='repeatPassword']").input(password)
        sleep(1)
        last_tab.ele("x://button[@type='submit']").click()

        ele = last_tab.ele("Congratulations", timeout=60)
        if ele:
            logger.success(f"========== {self.id} 完成导入助记词 ==========")
            return True
        else:
            logger.error(f"========== {self.id} 导入助记词失败 ==========")
            return False

    def login(self, password: str) -> bool:
        last_tab = self.page.new_tab(self.home_url)
        ele = last_tab.ele("x://input[@type='password']", timeout=3)
        if ele:
            ele.input(password)
            last_tab.ele("x://button[@type='submit']").click()

        ele = last_tab.ele("x://button[.='Coins']", timeout=3)
        if ele:
            logger.success(f"{self.id} 登录成功")
            return True
        else:
            logger.error(f"{self.id} 登录失败!")
            return False


    def setup(self, password: str, phrase: str) -> bool:
        last_tab = self.page.new_tab(self.import_url)

        ele = last_tab.ele("x://input[@type='password']", timeout=3)
        if ele:
            ele.input(password)
            last_tab.ele("x://button[@type='submit']").click()

            ele = last_tab.ele("x://button[.='Coins']", timeout=3)
            if ele:
                logger.success(f"{self.id} 已经导入过助记词并且已经登录...")
                return True
            else:
                logger.error(f"{self.id} 登录失败!")
                return False

        ele = last_tab.ele("x://button[.='Coins']", timeout=3)
        if ele:
            logger.success(f"{self.id} 已经导入过助记词并且已经登录...")
            return True

        return self._import_account(last_tab, phrase, password)

    def sign(self):
        pass

    def connect(self):
        pass

    @property
    def wallet_type(self) -> WalletType:
        return WalletType.RAZOR

    @property
    def home_url(self) -> str:
        return f"chrome-extension://{self.extension_id}/index.html#"
