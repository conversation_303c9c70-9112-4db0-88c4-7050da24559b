import os
import random
import re
import string
from typing import Optional


def generate_pwd(length: int = 20, is_contain_special=False) -> str:
    """生成指定长度的随机密码

    Args:
        length (int): 密码长度，建议不小于8位, 默认20
        is_contain_special (bool): 是否包含特殊字符

    Returns
    -------
        str: 包含大小写字母、数字和特殊字符的随机密码

    Raises
    ------
        ValueError: 如果密码长度小于8
    """
    if length < 8:
        raise ValueError("密码长度不应小于8位")

        # 定义字符集
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits

    # 合并所有字符集
    all_characters = lowercase + uppercase + digits

    # 确保密码包含至少一个小写字母、一个大写字母和一个数字
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits)
    ]

    if is_contain_special:
        special_chars = "~!@#$%^&*.="
        all_characters = all_characters + special_chars
        password.append(random.choice(all_characters))

    # 生成剩余的字符

    for _ in range(length - len(password)):
        password.append(random.choice(all_characters))

    # 打乱密码字符的顺序
    random.shuffle(password)

    # 将列表转换为字符串
    return "".join(password)


def generate_strong_password() -> str:
    """生成18位的强密码

    Returns
    -------
        str: 20位的强密码，包含大小写字母、数字和特殊字符
    """
    return generate_pwd(20, True)


def generate_new_password(original_password, min_hash=1, max_hash=3, hash_char="#"):
    """
    在原密码中随机位置插入1-n个#号

    Args:
        original_password: 原始密码
        min_hash: 最少插入#号的数量，默认1个
        max_hash: 最多插入#号的数量，默认3个

    Returns
    -------
        str: 新密码
    """
    # 确定要插入的#号数量
    hash_count = random.randint(min_hash, max_hash)

    # 将原密码转换为列表，方便插入
    password_list = list(original_password)

    # 随机选择不重复的插入位置
    insert_positions = random.sample(range(len(password_list) + 1), hash_count)
    insert_positions.sort(reverse=True)  # 从后向前插入，避免位置变化

    # 在选定位置插入#号
    for pos in insert_positions:
        password_list.insert(pos, hash_char)

    # 将列表转换回字符串
    new_password = "".join(password_list)

    return new_password


def generate_random_string(length: int = None) -> str:
    """生成随机字符串

    Args:
        length: 字符串长度，如果不指定则随机生成5-8位

    Returns
    -------
        str: 随机字符串
    """
    if length is None:
        length = random.randint(5, 8)
    characters = string.ascii_lowercase + string.digits
    return "".join(random.choice(characters) for _ in range(length))


def generate_specific_pwd(length: int) -> str:
    """生成指定长度的随机密码，只使用指定的特殊字符

    Args:
        length (int): 密码长度，建议不小于8位

    Returns
    -------
        str: 包含大小写字母、数字和指定特殊字符(~!@#$%^&*.=)的随机密码

    Raises
    ------
        ValueError: 如果密码长度小于8
    """
    if length < 8:
        raise ValueError("密码长度不应小于8位")

    # 使用指定的特殊字符
    special_chars = "~!@#$%^&*.="
    password_characters = string.ascii_letters + string.digits + special_chars

    while True:
        password = "".join(random.choice(password_characters) for _ in range(length))
        # 确保密码包含所有必要的字符类型
        if (
            any(char.islower() for char in password)
            and any(char.isupper() for char in password)
            and any(char.isdigit() for char in password)
            and any(char in special_chars for char in password)
        ):
            break

    return password
