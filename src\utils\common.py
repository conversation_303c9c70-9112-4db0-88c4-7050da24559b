import os
import random
import re
import string
from datetime import datetime

from faker import Faker
from pypinyin import lazy_pinyin

faker = Faker()


def get_project_root_path() -> str:
    """获取项目根目录的绝对路径.

    通过查找 pyproject.toml 文件来定位项目根目录

    Returns
    -------
        str: 项目根目录的绝对路径

    Raises
    ------
        FileNotFoundError: 如果找不到 pyproject.toml 文件
    """
    current_path = os.path.abspath(__file__)
    while True:
        current_path = os.path.dirname(current_path)
        # 查找项目标志文件
        if os.path.exists(os.path.join(current_path, "pyproject.toml")):
            return current_path
        # 防止无限循环（到达系统根目录时）
        if current_path == os.path.dirname(current_path):
            raise FileNotFoundError("无法找到项目根目录，请确保存在 pyproject.toml 文件")


def generate_username(min_length: int = 6, digits: int = 3) -> str:
    """生成随机用户名.

    Args:
        min_length (int, optional): 用户名最小长度. 默认为 6
        digits (int, optional): 随机数字位数. 默认为 3
    Returns:
        str: 由faker生成的用户名加上1-3位随机数字组成的字符串
    """
    username = faker.user_name()
    while len(username) < min_length:
        username = faker.user_name()

    random_string = "".join(random.choices(string.digits, k=digits))
    return username[:18] + random_string


def generate_email(name, domain):
    """
    根据输入的人名生成对应的邮箱地址.

    参数:
    name (str): 输入的人名
    domain (str): 邮箱后缀

    返回:
    str: 生成的邮箱地址
    """
    # 移除所有空格
    name = name.replace(" ", "")

    # 检查是否为中文名
    if re.search(r"[\u4e00-\u9fff]", name):
        # 如果是中文名，转换为拼音
        pinyin = lazy_pinyin(name)
        email_name = "".join(pinyin).lower()
    else:
        # 如果是英文名，直接转换为小写
        email_name = name.lower()

    # 生成邮箱地址
    email = f"{email_name}@{domain}"

    return email


def generate_login_name(name, birth_date=None):
    """
    根据输入的人名和出生日期生成社交账号唯一用户名.

    参数:
    name (str): 输入的人名
    birth_date (str): 出生日期，格式为yyyy-MM-dd

    返回:
    str: 生成的社交账号唯一用户名。
    """
    # 移除所有空格
    name = name.replace(" ", "")

    if re.search(r"[\u4e00-\u9fff]", name):
        pinyin = lazy_pinyin(name)
        social_name = "".join(word.capitalize() for word in pinyin)
    else:
        social_name = "".join(word.capitalize() for word in re.findall(r"[A-Za-z]+", name))

    if birth_date:
        birth_year = birth_date[:4]
        social_name += birth_year

    return social_name


def parse_indices(input_string: str) -> list[int]:
    """解析输入字符串为索引列表.

    支持以下格式:
    - 单个数字: "1" -> [1]
    - 逗号分隔: "1,2,3" -> [1,2,3]
    - 范围表示: "1-3" -> [1,2,3]

    Args:
        input_string (str): 要解析的输入字符串

    Returns
    -------
        list[int]: 解析后的索引列表

    Raises
    ------
        ValueError: 当输入为空时抛出
    """
    if not input_string:
        raise ValueError("输入不能为空")

    input_string = input_string.strip()
    if "," in input_string:
        integer_indices = [int(x) for x in input_string.split(",")]
        return integer_indices
    elif "-" in input_string:
        strings = input_string.split("-")
        start = int(strings[0])
        end = int(strings[1])
        return list(range(start, end + 1))
    else:
        return [int(input_string)]


def generate_email_content(
    name, total_count, success_count, failed_count, failed_records, success_records=None, success_fields=None
):
    """
    生成任务执行报告邮件内容.

    Args:
        name (str): 报告名称
        total_count (int): 总记录数
        success_count (int): 成功数量
        failed_count (int): 失败数量
        failed_records (list): 失败记录列表
        success_records (list, optional): 成功记录列表
        success_fields (dict, optional): 成功记录要显示的字段，格式为 {字段名: 显示名称}

    Returns
    -------
        str: HTML格式的邮件内容
    """
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 高兼容性HTML邮件模板 - 使用表格布局，内联样式
    email_content = f"""
<html>
<body style="font-family: Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.4; color: #333333;">
  <div style="max-width: 600px; margin: 0 auto;">
    <h2 style="font-size: 16px; margin: 10px 0 5px 0;">{name} 任务执行报告</h2>
    <p style="margin: 0 0 10px 0;">报告时间：{current_time}</p>

    <p style="font-weight: bold; margin: 15px 0 5px 0;">📊 统计概览</p>
    <table cellpadding="5" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 15px;">
      <tr style="background-color: #f2f2f2;">
        <th style="text-align: center; font-weight: normal;">总记录数</th>
        <th style="text-align: center; font-weight: normal;">成功数量</th>
        <th style="text-align: center; font-weight: normal;">失败数量</th>
        <th style="text-align: center; font-weight: normal;">成功率</th>
      </tr>
      <tr>
        <td style="text-align: center;">{total_count}</td>
        <td style="text-align: center; color: green;">{success_count}</td>
        <td style="text-align: center; color: red;">{failed_count}</td>
        <td style="text-align: center;">{f"{(success_count / total_count * 100):.1f}%" if total_count > 0 else "0%"}</td>
      </tr>
    </table>
"""

    # 先添加失败记录表格（如果有）
    if failed_records:
        email_content += """
    <p style="font-weight: bold; margin: 15px 0 5px 0;">❌ 失败记录</p>
    <table cellpadding="5" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 15px;">
      <tr style="background-color: #f2f2f2;">
        <th style="text-align: center; font-weight: normal;">序号</th>
        <th style="text-align: center; font-weight: normal;">浏览器类型</th>
        <th style="text-align: center; font-weight: normal;">账号索引</th>
      </tr>
"""
        for idx, record in enumerate(failed_records, 1):
            email_content += f"""
      <tr>
        <td style="text-align: center;">{idx}</td>
        <td style="text-align: center;">{record.get("type", "unknown")}</td>
        <td style="text-align: center;">{record.get("index", "unknown")}</td>
      </tr>
"""
        email_content += """
    </table>
"""

    # 然后添加成功记录表格（如果有记录且指定了字段）
    if success_records and len(success_records) > 0 and success_fields:
        email_content += """
    <p style="font-weight: bold; margin: 15px 0 5px 0;">✅ 成功记录</p>
    <table cellpadding="5" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 15px;">
      <tr style="background-color: #f2f2f2;">
"""
        # 动态生成表头
        for field_name in success_fields.values():
            email_content += f'<th style="text-align: center; font-weight: normal;">{field_name}</th>'

        email_content += """
      </tr>
"""
        # 动态生成表格内容
        for record in success_records:
            email_content += """
      <tr>
"""
            for field_key in success_fields:
                value = record.get(field_key, "-")
                email_content += f'<td style="text-align: center;">{value}</td>'

            email_content += """
      </tr>
"""

        email_content += """
    </table>
"""

    email_content += """
    <p style="color: #666666; font-size: 12px; margin-top: 15px; border-top: 1px solid #eeeeee; padding-top: 5px;">此报告自动生成，请勿回复</p>
  </div>
</body>
</html>
"""
    return email_content
