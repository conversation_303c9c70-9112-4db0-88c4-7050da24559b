import os
from time import sleep, time
from typing import List, Optional, Union

import pyautogui
from DrissionPage._elements.chromium_element import ChromiumElement
from DrissionPage._pages.chromium_frame import ChromiumFrame
from loguru import logger


def wait_for_condition(
    func, timeout: int, interval: float = 1.0
) -> ChromiumElement | ChromiumFrame | list[ChromiumElement] | list[ChromiumFrame] | None:
    """
    通用等待函数，用于等待元素出现或条件满足

    Args:
        func: 需要执行的函数
        timeout: 超时时间(秒)
        interval: 检查间隔时间(秒)

    Returns
    -------
        返回查找到的元素或None
    """
    start_time = time()
    while time() - start_time < timeout:
        result = func()
        if result:
            return result
        sleep(interval)
    return None


def get_element(tab, condition: str, timeout: int = 3) -> ChromiumElement | None:
    """
    获取单个元素.

    Args:
        tab: 浏览器标签页对象
        condition: 元素定位条件
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的元素，如果未找到返回None
    """
    return wait_for_condition(lambda: tab(condition, timeout=1), timeout)


def get_elements(tab, condition: str, timeout: int = 3) -> list[ChromiumElement]:
    """
    获取多个元素

    Args:
        tab: 浏览器标签页对象
        condition: 元素定位条件
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的元素列表，如果未找到返回空列表
    """
    return wait_for_condition(lambda: tab.eles(condition, timeout=1), timeout) or []


def get_frame(tab, frame_id: int, timeout: int = 3) -> ChromiumFrame | None:
    """
    获取框架元素

    Args:
        tab: 浏览器标签页对象
        frame_id: 框架ID
        timeout: 超时时间(秒)，默认3秒

    Returns
    -------
        返回找到的框架元素，如果未找到返回None
    """
    return wait_for_condition(lambda: tab.get_frame(frame_id, timeout=1), timeout)


def get_screen_scale():
    """
    获取屏幕缩放比例，支持 Windows 和 macOS
    """
    try:
        if os.name == "nt":  # Windows
            try:
                from win32con import LOGPIXELSX
                from win32gui import GetDC
                from win32print import GetDeviceCaps

                dc = GetDC(None)
                dpi = GetDeviceCaps(dc, LOGPIXELSX)
                return dpi / 96.0
            except ImportError:
                logger.warning("未安装 pywin32，将使用默认缩放比例 1.0")
                return 1.0
        else:  # macOS
            import subprocess

            cmd = "system_profiler SPDisplaysDataType | grep Resolution"
            output = subprocess.check_output(cmd, shell=True).decode()
            resolution = output.split(":")[1].split("(")[0].strip()
            width = int(resolution.split("x")[0].strip())
            if width >= 3840:  # 4K
                return 2.0
            elif width >= 2560:  # 2K
                return 1.5
            return 1.0
    except Exception as e:
        logger.error(f"获取屏幕缩放比例失败: {str(e)}")
        return 1.0


# 查找并点击目标图片
# 如果点击无效，请检查权限：
# - 安全性与隐私 -> 隐私 -> 辅助功能
# - 安全性与隐私 -> 隐私 -> 屏幕录制
def click_on_image(image_path):
    """
    查找并点击目标图片
    :param image_path: 目标图片路径
    """
    location = get_image_location(image_path)
    scale = get_screen_scale()
    if location:
        # 计算图片中心点坐标
        center_x = location.left + (location.width / 2)
        center_y = location.top + (location.height / 2)

        # 根据屏幕缩放比例调整坐标
        if os.name == "nt":  # Windows
            adjusted_x = int(center_x)
            adjusted_y = int(center_y)
        else:
            # 根据屏幕缩放比例调整坐标
            adjusted_x = int(center_x / scale)
            adjusted_y = int(center_y / scale)

        # 移动并点击
        pyautogui.moveTo(adjusted_x, adjusted_y, duration=0.1)
        pyautogui.click()
        return True

    return False


def get_image_location(image_path):
    """
    获取图片位置
    """
    if not os.path.exists(image_path):
        logger.error(f"错误：图片文件不存在: {image_path}")
        return

    try:
        # 检查是否安装了 OpenCV
        import cv2

        has_opencv = True
    except ImportError:
        has_opencv = False
        logger.warning("OpenCV 未安装，将使用精确匹配模式")

    try:
        if has_opencv:
            # 使用 OpenCV 时的匹配逻辑
            confidence_levels = [0.9, 0.8, 0.7, 0.6]
            for conf in confidence_levels:
                try:
                    location = pyautogui.locateOnScreen(image_path, confidence=conf, grayscale=True)
                    if location:
                        return location
                except pyautogui.ImageNotFoundException:
                    continue
                except Exception as e:
                    logger.error(f"匹配失败 (置信度 {conf}): {str(e) or '未找到匹配图像'}")
        else:
            # 不使用 OpenCV 时的精确匹配
            try:
                location = pyautogui.locateOnScreen(image_path, grayscale=True)
                if location:
                    return location
            except Exception as e:
                logger.error(f"精确匹配失败: {str(e) or '未找到匹配图像'}")
    except Exception as e:
        logger.error(f"图片匹配过程发生错误: {str(e)}")

    return None
