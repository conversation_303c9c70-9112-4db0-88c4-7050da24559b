import json
import os
import random
import re
from datetime import datetime
from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, try_click
from src.controllers import Browser<PERSON>ontroller
from src.enums.browsers_enums import BrowserType
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/soul.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.environ.get("PROXY_URL")
DEFAULT_HEADERS = ["index", "type", "address", "referral_code", "referral_by_code", "seeds", "last_seeds",
                   "seeds_update_time", "rank"]


class SoulProtocol:
    def __init__(self, browser_type: BrowserType, id: str, data_util: DataUtil):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def is_referral_by(self) -> bool:
        data = self.data_util.get(self.address)
        referral_by_code = data.get("referral_by_code", "")
        return len(referral_by_code) > 0

    def get_referral_by_code(self) -> str:
        try:
            path = os.path.join(os.path.dirname(__file__), "referral_codes.txt")
            with open(path) as f:
                referral_codes = [line.strip() for line in f.readlines()]
            return random.choice(referral_codes) if referral_codes else ""
        except Exception as e:
            logger.error(f"【{self.id}】 获取邀请码失败: {str(e)}")
            return ""

    @staticmethod
    def is_login(tab):
        # return get_element(tab, "x:(//button[.//span[contains(text(), '0x')]])[2]", 5)
        return get_element(tab, "x://button[.//span[contains(text(), '0x')]]", 20)

    def login(self):
        referral_code = ""
        for _i in range(3):
            tab = None
            try:
                url = "https://app.soul.io/seeds/quests"
                if not self.is_referral_by():
                    referral_code = self.get_referral_by_code()
                if referral_code:
                    url = f"https://app.soul.io/?referredBy={referral_code}"
                    logger.info(f"【{self.id}】使用邀请码 {referral_code} 登录")

                tab = self.page.new_tab(url)
                sleep(10)
                if self.is_login(tab):
                    logger.success(f"【{self.id}】登录登录成功")
                    return True

                connect_btn = get_element(tab, "x://span[text()='Connect Wallet']/ancestor::button", 10)
                if connect_btn and connect_btn.states.has_rect:
                    connect_btn.click()
                    sleep(2)

                okx_wallet_btn = get_element(tab, "x://div[text()='OKX Wallet']/ancestor::button", 10)
                if okx_wallet_btn:
                    okx_wallet_btn.click()
                    sleep(2)
                    self.browser_controller.okx_wallet_connect()

                sign_btn = get_element(tab, "x://div[@id='modal-root']//section//button[.//span[text()='SIGN']]", 10)
                if sign_btn and sign_btn.states.has_rect:
                    sign_btn.click()
                    sleep(2)
                    self.browser_controller.okx_wallet_sign()

                if self.is_login(tab):
                    logger.success(f"【{self.id}】登录登录成功")
                    if not self.data_util.get(self.address).get("referral_by_code", ""):
                        self.data_util.update(self.address, {"referral_by_code": referral_code})
                    return True

            except Exception as e:
                logger.error(f"【{self.id}】登录异常, error={str(e)}")
            self.page.close_tabs(tab)
            logger.warning(f"【{self.id}】第 {_i + 1} 次登录失败, retry...")
        logger.error(f"【{self.id}】尝试登录3次后仍失败")
        return False

    def get_referral_code(self) -> bool:
        self.browser_controller.window_max()
        self.browser_controller.okx_wallet_login()
        try:
            lasted_tab = self.page.latest_tab
            lasted_tab.listen.start("https://app.soul.io/seeds/referral")
            lasted_tab.get("https://app.soul.io/seeds/referral")
            if not self.is_login(lasted_tab):
                self.login()
                lasted_tab.get("https://app.soul.io/seeds/referral")
            for packet in lasted_tab.listen.steps(count=10):
                req = packet.request.postData
                res = packet.response.body
                if not isinstance(req, list) or self.address not in req:
                    continue
                if res is None:
                    continue
                if "personalSummary" in res:
                    json_data = self._parse_response(res)
                    referral_code = json_data.get("personalSummary", {}).get("code", "")
                    rank = json_data.get("personalSummary", {}).get("rank", "-1")
                    self.data_util.update(self.address, {"referral_code": referral_code, "rank": rank})
                    logger.success(f"【{self.id}】邀请码获取成功: {referral_code}")
                    return True
            logger.error(f"【{self.id}】邀请码获取失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】获取邀请码发生异常, error={str(e)}")
            return False
        # finally:
        #     if self.browser_controller:
        #         self.browser_controller.close_page()

    def parse_quests(self):
        lasted_tab = self.page.latest_tab
        lasted_tab.listen.start(targets="https://app.soul.io/seeds/quests", method="POST")
        lasted_tab.get("https://app.soul.io/seeds/quests")
        for packet in lasted_tab.listen.steps():
            req = packet.request.postData
            res = packet.response.body
            if not isinstance(req, list) or self.address not in req:
                continue
            if res is None:
                continue
            if "questsWithStatus" in res:
                continue
            logger.debug(f"【{self.id}】请求数据: {req}, 响应数据: {res}")
            return self._parse_response(res)
        return {}

    def _parse_response(self, text):
        try:
            # 使用正则表达式提取以 "1:" 开头的部分
            match = re.search(r"1:(\{.*})", text, re.DOTALL)
            if not match:
                raise ValueError("未找到以 '1:' 开头的 JSON 对象")

            # 获取匹配到的 JSON 字符串
            json_str = match.group(1)

            # 解析 JSON 字符串为 Python 字典
            target_data = json.loads(json_str)

            # 验证解析结果是否为字典
            if not isinstance(target_data, dict):
                logger.error(f"【{self.id}】字符串不是有效的JSON对象：{text}")
                return {}

            return target_data
        except Exception as e:
            logger.error(f"【{self.id}】解析结果出错，, error={str(e)}")
            return {}

    def _update_seeds(self, seeds):
        record = self.data_util.get(self.address)
        last_seeds = record.get("seeds", "0")
        new_seeds = int(seeds) + int(last_seeds)
        seeds_update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.data_util.update(self.address,
                              {"seeds": new_seeds, "last_seeds": last_seeds, "seeds_update_time": seeds_update_time})

    def _connect_x(self):
        try:
            latest_tab = self.page.latest_tab
            latest_tab.set.window.max()
            if "i/oauth2/authorize" not in latest_tab.url:
                xpath = ("x://p[contains(text(), 'Connect Your X Account.')]/parent::div/following-sibling::div["
                         "4]//button[.//span[text()='Connect']]")
                connect_btn = get_element(latest_tab, xpath, 5)
                connect_btn.click()
                sleep(3)
                latest_tab = self.page.latest_tab

            auth_btn = get_element(latest_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not auth_btn:
                if "i/flow/login" in latest_tab.url:
                    #todo 推特登录
                    pass
                logger.error(f"【{self.id}】绑定推特失败, 未找到Authorization Button")
                return False

            latest_tab.listen.start("https://app.soul.io/api/auth/session")
            auth_btn.click()
            res = latest_tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.id}】绑定推特失败，点击Authorization Button无响应")
                return False
            # x_username = self.browser_controller.browser_config.x_username
            # if x_username and x_username in res.response.body:
            if res.response.body.get("username", ""):
                logger.success(f"【{self.id}】绑定推特成功成功")
                return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】绑定推特异常, error={str(e)}")
            return False

    def connect_discord(self):
        try:
            latest_tab = self.page.latest_tab
            latest_tab.set.window.max()
            if "discord.com/oauth2/authorize" not in latest_tab.url:
                xpath = ("x://p[contains(text(), 'Connect Your Discord Account.')]/parent::div/following-sibling::div["
                         "4]//button[.//span[text()='Connect']]")
                connect_btn = get_element(latest_tab, xpath, 5)
                connect_btn.click()
                sleep(3)
                latest_tab = self.page.latest_tab

            auth_btn = get_element(latest_tab, "x:(//button)[2]", 5)
            if not auth_btn:
                # todo discord是否登录
                logger.error(f"【{self.id}】绑定Discord失败, 未找到Authorization Button")
                return False

            latest_tab.listen.start("https://app.soul.io/api/auth/session")
            auth_btn.click()
            res = latest_tab.listen.wait(timeout=90)
            if not res or not res.response or not res.response.body:
                logger.error(f"【{self.id}】绑定Discord失败，点击Authorization Button无响应")
                return False
            # dc_username = self.browser_controller.browser_config.dc_user
            # if dc_username and dc_username in res.response.body:
            if res.response.body.get("username", ""):
                logger.success(f"【{self.id}】绑定Discord成功")
                return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】绑定Discord异常，error={str(e)}")
            return False

    def connect_telegram(self):
        try:
            latest_tab = self.page.latest_tab
            if "t.me" not in latest_tab.url:
                xpath = ("x://p[contains(text(), 'Connect Your Telegram Account And Join The "
                         "Community.')]/parent::div/following-sibling::div[4]//button[.//span[text()='Connect']]")
                connect_btn = get_element(latest_tab, xpath, 5)
                connect_btn.click()
                sleep(3)

            xpath = ("x://div[contains(@class, 'flex items-center justify-center text-[12px]') and contains(., "
                     "'Your verification code')]/input")
            code_i = get_element(latest_tab, xpath, 5)
            code = code_i.value

            new_tab = self.page.new_tab("https://t.me/soul_verification_bot")
            logger.success(f"【{self.id}】请手动输入telegram验证码：verify {code}")
            sleep(300)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】绑定Telegram异常, error={str(e)}")
            return False

    def x_follow(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特关注成功（假任务）")
        return True

    def x_like(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特点赞成功（假任务）")
        return True

    def x_retweet(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特转发成功（假任务）")
        return True

    def x_comment(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特评论成功（假任务）")
        return True

    def x_task(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特任务成功（假任务）")
        return True

    def join_discord(self):
        sleep(2)
        latest_tab = self.page.latest_tab
        if "discord.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】discord加群成功")
        return True

    def _claim_tokens(self):
        try:
            latest_tab = self.page.latest_tab
            xpath = ("x://p[contains(text(), 'Claim Testnet Tokens To Perform The Next "
                     "Tasks.')]/parent::div/following-sibling::div[4]//button[.//span[text()='Faucet']]")
            claim_btn = get_element(latest_tab, xpath, 5)
            claim_btn.click()
            sleep(2)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】领水异常, error={str(e)}")
            return False

    def _supply(self, chain_id, protocol):
        pass

    def _borrow(self, chain_id, protocol):
        pass

    def _add_collateral(self, chain_id, protocol):
        pass

    def _remove_collateral(self, chain_id, protocol):
        pass

    def _redeem(self, chain_id, protocol):
        pass

    def _repay_borrow(self, chain_id, protocol):
        pass

    def _execute(self, quest_name: str, quest_type: str, protocol: str, chain_id: str, operation: str):
        try:
            logger.info(f"【{self.id}】开始执行任务：{quest_name}")
            chain_execute_option = {
                "supply": self._supply,
                "redeem": self._redeem,
                "borrow": self._borrow,
                "repayBorrow": self._repay_borrow,
                "addCollateral": self._add_collateral,
                "removeCollateral": self._remove_collateral,
            }
            social_execute_option = {
                "CONNECT_X": self._connect_x,
                "SOCIAL_X_FOLLOW": self.x_follow,
                "SOCIAL_X_RETWEET": self.x_retweet,
                "SOCIAL_X_COMMENT": self.x_comment,
                "SPECIAL_X": self.x_task,
                "CONNECT_TELEGRAM": self.connect_telegram,
                "CONNECT_DISCORD": self.connect_discord,
                "SOCIAL_DISCORD": self.join_discord
            }
            if quest_type == "ON_CHAIN":
                logger.info(f"【{self.id}】链上操作暂未实现")
                return chain_execute_option[operation](chain_id, protocol)
            elif "SOCIAL" in quest_type or "CONNECT" in quest_type or quest_type == "SPECIAL_X":
                return social_execute_option[quest_type]()
            elif quest_type == "CLAIM_TOKENS":
                return self._claim_tokens()
            else:
                logger.warning(f"【{self.id}】暂不支持的任务类型：{quest_type}")
                return False
        except Exception as e:
            logger.error(f"【{self.id}】执行任务 {quest_name} 发生异常, error={str(e)}")
            return False

    def quest(self):
        # 暂时3次
        for _ in range(3):
            try:
                quest = self.parse_quests()
                if not quest:
                    logger.info(f"【{self.id}】未发现可执行任务")
                    return True

                quest_name = quest.get("description", "")
                seeds = quest.get("seeds", "0")
                quest_type = quest.get("type", "")
                protocol = quest.get("protocol", "")
                chain_id = quest.get("chainId", "")
                operation = quest.get("operation", "")

                latest_tab = self.page.latest_tab
                btn_in_progress = get_element(latest_tab, "x://button[contains(@class, 'QuestItemBtn_isInProgress')]",
                                              5)
                if not btn_in_progress:
                    return False
                btn_in_progress.click()

                # 外层必须用单引号
                access_xpath = (f'x://p[contains(text(), "{quest_name}")]/parent::div/following-sibling::div['
                                f'4]//button[.//span[text()="Access App"]]')
                access_click = get_element(latest_tab, access_xpath, 5)
                if access_click:
                    access_click.click()

                self._execute(quest_name, quest_type, protocol, chain_id, operation)
                sleep(3)

                if "app.soul.io/seeds/quests" not in latest_tab.url:
                    latest_tab.close()
                    latest_tab = self.page.latest_tab

                latest_tab.listen.start("https://app.soul.io/api/referral/validateMission")
                verify_xpath = (f'x://p[contains(text(), "{quest_name}")]/parent::div/following-sibling::div['
                                f'4]//button[.//span[text()="Verify"]]')
                verify_click = get_element(latest_tab, verify_xpath, 5)
                if not verify_click:
                    logger.warning(f"【{self.id}】未找到任务 {quest_name} 的Verify Button")
                    continue

                verify_click.click()
                res = latest_tab.listen.wait(timeout=90)
                if not res or not res.response or not res.response.body:
                    logger.warning(f"【{self.id}】任务 {quest_name} 验证无响应")
                    continue

                message = res.response.body.get("message", "")
                success = res.response.body.get("success", False)
                if not success:
                    logger.warning(f"【{self.id}】任务: {quest_name} 验证不通过, {message}")
                    sleep(5)
                    continue

                logger.success(f"【{self.id}】任务: {quest_name} 完成, 增加 {seeds}seeds")
                self._update_seeds(seeds)
            except Exception as e:
                logger.error(f"【{self.id}】执行任务出错, error={str(e)}")

    def task(self):
        self.browser_controller.window_max()
        self.browser_controller.okx_wallet_login()
        try:
            if not self.login():
                return False
            self.quest()
            return True
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
            return False


def _get_referral_code(browser_type, index):
    try:
        soul = SoulProtocol(browser_type, str(index))
        return soul.get_referral_code()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False


def _execute_task(browser_type, index, data_util):
    try:
        soul = SoulProtocol(browser_type, str(index), data_util)
        return soul.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("code")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def get_referral_code(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []
        failed_indices = []

        def process_task(index):
            result = _get_referral_code(type, index)
            if result:
                successful_indices.append(index)
            else:
                failed_indices.append(index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=1200,  # 20分钟超时
            retries=3,
            interval=10,
            task_name=f"Soul-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [
            str(index) for index in failed_indices
        ]
        logger.success(
            f"本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个")
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        data_dir = os.path.join(get_project_root_path(), "examples", "soul")
        csv_path = os.path.join(data_dir, f"soul_{type.lower()}.csv")
        data_util = DataUtil(csv_path, DEFAULT_HEADERS)

        def process_task(index):
            result = _execute_task(type, index, data_util)
            if result:
                successful_indices.append(index)
            else:
                failed_indices.append(index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Soul_{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [
            str(index) for index in failed_indices
        ]
        logger.success(
            f"本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个")
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击


if __name__ == "__main__":
    cli()
    # _execute_task(BrowserType.MORE, "1")
    # _get_referral_code(BrowserType.MORE, "2")
