import click
import random
import os
from retry import retry
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import <PERSON>rowserController
from src.utils.common import parse_indices
from time import sleep
from datetime import datetime
from src.utils.element_util import get_element
from src.utils.thread_executor import ThreadExecutor
from src.utils.hhcsv import HHCSV
from src.utils.browser_config import BrowserConfigInstance

from src.socials.discord_chat_bot import DiscordChatBot

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

PROXY_URL = os.getenv("PROXY_URL")

class Faucet(BrowserConfigInstance):

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.channel_id = "1361269130296033441"

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        evm_address = self.browser_config.evm_address
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not evm_address or not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(self.channel_id, f"{evm_address}"):
            raise Exception("发送faucet消息失败")
        return True


@click.group()
def cli():
    pass



@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def faucet(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(id):
            try:
                return Faucet(type, id).task()
            except Exception as e:
                logger.error(f"账号 {id} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=120,  # 2分超时
            retries=3,
            interval=10,
            task_name=f"Monad-FAUCET-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    cli()
