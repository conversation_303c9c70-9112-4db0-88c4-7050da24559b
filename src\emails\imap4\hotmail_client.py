import imaplib

import requests
from loguru import logger

from ..exceptions import MailAuthError, MailConnectionError, MailOperationError
from .imap_client import IMAPClient, MailConfig


class HotmailClient(IMAPClient):
    """Hotmail/Outlook 邮件客户端

    专门处理 Hotmail/Outlook 邮箱的 IMAP 客户端，继承自基础的 IMAPClient。
    提供了 Hotmail/Outlook 特定的配置和功能。
    """

    SUPPORTED_DOMAINS = ("hotmail.com", "outlook.com", "live.com", "msn.com")
    IMAP_SERVER = "outlook.live.com"

    DEFAULT_CLIENT_ID = "dbc8e03a-b00c-46bd-ae65-b683e7707cb0"  # 默认使用第一个client_id

    def __init__(self, email: str, password: str):
        """初始化 Hotmail/Outlook 邮件客户端

        Args:
            email: 邮箱地址
            password: 如果是普通密码，则使用密码认证；如果是OAuth2令牌，则使用OAuth2认证
            client_id_key: 使用的client_id的键名，可选值："out1" 或 "out2"
        """
        valid_domains = ("@hotmail.com", "@outlook.com", "@live.com", "@msn.com")
        if not email.endswith(valid_domains):
            raise ValueError("邮箱地址必须是 Hotmail/Outlook 邮箱（@hotmail.com、@outlook.com、@live.com 或 @msn.com）")
        # 兼容第二批DEFAULT_CLIENT_ID不一致，写在token后面
        split_result = password.split("---")
        if len(split_result) == 2:
            password, client_id_key = split_result
        else:
            password = split_result[0]
            client_id_key = None  # 或赋予默认值
        config = MailConfig(
            email=email,
            password=password,
            imap_server=self.IMAP_SERVER,
        )
        super().__init__(config)
        # 检查密码是否为 OAuth2 令牌格式
        if password and len(password) > 100:
            self.is_oauth_token = True
            self.oauth_token = password
        else:
            self.is_oauth_token = False

        # 设置client_id
        self.client_id = client_id_key if client_id_key else self.DEFAULT_CLIENT_ID

    def set_client_id(self, client_id: str):
        """设置客户端ID"""
        self.client_id = client_id

    def connect(self):
        """连接并登录到 Hotmail/Outlook 邮箱服务器

        根据初始化时提供的认证类型，使用相应的方式连接到服务器：
        1. 如果设置了 OAuth2 令牌，则使用 OAuth2 认证
        2. 否则使用基础的用户名/密码认证

        Returns
        -------
            self: 返回自身用于支持 with 语句

        Raises
        ------
            MailConnectionError: 连接失败
            MailAuthError: 认证失败
        """
        if self.is_oauth_token:
            try:
                self._connect_oauth2(self.oauth_token)
                return self
            except Exception as e:
                error_msg = f"Hotmail/Outlook OAuth2 连接失败: {str(e)}"
                logger.error(error_msg)
                raise MailConnectionError(error_msg) from e
        else:
            # 使用基类的普通连接方法
            return super().connect()

    def list_folders(self) -> list[str]:
        """获取邮箱中所有可用的文件夹列表并打印"""
        try:
            self._validate_connection()
            result, folder_list = self.server.list()

            if result != "OK":
                raise MailOperationError("获取文件夹列表失败")

            folders = []
            for folder_info in folder_list:
                if not folder_info:
                    continue
                try:
                    folder_name = folder_info.decode()
                    folder_name = folder_name.split('"')[-1].strip()
                    folders.append(folder_name)
                except Exception as e:
                    logger.warning(f"解析文件夹名称失败: {str(e)}")
                    continue

            return folders
        except Exception as e:
            logger.error(f"获取文件夹列表失败: {str(e)}")
            return []

    def login(self) -> bool:
        """登录 Hotmail/Outlook 邮箱

        根据初始化时提供的密码类型，自动选择认证方式：
        1. 如果密码是 OAuth2 令牌，使用 OAuth2 认证
        2. 否则使用普通密码认证

        Returns
        -------
            bool: 登录是否成功
        """
        try:
            if self.is_oauth_token:
                # 使用 OAuth2 令牌进行连接和认证
                self._connect_oauth2(self.oauth_token)
                return True
            else:
                # 使用普通密码认证
                return super().login()
        except MailAuthError as e:
            error_msg = (
                "Hotmail/Outlook 认证失败，请检查：\n"
                "1. 密码或令牌是否正确\n"
                "2. 如果开启了两步验证，请使用应用密码或 OAuth2 令牌\n"
                "3. 账户是否被锁定或需要额外验证\n"
                "管理安全选项：https://account.live.com/proofs/Manage"
            )
            logger.error(error_msg)
            raise MailAuthError(error_msg) from e
        except Exception as e:
            logger.error(f"Hotmail 登录失败: {str(e)}")
            raise

    def _connect_oauth2(self, refresh_token):
        """使用 OAuth2 连接到 Hotmail/Outlook 邮箱服务器

        Args:
            access_token (str): OAuth2 访问令牌

        Returns
        -------
            bool: 连接是否成功

        Raises
        ------
            MailConnectionError: 连接失败
        """
        try:
            if self.server:
                try:
                    self.server.logout()
                except:
                    pass

            # 使用日志记录详细的连接信息
            logger.debug(f"尝试连接到服务器: {self.config.imap_server}")
            self.server = imaplib.IMAP4_SSL(self.config.imap_server)

            # 使用 OAuth2 认证
            # 参照示例代码的方式传递认证字符串
            email_name = self.config.email
            access_res = self._get_access_token(refresh_token)
            if access_res[0]:
                # 这里不预先生成auth_string，而是在lambda中生成
                self.server.authenticate("XOAUTH2", lambda x: self._generate_auth_string(email_name, access_res[1]))
                logger.info(f"已使用 OAuth2 连接到 {self.config.imap_server}")
                self.server.select("inbox")  # 选择收件箱
                # mail.select('Junk')  #选择垃圾箱
                result, data = self.server.search(None, "ALL")
                self.server.fetch("1", "(RFC822)")
                return True
            else:
                print(access_res[1])

        except Exception as e:
            if self.server:
                try:
                    self.server.logout()
                except:
                    pass
            self.server = None
            error_msg = f"OAuth2 连接到 {self.config.imap_server} 失败: {str(e)}"
            logger.error(error_msg)
            raise MailConnectionError(error_msg) from e

    def _get_access_token(self, refresh_token):
        """获取 Microsoft OAuth2 访问令牌

        使用 refresh_token 和 client_id 获取新的访问令牌

        Returns
        -------
            tuple: (成功标志, 访问令牌或错误消息)
        """
        url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
        data = {
            "client_id": self.client_id,
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
        }

        try:
            response = requests.post(url, data=data)
            result = response.json()

            if "error" in result:
                error_msg = f"获取访问令牌失败: {result.get('error')}"
                logger.error(error_msg)
                return False, f"邮箱状态异常：{result.get('error')}"

            new_access_token = result.get("access_token")
            return True, new_access_token
        except Exception as e:
            logger.error(f"获取访问令牌异常: {str(e)}")
            return False, f"获取令牌异常: {str(e)}"

    def _generate_auth_string(self, email_name, access_token):
        """生成 OAuth2 认证字符串

        Args:
            email_name (str): 邮箱地址
            access_token (str): OAuth2 访问令牌

        Returns
        -------
            str: 格式化的认证字符串
        """
        auth_string = f"user={email_name}\1auth=Bearer {access_token}\1\1"
        return auth_string

    def set_oauth2_credentials(self, client_id=None, refresh_token=None):
        """设置 OAuth2 认证凭据

        Args:
            client_id (str, optional): 应用客户端 ID，不提供则使用默认值
            refresh_token (str): OAuth2 刷新令牌
        """
        if client_id:
            self.client_id = client_id
        if refresh_token:
            self.refresh_token = refresh_token
        logger.info(f"已设置 OAuth2 认证凭据，client_id: {self.client_id[:8]}...")

    def refresh_oauth2_token(self):
        """刷新 OAuth2 令牌并重新认证

        使用存储的刷新令牌获取新的访问令牌，并重新连接邮箱

        Returns
        -------
            bool: 刷新并重新认证是否成功
        """
        if not hasattr(self, "client_id") or not hasattr(self, "refresh_token"):
            logger.error("缺少 OAuth2 凭据，请先调用 set_oauth2_credentials")
            return False

        # 记录详细的请求信息以便调试
        logger.debug(f"尝试刷新令牌，使用client_id: {self.client_id[:8]}...")

        success, result = self._get_access_token()
        if not success:
            logger.error(f"刷新令牌失败: {result}")
            return False

        try:
            # 记录访问令牌的一部分用于调试
            logger.debug(f"获得新令牌，前10个字符: {result[:10]}...")
            self._connect_oauth2(result)
            logger.success("OAuth2 令牌刷新并重新认证成功")
            return True
        except Exception as e:
            logger.error(f"使用新令牌重新认证失败: {str(e)}")
            return False

    def _execute_search(self, search_criteria: str) -> list[bytes]:
        """执行搜索操作"""
        try:
            encoded_criteria = search_criteria.encode("utf-8")
            result, data = self.server.search(None, encoded_criteria)

            if result != "OK":
                raise MailOperationError("搜索邮件失败")

            if not data or not data[0]:
                return []

            return data[0].split()
        except Exception as e:
            logger.error(f"执行搜索操作失败: {str(e)}")
            raise MailOperationError(f"执行搜索操作失败: {str(e)}") from e
