import os
import random
import re
import string
import time
from collections.abc import Mapping
from datetime import datetime
from threading import Lock
from time import sleep

import click
import requests
from faker import Faker
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.api.twitter.twitter import Twitter
from src.browsers import BROWSER_TYPES
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.socials import X
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element, get_elements
from src.utils.password import generate_pwd
from src.utils.thread_executor import ThreadExecutor
from src.utils.yescaptcha import YesCaptcha

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/linera.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.environ.get("PROXY_URL")

TypeAlias = Mapping[str, str | bytes | None]


class Linera:
    # 创建类级别的锁
    _csv_lock = Lock()

    def __init__(self, browser_type: BrowserType, id: str, data_util: DataUtil):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()


    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _generate_random_string(self):
        # 定义字符串长度范围（5-8）
        length = random.randint(5, 8)
        # 字母和数字的字符集
        characters = string.ascii_letters + string.digits
        # 随机选择字符生成字符串
        random_string = "".join(random.choice(characters) for _ in range(length))
        return random_string

    def _get_captcha_token(self, page) -> str | None:
        return YesCaptcha.get_web_plugin_token_hcaptcha(page, self.id)

    def _get_email_verify_link(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd, proxy_email)

            search_criteria = SearchCriteria(
                # subject="drops",
                from_addr="email.drops.house",
                # to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]

            pattern = r'https://app\.drops\.house/email-verification\?token=[^\s<>"]+'

            # 查找匹配
            match = re.search(pattern, email["content"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _get_email_opt_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Sign in to your wallet. Your code is",
                from_addr="<EMAIL>",
                to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            # 修改正则表达式，添加捕获组
            pattern = r"(?<=Your code is[ :])(\d{6})"  # 添加括号创建捕获组

            # 查找匹配
            match = re.search(pattern, email["subject"])
            if match:
                # 获取验证码并转换为小写
                verification_code = match.group(1).lower()
                return verification_code  # 返回转换为小写的验证码
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=2):
        """统一处理输入框操作"""
        try:
            input_element = get_element(tab, xpath, timeout=10)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=10, sleep_time=3):
        """统一处理点击操作."""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def login(self):
        register = self.data_util.get(self.address)
        if not register or not register.get("status") or register["status"] == "0":
            logger.error(f"【{self.id}】 未找到注册信息, 请先注册")
            return False

        for _ in range(3):
            tab = self.page.latest_tab
            if "https://drops.linera.io/home" not in tab.url:
                tab = self.page.new_tab("https://drops.linera.io/home")
                sleep(5)
            try:
                if not try_click(tab, "x://span[normalize-space()='Login']", timeout=10, id=self.id):
                    logger.error(f"【{self.id}】点击Login失败")
                    sleep(3)
                    tab.close()
                    continue

                if not input_text(tab, "x://input[@placeholder='Enter email address']", register["email"]):
                    logger.error(f"【{self.id}】输入邮箱失败")
                    sleep(3)
                    tab.close()
                    continue
                sleep(1)
                if not input_text(tab, "x://input[@placeholder='Enter password']", register["password"]):
                    logger.error(f"【{self.id}】输入密码失败")
                    sleep(3)
                    tab.close()
                    continue
                sleep(1)
                tab.listen.start("https://api.getdrops.co/v1/auth/email/login")
                try_click(tab, "x://span[normalize-space()='Log me in']", timeout=10, id=self.id)
                res = tab.listen.wait(timeout=90)
                if not res or not res.response:
                    logger.error(f"【{self.id}】登录失败， 网络超时无响应")
                    continue
                if res.response.body.get("token", ""):
                    logger.success(f"【{self.id}】登录成功")
                    self.data_util.update(self.address, {"error_code": ""})
                    return True
                if res.response.body.get("error_code", ""):
                    code = res.response.body.get("error_code")
                    logger.error(f"【{self.id}】登录失败, {code}")
                    self.data_util.update(self.address, {"error_code": code})
                    return False
            except Exception as e:
                logger.error(f"【{self.id}】 登录失败{str(e)}")
                continue
        return False

    def get_referral_codes(self):
        try:
            path = os.path.join(os.path.dirname(__file__), "referral_codes.txt")
            with open(path) as f:
                return [line.strip() for line in f.readlines()]
        except Exception as e:
            logger.error(f"【{self.id}】 获取邀请码失败: {str(e)}")
            return []

    def get_referral_code(self):
        referral_codes = self.get_referral_codes()
        if not referral_codes:
            logger.error(f"【{self.id}】 未找到邀请码, 请检查 referral_codes.txt 文件")
            return False
        return random.choice(referral_codes)

    def register(self):
        """注册linera 账号."""
        try:
            register = self.data_util.get(self.address)
            if register and register.get("status") == "1":
                logger.info(f"【{self.id}】钱包 {self.address} 已注册,跳过注册流程")
                return True

            referral_code = self.get_referral_code()
            if not referral_code:
                logger.error(f"【{self.id}】 未找到邀请码, 请检查 referral_codes.txt 文件")
                return False

            email = self.browser_controller.browser_config.email
            if not email:
                logger.error(f"【{self.id}】 未找到邮箱, 请检查 data/ads|bit|chrome.csv 文件")
                return False

            _password = register.get("password")
            password = _password if _password else generate_pwd()
            update_data = {
                "email": email,
                "password": password,
            }
            if self.data_util.update(self.address, update_data):
                logger.success(
                    f"【{self.id}】注册开始, 写入数据: address={self.address}, email={email}, password={password}")
            else:
                logger.error(f"【{self.id}】注册开始, 写入数据失败, address={self.address}, email={email}, password={password}")

            tab = self.page.new_tab(f"https://drops.linera.io/invite?code={referral_code}&ext_id=5oqo4TUSG")
            if not tab.wait.ele_displayed("x://span[normalize-space()='Login']", timeout=20):
                logger.error(f"【{self.id}】 可能已经注册过")
                return False
            sleep(5)
            if not try_click(tab, "x://span[normalize-space()='Login']", timeout=10, id=self.id):
                logger.error(f"【{self.id}】 点击login失败")
                return False
            sleep(5)

            if not try_click(tab, "x://span[normalize-space()='Sign up']", timeout=10, id=self.id):
                logger.error(f"【{self.id}】 点击Sign up失败")
                return False

            if not input_text(tab, "x://input[@placeholder='Enter email address']", email, timeout=10):
                logger.error(f"【{self.id}】 输入邮箱失败")
                return False

            if not input_text(tab, "x://input[@placeholder='Enter password']", password, timeout=10):
                logger.error(f"【{self.id}】 输入密码失败")
                return False

            if not input_text(tab, "x://input[@placeholder='Confirm password']", password, timeout=10):
                logger.error(f"【{self.id}】 输入确认密码失败")
                return False

            tab.listen.start("https://api.getdrops.co/v1/auth/email/signup")
            try_click(tab, "x://span[normalize-space()='Sign up with Email']")
            res = tab.listen.wait(timeout=90)
            if not res or not res.response:
                logger.error(f"【{self.id}】注册失败， 网络超时无响应")
                return False
            error_code = res.response.body.get("error_code", "")
            if error_code:
                logger.error(f"【{self.id}】注册失败, {error_code}")
                return False

            logger.success(
                f"【{self.id}】注册成功, 注册信息: address={self.address}, email={email}, password={password}, referral_code={referral_code}")
            update_data = {
                "referral_code": referral_code,
                "status": 1,
            }

            if self.data_util.update(self.address, update_data):
                logger.success(f"【{self.id}】注册成功, 更新状态成功")
            else:
                logger.error(f"【{self.id}】注册成功, 更新状态失败")

            try:
                name = Faker().name()
                input_text(tab, "x://input[@placeholder='Enter username']", name, timeout=10)
                sleep(2)
                try_click(tab, "x://span[normalize-space()='Continue']")
            except Exception as e:
                logger.error(f"【{self.id}】注册成功, 更新用户名失败, error={str(e)}")

            return True

        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            return False

    def x_login(self):
        x = X(self.id, self.page)
        return x.login_by_user_auth_app(
            self.browser_controller.browser_config.x_user,
            self.browser_controller.browser_config.x_password,
            self.browser_controller.browser_config.x_two_fa,
            self.browser_controller.browser_config.x_email,
            self.browser_controller.browser_config.x_email_password,
            self.browser_controller.browser_config.x_proxy_email,
        )

    def add_dc(self, lasted_tab):
        check_dc = lasted_tab.ele("x://button[normalize-space()='Connect Discord']", timeout=5)
        if check_dc:
            check_dc.next().click()
            lasted_tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
            lasted_tab.set.window.max()
            lasted_tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
            allow_btn = get_element(lasted_tab, "x:(//button)[2]", 5)
            if allow_btn:
                allow_btn.click()
                sleep(10)
            sleep(10)
            return True

    def _extract_points_and_step_ids(self, data):
        try:
            total_points_earned = data.get("points_earned", 0)

            campaign_step_ids = []
            step_points_earned = []

            for step in data.get("steps_submission", []):
                campaign_step_ids.append(step.get("campaign_step_id"))
                step_points_earned.append(step.get("points_earned", 0))

            return {
                "total_points_earned": total_points_earned,
                "campaign_step_ids": campaign_step_ids,
                "step_points_earned": step_points_earned,
            }
        except Exception as e:
            logger.error(f"【{self.id}】 提取任务状态失败: {e}")
            return {}

    def task_status(self):
        for _ in range(3):
            try:
                headers = self._get_auth_headers()
                if not headers:
                    logger.error(f"【{self.id}】未获取到auth headers, 可能用户未登录")
                    continue

                proxy = self.browser_controller.browser_config.proxy or PROXY_URL
                url = "https://api.getdrops.co/v1/user/submission?campaign_id=125&with_steps=true"
                filtered_headers = self._convert_and_filter_headers(headers)
                proxies = {
                    "http": proxy,
                    "https": proxy,
                }
                response = requests.request(
                    method="GET",
                    url=url,
                    headers=filtered_headers,
                    proxies=proxies,
                    timeout=30,
                )
                result = self._extract_points_and_step_ids(response.json())
                if result:
                    return result
            except Exception as e:
                logger.error(f"【{self.id}】获取任务状态失败, error={str(e)}")
                continue
        return {}

    def get_referral_code_from_request(self):
        try:
            headers = self._get_auth_headers()
            if not headers:
                logger.error(f"【{self.id}】 未获取到auth headers, 可能用户未登录")
                return None

            url = "https://api.getdrops.co/v1/user/referral_code?campaign_id=125"
            filtered_headers = self._convert_and_filter_headers(headers)

            proxy = self.browser_controller.browser_config.proxy or PROXY_URL
            proxies = {
                "http": proxy,
                "https": proxy,
            }

            response = requests.request(
                method="GET",
                url=url,
                headers=filtered_headers,
                proxies=proxies,
                timeout=30,
            )
            return response.json().get("referral_code", None)
        except Exception as e:
            logger.error(f"{self.id} 获取referral_code 失败: {e}")
            return None

    def task_connect_x(self, lasted_tab):
        if self.data_util.get(self.address).get("connect_x", "0") == "1":
            return True

        sleep(5)
        result = try_click(lasted_tab, "x://p[normalize-space()='Entry']")
        if not result:
            logger.error(f"【{self.id}】 点击Entry按钮失败")
            return False

        ele = lasted_tab.ele("x://a[.='OPEN TWITTER']", timeout=10)
        if ele:
            logger.success(f"【{self.id}】 Twitter已连接")
            self.data_util.update(self.address, {
                "connect_x": "1"
            })
            return True

        x_ele = get_element(lasted_tab, "x://span[normalize-space()='Connect Twitter']", timeout=10)
        if x_ele:
            result = try_click(lasted_tab, "x://span[normalize-space()='Connect Twitter']")
            if not result:
                logger.error(f"【{self.id}】 点击Twitter连接按钮失败")
                return False

            tab = lasted_tab.wait.url_change(text="i/flow/login", timeout=10)
            if tab:
                result = self.browser_controller.login_x_with_auth()
                if not result:
                    logger.error(f"【{self.id}】 登录Twitter失败")
                    return False

            lasted_tab.wait.url_change(text="i/oauth2/authorize", timeout=20)
            result = lasted_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            if not result:
                logger.error(f"【{self.id}】未找到Twitter授权按钮")
                return False

            allow_btn = get_element(lasted_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not allow_btn:
                logger.error(f"【{self.id}】未找到Twitter授权按钮")
                return False

            allow_btn.click()
            sleep(10)
            ele = lasted_tab.ele("x://a[.='OPEN TWITTER']", timeout=10)
            if ele:
                logger.success(f"【{self.id}】 连接Twitter成功")
                self.data_util.update(self.address, {
                    "connect_x": "1"
                })
                return True
            else:
                logger.error(f"【{self.id}】 连接Twitter失败")
                return False

    def task_1357(self):
        logger.info(f"【{self.id}】开始执行任务【1357】")
        tab = None
        try:
            tab = self.page.new_tab("https://drops.linera.io/home")
            tab.wait.ele_displayed("x://img[@alt='User avatar']", timeout=20)
            sleep(2)
            result = try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1357']")
            if not result:
                logger.error(f"【{self.id}】点击Entry按钮失败")
                return False

            update_ele = get_element(tab, "x://span[normalize-space()='Update']")
            if update_ele:
                update_ele.click()
                return True

            dc_ele = get_element(tab, "x://button//span[normalize-space()='Connect Discord']", timeout=6)
            if dc_ele:
                result = try_click(tab, dc_ele)
                if not result:
                    logger.error(f"【{self.id}】 点击Connect Discord按钮失败")
                    return False

                dc_tab = tab.wait.url_change(text="https://discord.com/login", timeout=10)
                if dc_tab:
                    self.browser_controller.login_discord()
                tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
                tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
                allow_btn = get_element(tab, "x:(//button)[2]", 5)
                if allow_btn:
                    allow_btn.click()
                    sleep(10)
                    linera_tab = tab.wait.url_change(text="https://drops.linera.io", timeout=10)
                    if not linera_tab:
                        return False

            tab.listen.start("https://api.getdrops.co/v1/step_submission/create/verify_discord_roles")
            result = try_click(tab, "x://button//p[normalize-space()='Submit']")
            if not result:
                logger.error(f"【{self.id}】 点击Submit按钮失败")
                return False

            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】 未收到响应")
                return False

            response_data = res.response.body
            if error_code := response_data.get("error_code"):
                logger.warning(f"【{self.id}】执行任务 1357 失败, {error_code}")
                # self.data_util.update(self.address, {
                #     "error_code":error_code
                # })
                return False

            return True
        except Exception as e:
            logger.error(f"【{self.id}】执行任务 1357 失败, error={str(e)}")
            return False
        finally:
            if tab:
                tab.close()

    def task_1358(self):
        return self.task_tweet(1358, " .@Linera_io is the alpha #microchains ")

    def _login_x(self, tab, tw_tab):
        if get_element(tw_tab, "x://a[@href='/login' and @role='link']", timeout=10):
            tw_tab.get(url="https://x.com")
            self.browser_controller.login_x()
            tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
            if tw_ele:
                try_click(tab, tw_ele)
                self.page.wait.new_tab()

                tw_tab = self.page.latest_tab
                tw_tab.set.window.max()

    def _post_tweet(self, tweet_text):
        tweet_url = None
        try:
            x_token = self.browser_controller.browser_config.x_token
            proxy = self.browser_controller.browser_config.proxy or PROXY_URL
            twitter_api = Twitter(self.id, x_token, proxy)
            twitter_api.start()
            tweet_url = twitter_api.post_tweet(tweet_text)
        except Exception as e:
            if "Authorization: Status is a duplicate. (187)" in str(e):
                logger.info(f"{self.id} Duplicate tweet. Trying to find original one")
                tweet_url = twitter_api.find_posted_tweet(lambda t: tweet_text.split("\n")[0] in t)
                if tweet_url is None:
                    raise Exception("Tried to post duplicate tweet. Can't find original one") from e
                logger.info(f"{self.id} Duplicate tweet found: {tweet_url}")
            else:
                raise Exception(f"Post tweet error: {str(e)}") from e
        logger.info(f"{self.id} Posted link tweet: {tweet_url}")
        return tweet_url

    def _retweet(self, post_id):
        try:
            x_token = self.browser_controller.browser_config.x_token
            proxy = self.browser_controller.browser_config.proxy or PROXY_URL
            twitter_api = Twitter(self.id, x_token, proxy)
            twitter_api.start()
            return twitter_api.retweet(post_id)
        except Exception as e:
            logger.error(f"【{self.id}】 点赞失败: {e}")
            return False

    def task_tweet(self, step_id, tweet_text):
        logger.info(f"【{self.id}】开始执行任务【{step_id}】")

        for i in range(3):
            tab = None
            try:
                tab = self.page.new_tab(f"https://drops.linera.io/create-entry?ext_id=5oqo4TUSG&stepId={step_id}")
                tab.wait.ele_displayed("x://img[@alt='User avatar']", timeout=20)
                sleep(2)

                data_item = self.data_util.get(self.address)
                post_url = data_item.get(f"{step_id}_tweet_url")
                if not post_url:
                    post_url = self._post_tweet(tweet_text)
                    self.data_util.update(self.address, {f"{step_id}_tweet_url": post_url})

                if not post_url:
                    logger.error(f"【{self.id}】提交任务【{step_id}】失败, 未找到推文")
                    return False

                input_ele = get_element(tab, "x://input[@placeholder='Enter the tweet link here']", timeout=10)
                input_ele.clear(True)
                input_ele.input(post_url)
                sleep(2)

                tab.listen.start("https://api.getdrops.co/v1/step_submission/create/tweet_with_phrase")
                if not try_click(tab, "x://button//p[normalize-space()='Submit']"):
                    logger.error(f"【{self.id}】提交任务【{step_id}】时点击Submit按钮失败")
                    continue

                res = tab.listen.wait(timeout=90)
                if not res or not res.response:
                    logger.error(f"【{self.id}】提交任务【{step_id}】网络无响应")
                    continue

                response_data = res.response.body
                status = response_data.get("status")
                if status == "success":
                    logger.success(f"【{self.id}】提交任务【{step_id}】成功")
                    return True
                else:
                    message = response_data.get("error_code")
                    logger.warning(f"【{self.id}】提交任务【{step_id}】失败, {message}")
                    self.data_util.update(self.address, {f"{step_id}_tweet_url": ""})
            except Exception as e:
                logger.error(f"【{self.id}】提交任务【{step_id}】异常, error={str(e)}")
            finally:
                if tab:
                    tab.close()
            logger.info(f"【{self.id}】第 {i + 1} 次提交任务【{step_id}】失败, retry...")
        return False

    def task_retweet(self, step_id, post_id):
        tab = None
        logger.info(f"【{self.id}】开始执行任务【{step_id}】")

        try:
            tab = self.page.new_tab(f"https://drops.linera.io/create-entry?ext_id=5oqo4TUSG&stepId={step_id}")

            # update_ele = get_element(tab, "x://span[normalize-space()='Update']")
            # if update_ele:
            #     update_ele.click()
            #     logger.success(f"【{self.id}】 已经完成了任务，点击更新")
            #     return True

            tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
            if tw_ele:
                result = try_click(tab, tw_ele)
                if not result:
                    logger.error(f"【{self.id}】 点击Open Twitter按钮失败")
                    return False
                sleep(3)
                self.page.latest_tab.close()

            # TODO: 假任务直接点提交, 以下代码不要删除
            # result = self._retweet(post_id)
            # if not result:
            #     logger.error(f"【{self.id}】 转发失败")
            #     return False
            tab.listen.start("https://api.getdrops.co/v1/step_submission/create")
            result = try_click(tab, "x://button//p[normalize-space()='Submit']")
            if not result:
                logger.error(f"【{self.id}】 点击Submit按钮失败")
                return False

            res = tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"【{self.id}】 未收到响应")
                return False

            response_data = res.response.body
            status = response_data.get("status")
            if status == "success":
                logger.success(f"【{self.id}】 领取成功")
                return True
            else:
                message = response_data.get("message")
                logger.warning(f"【{self.id}】领取异常, {message}")
                return False

        except Exception as e:
            logger.error(f"【{self.id}】 领取任务失败: {e}")
            return False
        finally:
            if tab:
                tab.close()

    def task_visit(self, step_id):
        logger.info(f"【{self.id}】开始执行任务【{step_id}】")

        tab = None
        try:
            tab = self.page.new_tab(f"https://drops.linera.io/create-entry?ext_id=5oqo4TUSG&stepId={step_id}")
            update_ele = get_element(tab, "x://span[normalize-space()='Update']")
            if update_ele:
                update_ele.click()
                logger.success(f"【{self.id}】 已经完成了任务，点击更新")
                return True

            a_ele = get_element(tab, "x://a[@rel]", timeout=10)
            if not a_ele:
                logger.error(f"【{self.id}】 未找到链接")
                return False

            new_tab = a_ele.click.for_new_tab()
            if not new_tab:
                logger.error(f"【{self.id}】 点击链接失败")
                return False

            new_tab.close()

            tab.listen.start("https://api.getdrops.co/v1/step_submission/create")
            result = try_click(tab, "x://button//p[normalize-space()='Submit']")
            if not result:
                logger.error(f"【{self.id}】 点击Submit按钮失败")
                return False

            res = tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"【{self.id}】 未收到响应")
                return False

            response_data = res.response.body
            status = response_data.get("status")
            if status == "success":
                logger.success(f"【{self.id}】 领取成功")
                return True
            else:
                message = response_data.get("message")
                logger.warning(f"【{self.id}】领取异常, {message}")
                return False

        except Exception as e:
            logger.error(f"【{self.id}】 领取任务失败: {e}")
            return False
        finally:
            if tab:
                tab.close()

    def task_1458(self):
        words = [
            "Revolutionary",
            "Game-Changing",
            "Visionary",
            "Unstoppable",
            "Transformative",
            "Pioneering",
            "Next-Level",
            "Epic",
            "Phenomenal",
            "Outstanding",
        ]

        # 50 tweet templates addressing slow transaction story and Linera’s solution
        templates = [
            "Lost a DeFi trade due to Ethereum’s slow txs—stuck for 45 mins with $50 fees! 😣 Linera’s {random_word} #microchains deliver instant trades. 🚀 Join .@Linera_io for a faster Web3! 🌟 #Blockchain #DeFi",
            "Ethereum’s congestion killed my NFT mint—1hr delay, $70 fees! 😩 Linera’s {random_word} #microchains ensure instant processing. Join .@Linera_io! ⚡️ #Web3 #Crypto",
            "Missed a crypto auction due to slow txs on BSC—30 mins, $40 fees! 😤 Linera’s {random_word} #microchains fix congestion. .@Linera_io leads! 🚀 #Blockchain #Web3",
            "Stuck in a slow Ethereum tx for an NFT drop—1hr, $65 fees! 😖 Linera’s {random_word} #microchains offer instant confirmations. .@Linera_io! 🌐 #Crypto #DeFi",
            "Polygon choked during a game item purchase—40 mins, $30 fees! 😣 Linera’s {random_word} #microchains enable fast txs. Join .@Linera_io! 🚀 #Web3 #Blockchain",
            "Ethereum’s gas fees ate my NFT budget—1hr wait, $55! 😫 Linera’s {random_word} #microchains ensure low-cost, fast txs. .@Linera_io! ⚡️ #Crypto #Web3",
            "Missed a token sale due to Solana’s lag—50 mins, high fees! 😤 Linera’s {random_word} #microchains guarantee speed. Join .@Linera_io! 🌟 #Blockchain #DeFi",
            "Slow Ethereum tx cost me a rare NFT—1hr, $60 fees! 😩 Linera’s {random_word} #microchains fix this with parallel txs. .@Linera_io! 🚀 #Web3 #Crypto",
            "BSC froze my DeFi swap—45 mins, $45 fees! 😣 Linera’s {random_word} #microchains deliver instant swaps. Join .@Linera_io! ⚡️ #Blockchain #Web3",
            "Ethereum’s congestion ruined my NFT bid—1hr, $70 fees! 😖 Linera’s {random_word} #microchains ensure no delays. .@Linera_io! 🌐 #Crypto #DeFi",
            "Stuck in a slow Polygon tx for a game—40 mins, $35 fees! 😫 Linera’s {random_word} #microchains make gaming fast. Join .@Linera_io! 🚀 #Web3 #Blockchain",
            "Missed a crypto deal due to Ethereum’s lag—1hr, $50 fees! 😤 Linera’s {random_word} #microchains offer instant txs. .@Linera_io! ⚡️ #Crypto #Web3",
            "Solana’s slowdown killed my token swap—50 mins, high fees! 😣 Linera’s {random_word} #microchains ensure speed. Join .@Linera_io! 🌟 #Blockchain #DeFi",
            "Ethereum tx took 1hr for an NFT—$65 fees! 😩 Linera’s {random_word} #microchains fix congestion with parallel processing. .@Linera_io! 🚀 #Web3 #Crypto",
            "BSC’s slow txs cost me a DeFi yield—45 mins, $40 fees! 😖 Linera’s {random_word} #microchains deliver fast swaps. Join .@Linera_io! ⚡️ #Blockchain #Web3",
            "Ethereum’s gas spike ruined my NFT mint—1hr, $60 fees! 😫 Linera’s {random_word} #microchains ensure low fees. .@Linera_io! 🌐 #Crypto #DeFi",
            "Polygon lagged during a game tx—40 mins, $30 fees! 😤 Linera’s {random_word} #microchains power fast gaming. Join .@Linera_io! 🚀 #Web3 #Blockchain",
            "Missed a token sale due to Solana’s congestion—50 mins, high fees! 😣 Linera’s {random_word} #microchains guarantee speed. .@Linera_io! ⚡️ #Crypto #Web3",
            "Ethereum’s slow txs killed my NFT drop—1hr, $55 fees! 😩 Linera’s {random_word} #microchains ensure instant txs. .@Linera_io! 🌟 #Blockchain #DeFi",
            "BSC’s lag cost me a DeFi trade—45 mins, $50 fees! 😖 Linera’s {random_word} #microchains fix delays. Join .@Linera_io! 🚀 #Web3 #Crypto",
            "Ethereum’s congestion ruined my NFT bid—1hr, $70 fees! 😫 Linera’s {random_word} #microchains offer fast processing. .@Linera_io! ⚡️ #Blockchain #Web3",
            "Polygon’s slow txs hit my game item buy—40 mins, $35 fees! 😤 Linera’s {random_word} #microchains ensure speed. .@Linera_io! 🌐 #Crypto #DeFi",
            "Missed a crypto auction due to Ethereum’s lag—1hr, $60 fees! 😣 Linera’s {random_word} #microchains fix congestion. Join .@Linera_io! 🚀 #Web3 #Blockchain",
            "Solana’s slowdown killed my swap—50 mins, high fees! 😩 Linera’s {random_word} #microchains deliver instant txs. .@Linera_io! ⚡️ #Crypto #Web3",
            "Ethereum tx took 1hr for an NFT mint—$65 fees! 😖 Linera’s {random_word} #microchains ensure no delays. .@Linera_io! 🌟 #Blockchain #DeFi",
            "BSC’s congestion cost me a DeFi yield—45 mins, $40 fees! 😫 Linera’s {random_word} #microchains power fast swaps. Join .@Linera_io! 🚀 #Web3 #Crypto",
            "Ethereum’s gas fees ate my NFT budget—1hr, $55 fees! 😤 Linera’s {random_word} #microchains offer low-cost txs. .@Linera_io! ⚡️ #Blockchain #Web3",
            "Polygon lagged during a game tx—40 mins, $30 fees! 😣 Linera’s {random_word} #microchains ensure fast gaming. Join .@Linera_io! 🌐 #Crypto #DeFi",
            "Missed a token sale due to Solana’s lag—50 mins, high fees! 😩 Linera’s {random_word} #microchains guarantee speed. .@Linera_io! 🚀 #Web3 #Blockchain",
            "Ethereum’s slow txs killed my NFT drop—1hr, $60 fees! 😖 Linera’s {random_word} #microchains fix delays. .@Linera_io! ⚡️ #Crypto #Web3",
            "BSC’s lag cost me a DeFi trade—45 mins, $50 fees! 😫 Linera’s {random_word} #microchains ensure instant swaps. Join .@Linera_io! 🌟 #Blockchain #DeFi",
            "Ethereum’s congestion ruined my NFT bid—1hr, $70 fees! 😤 Linera’s {random_word} #microchains offer fast txs. .@Linera_io! 🚀 #Web3 #Crypto",
            "Polygon’s slow txs hit my game item buy—40 mins, $35 fees! 😣 Linera’s {random_word} #microchains power speed. Join .@Linera_io! ⚡️ #Blockchain #Web3",
            "Missed a crypto auction due to Ethereum’s lag—1hr, $60 fees! 😩 Linera’s {random_word} #microchains fix congestion. .@Linera_io! 🌐 #Crypto #DeFi",
            "Solana’s slowdown killed my swap—50 mins, high fees! 😖 Linera’s {random_word} #microchains ensure instant txs. .@Linera_io! 🚀 #Web3 #Blockchain",
            "Ethereum tx took 1hr for an NFT—$65 fees! 😫 Linera’s {random_word} #microchains deliver fast confirmations. .@Linera_io! ⚡️ #Crypto #Web3",
            "BSC’s congestion cost me a DeFi yield—45 mins, $40 fees! 😤 Linera’s {random_word} #microchains power fast swaps. Join .@Linera_io! 🌟 #Blockchain #DeFi",
            "Ethereum’s gas spike ruined my NFT mint—1hr, $55 fees! 😣 Linera’s {random_word} #microchains ensure low fees. .@Linera_io! 🚀 #Web3 #Crypto",
            "Polygon lagged during a game tx—40 mins, $30 fees! 😩 Linera’s {random_word} #microchains make gaming fast. Join .@Linera_io! ⚡️ #Blockchain #Web3",
            "Missed a token sale due to Solana’s congestion—50 mins, high fees! 😖 Linera’s {random_word} #microchains guarantee speed. .@Linera_io! 🌐 #Crypto #DeFi",
            "Ethereum’s slow txs killed my NFT drop—1hr, $60 fees! 😫 Linera’s {random_word} #microchains fix delays. .@Linera_io! 🚀 #Web3 #Blockchain",
            "BSC’s lag cost me a DeFi trade—45 mins, $50 fees! 😤 Linera’s {random_word} #microchains ensure instant swaps. Join .@Linera_io! ⚡️ #Crypto #Web3",
            "Ethereum’s congestion ruined my NFT bid—1hr, $70 fees! 😣 Linera’s {random_word} #microchains offer fast txs. .@Linera_io! 🌟 #Blockchain #DeFi",
            "Polygon’s slow txs hit my game item buy—40 mins, $35 fees! 😩 Linera’s {random_word} #microchains power speed. Join .@Linera_io! 🚀 #Web3 #Crypto",
            "Missed a crypto auction due to Ethereum’s lag—1hr, $60 fees! 😖 Linera’s {random_word} #microchains fix congestion. .@Linera_io! ⚡️ #Blockchain #Web3",
            "Solana’s slowdown killed my swap—50 mins, high fees! 😫 Linera’s {random_word} #microchains ensure instant txs. .@Linera_io! 🌐 #Crypto #DeFi",
            "Ethereum tx took 1hr for an NFT mint—$65 fees! 😤 Linera’s {random_word} #microchains deliver fast confirmations. .@Linera_io! 🚀 #Web3 #Blockchain",
            "BSC’s congestion cost me a DeFi yield—45 mins, $40 fees! 😣 Linera’s {random_word} #microchains power fast swaps. Join .@Linera_io! ⚡️ #Crypto #Web3",
            "Ethereum’s gas fees ate my NFT budget—1hr, $55 fees! 😩 Linera’s {random_word} #microchains offer low-cost txs. .@Linera_io! 🌟 #Blockchain #DeFi",
            "Polygon lagged during a game tx—40 mins, $30 fees! 😖 Linera’s {random_word} #microchains ensure fast gaming. Join .@Linera_io! 🚀 #Web3 #Crypto",
        ]

        # Randomly select one template
        random_word = random.choice(words)
        tweet_text = random.choice(templates).format(random_word=random_word)
        return self.task_tweet(1458, tweet_text)

    def task_1459(self):
        words = [
            "Revolutionary",
            "Game-Changing",
            "Visionary",
            "Unstoppable",
            "Transformative",
            "Pioneering",
            "Next-Level",
            "Epic",
            "Phenomenal",
            "Outstanding",
        ]
        random_word = random.choice(words)

        # 50 tweet templates with crypto origin story and Linera discovery
        templates = [
            f"Got into crypto with Bitcoin in ’16, then DeFi in ’20. 😎 Ethereum’s gas fees hurt! Found .@Linera_io’s {random_word} #microchains on X—fast & scalable! 🚀 #Web3 #Blockchain",
            f"Started with Ethereum in ’18, hit by NFT fees in ’21. 😩 .@Linera_io’s {random_word} #microchains on X saved me—instant txs! ⚡️ #Crypto #Web3",
            f"Dove into crypto with BTC in ’17, then NFTs in ’22. 😤 Gas fees were brutal! Discovered .@Linera_io’s {random_word} #microchains on X—game-changer! 🌟 #Blockchain #DeFi",
            f"My crypto journey began with Bitcoin in ’15, then DeFi in ’20. 😣 High fees stung! Found .@Linera_io’s {random_word} #microchains on X—super fast! 🚀 #Web3 #Crypto",
            f"Entered crypto with Ethereum in ’19, burned by NFT mints in ’21. 😫 .@Linera_io’s {random_word} #microchains on X are congestion-free! ⚡️ #Blockchain #Web3",
            f"Bought BTC in ’17, got into NFTs in ’21. 😖 Ethereum’s fees killed me! Found .@Linera_io’s {random_word} #microchains on X—scalable Web3! 🌐 #Crypto #DeFi",
            f"Started crypto with Bitcoin in ’16, then DeFi swaps in ’20. 😤 Gas fees were wild! Discovered .@Linera_io’s {random_word} #microchains on X—fast! 🚀 #Web3 #Blockchain",
            f"Jumped into crypto with ETH in ’18, hit by NFT costs in ’22. 😩 .@Linera_io’s {random_word} #microchains on X deliver instant txs! ⚡️ #Crypto #Web3",
            f"My crypto story: BTC in ’17, NFTs in ’21. 😣 Gas fees drained me! Found .@Linera_io’s {random_word} #microchains on X—Web3’s future! 🌟 #Blockchain #DeFi",
            f"Got hooked on crypto with Bitcoin in ’15, then DeFi in ’21. 😫 Fees were a nightmare! Discovered .@Linera_io’s {random_word} #microchains on X! 🚀 #Web3 #Crypto",
            f"Started with Ethereum in ’19, burned by NFT fees in ’22. 😤 .@Linera_io’s {random_word} #microchains on X fix congestion! ⚡️ #Blockchain #Web3",
            f"Entered crypto with BTC in ’16, then NFTs in ’21. 😖 High gas fees hurt! Found .@Linera_io’s {random_word} #microchains on X—scalable! 🌐 #Crypto #DeFi",
            f"My crypto path: Bitcoin in ’17, DeFi in ’20. 😩 Fees were brutal! Discovered .@Linera_io’s {random_word} #microchains on X—game-changer! 🚀 #Web3 #Blockchain",
            f"Joined crypto with ETH in ’18, hit by NFT mints in ’21. 😣 .@Linera_io’s {random_word} #microchains on X are super fast! ⚡️ #Crypto #Web3",
            f"Started with Bitcoin in ’16, then NFTs in ’22. 😫 Gas fees killed my vibe! Found .@Linera_io’s {random_word} #microchains on X—Web3’s best! 🌟 #Blockchain #DeFi",
            f"Got into crypto with BTC in ’17, DeFi in ’21. 😤 Fees were insane! Discovered .@Linera_io’s {random_word} #microchains on X—fast & cheap! 🚀 #Web3 #Crypto",
            f"Entered crypto with Ethereum in ’19, burned by NFT costs in ’21. 😖 .@Linera_io’s {random_word} #microchains on X solve gas woes! ⚡️ #Blockchain #Web3",
            f"My crypto journey: BTC in ’15, NFTs in ’22. 😩 Fees drained me! Found .@Linera_io’s {random_word} #microchains on X—scalable Web3! 🌐 #Crypto #DeFi",
            f"Started with Bitcoin in ’16, then DeFi in ’20. 😣 Gas fees stung! Discovered .@Linera_io’s {random_word} #microchains on X—game-changer! 🚀 #Web3 #Blockchain",
            f"Jumped into crypto with ETH in ’18, hit by NFT fees in ’21. 😫 .@Linera_io’s {random_word} #microchains on X deliver instant txs! ⚡️ #Crypto #Web3",
            f"Got hooked on Bitcoin in ’17, then NFTs in ’22. 😤 Fees were brutal! Found .@Linera_io’s {random_word} #microchains on X—Web3’s future! 🌟 #Blockchain #DeFi",
            f"Started crypto with Ethereum in ’19, burned by DeFi fees in ’21. 😖 .@Linera_io’s {random_word} #microchains on X are congestion-free! 🚀 #Web3 #Crypto",
            f"Entered crypto with BTC in ’16, NFTs in ’21. 😩 High fees hurt! Discovered .@Linera_io’s {random_word} #microchains on X—scalable! ⚡️ #Blockchain #Web3",
            f"My crypto story: Bitcoin in ’17, DeFi in ’20. 😣 Gas fees killed me! Found .@Linera_io’s {random_word} #microchains on X—fast! 🌐 #Crypto #DeFi",
            f"Joined crypto with ETH in ’18, hit by NFT mints in ’22. 😫 .@Linera_io’s {random_word} #microchains on X are game-changing! 🚀 #Web3 #Blockchain",
            f"Started with Bitcoin in ’16, then NFTs in ’21. 😤 Fees were a nightmare! Discovered .@Linera_io’s {random_word} #microchains on X—Web3’s best! ⚡️ #Crypto #Web3",
            f"Got into crypto with BTC in ’17, DeFi in ’21. 😖 Fees drained my wallet! Found .@Linera_io’s {random_word} #microchains on X—scalable! 🌟 #Blockchain #DeFi",
            f"Entered crypto with Ethereum in ’19, burned by NFT fees in ’22. 😩 .@Linera_io’s {random_word} #microchains on X fix gas issues! 🚀 #Web3 #Crypto",
            f"My crypto path: BTC in ’15, NFTs in ’21. 😣 Fees were brutal! Discovered .@Linera_io’s {random_word} #microchains on X—fast & cheap! ⚡️ #Blockchain #Web3",
            f"Started with Bitcoin in ’16, then DeFi in ’20. 😫 Gas fees stung! Found .@Linera_io’s {random_word} #microchains on X—game-changer! 🌐 #Crypto #DeFi",
            f"Jumped into crypto with ETH in ’18, hit by NFT costs in ’21. 😤 .@Linera_io’s {random_word} #microchains on X deliver instant txs! 🚀 #Web3 #Blockchain",
            f"Got hooked on Bitcoin in ’17, NFTs in ’22. 😖 Fees killed my vibe! Discovered .@Linera_io’s {random_word} #microchains on X—Web3’s future! ⚡️ #Crypto #Web3",
            f"Started crypto with Ethereum in ’19, burned by DeFi fees in ’21. 😩 .@Linera_io’s {random_word} #microchains on X are congestion-free! 🌟 #Blockchain #DeFi",
            f"Entered crypto with BTC in ’16, NFTs in ’21. 😣 High fees hurt! Found .@Linera_io’s {random_word} #microchains on X—scalable Web3! 🚀 #Web3 #Crypto",
            f"My crypto journey: Bitcoin in ’17, DeFi in ’20. 😫 Fees drained me! Discovered .@Linera_io’s {random_word} #microchains on X—fast! ⚡️ #Blockchain #Web3",
            f"Joined crypto with ETH in ’18, hit by NFT mints in ’22. 😤 .@Linera_io’s {random_word} #microchains on X are game-changing! 🌐 #Crypto #DeFi",
            f"Started with Bitcoin in ’16, then NFTs in ’21. 😖 Fees were a nightmare! Found .@Linera_io’s {random_word} #microchains on X—Web3’s best! 🚀 #Web3 #Blockchain",
            f"Got into crypto with BTC in ’17, DeFi in ’21. 😩 Fees killed my wallet! Discovered .@Linera_io’s {random_word} #microchains on X—scalable! ⚡️ #Crypto #Web3",
            f"Entered crypto with Ethereum in ’19, burned by NFT fees in ’22. 😫 .@Linera_io’s {random_word} #microchains on X fix gas issues! 🌟 #Blockchain #DeFi",
            f"My crypto story: BTC in ’15, NFTs in ’21. 😤 Fees were brutal! Found .@Linera_io’s {random_word} #microchains on X—fast & cheap! 🚀 #Web3 #Crypto",
            f"Started with Bitcoin in ’16, then DeFi in ’20. 😖 Gas fees stung! Discovered .@Linera_io’s {random_word} #microchains on X—game-changer! ⚡️ #Blockchain #Web3",
            f"Jumped into crypto with ETH in ’18, hit by NFT costs in ’21. 😩 .@Linera_io’s {random_word} #microchains on X deliver instant txs! 🌐 #Crypto #DeFi",
            f"Got hooked on Bitcoin in ’17, NFTs in ’22. 😣 Fees killed my vibe! Found .@Linera_io’s {random_word} #microchains on X—Web3’s future! 🚀 #Web3 #Blockchain",
            f"Started crypto with Ethereum in ’19, burned by DeFi fees in ’21. 😫 .@Linera_io’s {random_word} #microchains on X are congestion-free! ⚡️ #Crypto #Web3",
            f"Entered crypto with BTC in ’16, NFTs in ’21. 😤 High fees hurt! Discovered .@Linera_io’s {random_word} #microchains on X—scalable! 🌟 #Blockchain #DeFi",
            f"My crypto path: Bitcoin in ’17, DeFi in ’20. 😖 Fees drained me! Found .@Linera_io’s {random_word} #microchains on X—fast! 🚀 #Web3 #Crypto",
            f"Joined crypto with ETH in ’18, hit by NFT mints in ’22. 😩 .@Linera_io’s {random_word} #microchains on X are game-changing! ⚡️ #Blockchain #Web3",
            f"Started with Bitcoin in ’16, then NFTs in ’21. 😣 Fees were a nightmare! Discovered .@Linera_io’s {random_word} #microchains on X—Web3’s best! 🌐 #Crypto #DeFi",
            f"Got into crypto with BTC in ’17, DeFi in ’21. 😫 Fees killed my wallet! Found .@Linera_io’s {random_word} #microchains on X—scalable! 🚀 #Web3 #Blockchain",
            f"Entered crypto with Ethereum in ’19, burned by NFT fees in ’22. 😤 .@Linera_io’s {random_word} #microchains on X fix gas issues! ⚡️ #Crypto #Web3",
        ]

        # Randomly select one template
        random_word = random.choice(words)
        tweet_text = random.choice(templates).format(random_word=random_word)
        return self.task_tweet(1459, tweet_text)

    def task_1514(self):
        words = [
            "Revolutionary",
            "Game-Changing",
            "Visionary",
            "Unstoppable",
            "Transformative",
            "Pioneering",
            "Next-Level",
            "Epic",
            "Phenomenal",
            "Outstanding",
            "Innovative",
            "Groundbreaking",
            "Dynamic",
            "Trailblazing",
            "Cutting-Edge",
            "Futuristic",
            "Remarkable",
            "Stellar",
            "Breakthrough",
            "Inspiring",
        ]
        random_word = random.choice(words)

        # 50 tweet templates focusing on AI use cases with Linera's microchains
        templates = [
            f".@Linera_io’s {random_word} #microchains power AI! 🚀 On-chain agents execute real-time trades with low latency—perfect for DeFi bots! 🌟 #AI #Web3",
            f"Linera’s {random_word} #microchains are AI-ready! ⚡️ Real-time bots analyze data instantly for dynamic NFT pricing. Game-changer! .@Linera_io #AI #Blockchain",
            f"AI + .@Linera_io = {random_word}! 🌐 Microchains enable fast AI games with on-chain logic—seamless, scalable fun! 🎮 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains unlock AI potential! 🚀 Real-time bots for DeFi yield optimization—fast & scalable! ⚡️ #AI #Web3",
            f"Linera’s {random_word} #microchains are built for AI! 🌟 On-chain agents for instant market analysis—DeFi’s future! .@Linera_io #Blockchain #AI",
            f"AI on .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for supply chain tracking—scalable & efficient! 🚀 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains power AI games! 🎮 Fast, on-chain logic for dynamic gameplay—next-level Web3! 🌐 #AI #Blockchain",
            f"Linera’s {random_word} #microchains fuel AI! 🚀 On-chain agents for real-time fraud detection—secure & fast! ⚡️ .@Linera_io #Web3 #AI",
            f"AI meets .@Linera_io’s {random_word} #microchains! 🌟 Real-time bots for personalized NFT marketplaces—scalable! 🚀 #AI #Web3",
            f".@Linera_io’s {random_word} #microchains are AI’s future! ⚡️ On-chain agents for instant loan approvals—game-changer! 🌐 #Blockchain #AI",
            f"Linera’s {random_word} #microchains empower AI! 🚀 Real-time bots for DeFi arbitrage—fast & scalable! ⚡️ .@Linera_io #Web3 #AI",
            f"AI thrives on .@Linera_io’s {random_word} #microchains! 🌟 On-chain agents for real-time health data analysis—secure! 🚀 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains boost AI! ⚡️ Fast AI games with on-chain rewards—scalable & fun! 🎮 #Web3 #AI",
            f"Linera’s {random_word} #microchains are AI-ready! 🚀 Real-time bots for dynamic ad targeting—Web3’s future! 🌐 .@Linera_io #AI #Blockchain",
            f"AI + .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for instant loan approvals—scalable & secure! 🚀 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains power AI! 🌟 Real-time bots for supply chain optimization—fast & efficient! ⚡️ #AI #Web3",
            f"Linera’s {random_word} #microchains fuel AI games! 🎮 On-chain logic for dynamic worlds—scalable fun! 🚀 .@Linera_io #AI #Blockchain",
            f"AI on .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for DeFi risk assessment—game-changer! 🌐 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains unlock AI! 🚀 On-chain agents for real-time voting systems—secure & fast! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains are AI’s backbone! 🌟 Real-time bots for NFT auctions—scalable & instant! 🚀 .@Linera_io #Web3 #AI",
            f"AI thrives with .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for real-time logistics—efficient! 🌐 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains power AI! 🚀 Real-time bots for dynamic pricing in DeFi—scalable! ⚡️ #Web3 #AI",
            f"Linera’s {random_word} #microchains fuel AI games! 🎮 Fast, on-chain logic for immersive worlds—Web3’s best! 🌟 .@Linera_io #AI #Blockchain",
            f"AI + .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for fraud detection in DeFi—secure & fast! 🚀 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains empower AI! 🌐 On-chain agents for instant market predictions—game-changer! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains are AI-ready! 🚀 Real-time bots for personalized Web3 ads—scalable! 🌟 .@Linera_io #Web3 #AI",
            f"AI on .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for instant insurance claims—fast & secure! 🚀 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains boost AI! 🌐 Real-time bots for DeFi portfolio tracking—scalable! ⚡️ #Web3 #AI",
            f"Linera’s {random_word} #microchains power AI games! 🎮 On-chain logic for dynamic rewards—Web3’s future! 🚀 .@Linera_io #AI #Blockchain",
            f"AI thrives on .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for supply chain analytics—efficient! 🌟 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains fuel AI! 🚀 On-chain agents for instant credit analysis—scalable! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains are AI’s future! 🌐 Real-time bots for NFT pricing—fast & dynamic! 🚀 .@Linera_io #Web3 #AI",
            f"AI + .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for real-time voting—secure & scalable! 🌟 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains empower AI! 🚀 Real-time bots for DeFi yield farming—game-changer! ⚡️ #Web3 #AI",
            f"Linera’s {random_word} #microchains boost AI games! 🎮 Fast, on-chain logic for immersive play—scalable! 🌐 .@Linera_io #AI #Blockchain",
            f"AI on .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for dynamic ad markets—Web3’s best! 🚀 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains power AI! 🌟 On-chain agents for real-time fraud checks—secure! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains fuel AI! 🚀 Real-time bots for NFT marketplaces—scalable & fast! 🌐 .@Linera_io #Web3 #AI",
            f"AI thrives with .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for instant logistics—efficient! 🚀 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains are AI-ready! 🌟 Real-time bots for DeFi analytics—game-changer! ⚡️ #Web3 #AI",
            f"Linera’s {random_word} #microchains power AI games! 🎮 On-chain logic for dynamic worlds—scalable fun! 🚀 .@Linera_io #AI #Blockchain",
            f"AI + .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for supply chain tracking—fast & secure! 🌐 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains boost AI! 🚀 On-chain agents for real-time market analysis—scalable! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains fuel AI! 🌟 Real-time bots for personalized NFTs—Web3’s future! 🚀 .@Linera_io #Web3 #AI",
            f"AI on .@Linera_io’s {random_word} #microchains! ⚡️ On-chain agents for instant loan scoring—secure & fast! 🌐 #AI #Blockchain",
            f".@Linera_io’s {random_word} #microchains empower AI! 🚀 Real-time bots for DeFi arbitrage—scalable! ⚡️ #Web3 #AI",
            f"Linera’s {random_word} #microchains are AI’s backbone! 🌟 On-chain logic for AI games—fast & immersive! 🎮 .@Linera_io #AI #Blockchain",
            f"AI thrives on .@Linera_io’s {random_word} #microchains! ⚡️ Real-time bots for dynamic pricing—game-changer! 🚀 #Web3 #AI",
            f".@Linera_io’s {random_word} #microchains power AI! 🌐 On-chain agents for real-time health analytics—secure! ⚡️ #AI #Blockchain",
            f"Linera’s {random_word} #microchains fuel AI! 🚀 Real-time bots for NFT auctions—scalable & instant! 🌟 .@Linera_io #Web3 #AI",
        ]

        # Randomly select one template
        random_word = random.choice(words)
        tweet_text = random.choice(templates).format(random_word=random_word)
        return self.task_tweet(1514, tweet_text)

    def task_1515(self):
        words = [
            "Revolutionary",
            "Game-Changing",
            "Visionary",
            "Unstoppable",
            "Transformative",
            "Pioneering",
            "Next-Level",
            "Epic",
            "Phenomenal",
            "Outstanding",
            "Innovative",
            "Groundbreaking",
            "Dynamic",
            "Trailblazing",
            "Cutting-Edge",
            "Futuristic",
            "Remarkable",
            "Stellar",
            "Breakthrough",
            "Inspiring",
        ]
        random_word = random.choice(words)

        # 50 tweet templates focusing on AI use cases with Linera's microchains
        templates = [
            f"That Community Hour when someone asked if @linera_io microchains run on vibes and the host said only on weekends! Pure joy! {random_word} #LineraCommunity",
            f"My fave moment was the Pomodoro Pour joke at @linera_io Community Hour that had us all cackling! #LineraCommunity {random_word}",
            f"#LineraCommunity infinite parallel worlds joke during @linera_io Community Hour was hilarious with bugs blamed on alt chains! {random_word}",
            f"Loved when @linera_io host said microchains are more like micro-wins after a dev demo in Community Hour. {random_word} #LineraCommunity",
            f"When a dev shared a cat vid as an error at @linera_io Community Hour it became our mascot! #LineraCommunity {random_word}",
            f"#LineraCommunity vibe is epic! The linear algebra magic joke at @linera_io Community Hour was gold. {random_word}",
            f"That @linera_io Community Hour turned into a Linera vs Critics meme fest with total chaos and fun! {random_word} #LineraCommunity",
            f"The Pomodoro Pour quip at @linera_io Community Hour had me in stitches! {random_word} #LineraCommunity",
            f"#LineraCommunity energy! When @linera_io host said microchains could outrun light we all cheered. {random_word}",
            f"@linera_io bugs are just alt microchain antics joke in Community Hour had #LineraCommunity roaring! {random_word}",
            f"Fave memory was @linera_io Community Hour with virtual confetti for a dev’s app! #LineraCommunity {random_word}",
            f"#LineraCommunity infinite parallel worlds gag at @linera_io Community Hour was cosmic! {random_word}",
            f"When someone called @linera_io microchains tiny blockchain hugs in Community Hour we melted. {random_word} #LineraCommunity",
            f"@linera_io Pomodoro Pour puns took over Community Hour as my fave moment! #LineraCommunity {random_word}",
            f"#LineraCommunity vibe! @linera_io host said microchains are vibes in binary during Community Hour. {random_word}",
            f"A dev blamed a bug on microchain gremlins at @linera_io Community Hour so hilarious! {random_word} #LineraCommunity",
            f"When @linera_io Community Hour became a microchain rap battle I was living! #LineraCommunity {random_word}",
            f"#LineraCommunity Pomodoro Pour joke at @linera_io Community Hour still cracks me up! {random_word}",
            f"@linera_io Community Hour meme challenge with Linera in Movies gifs was epic! {random_word} #LineraCommunity",
            f"A dev called @linera_io microchains blockchain sprinkles in Community Hour and I loved it! #LineraCommunity {random_word}",
            f"#LineraCommunity microchains don’t sleep they scale quip at @linera_io Community Hour was fire! {random_word}",
            f"@linera_io Community Hour debating microchains vs unicorn magic was peak #LineraCommunity! {random_word}",
            f"Fave memory was @linera_io Community Hour with POAPs and we cheered like kids! {random_word} #LineraCommunity",
            f"#LineraCommunity infinite parallel worlds joke at @linera_io Community Hour had me snorting! {random_word}",
            f"@linera_io host said microchains are blockchain hugs in Community Hour so wholesome! #LineraCommunity {random_word}",
            f"@linera_io Community Hour microchain meme-off was my fave chaotic moment! {random_word} #LineraCommunity",
            f"#LineraCommunity bugs are microchain adventures quip at @linera_io Community Hour was genius! {random_word}",
            f"When a dev said microchains are my cardio at @linera_io Community Hour #LineraCommunity lost it! {random_word}",
            f"@linera_io Community Hour celebrating a new SDK with high-fives was unforgettable! #LineraCommunity {random_word}",
            f"#LineraCommunity Pomodoro Pour at @linera_io Community Hour is iconic! {random_word}",
            f"@linera_io Community Hour microchain meme war was pure #LineraCommunity chaos! {random_word}",
            f"Chanting scale it like a rally at @linera_io Community Hour was my fave! #LineraCommunity {random_word}",
            f"#LineraCommunity infinite parallel worlds at @linera_io Community Hour was peak humor! {random_word}",
            f"@linera_io microchains are blockchain poetry in Community Hour hit deep! {random_word} #LineraCommunity",
            f"@linera_io Community Hour with a 100 USDC raffle had us hyped! #LineraCommunity {random_word}",
            f"#LineraCommunity Pomodoro Pour at @linera_io Community Hour had us in tears! {random_word}",
            f"@linera_io Community Hour microchain karaoke session was wild! #LineraCommunity {random_word}",
            f"A dev said microchains are my spirit animal at @linera_io Community Hour and yes! {random_word} #LineraCommunity",
            f"#LineraCommunity microchains are tiny rockets quip at @linera_io Community Hour was lit! {random_word}",
            f"@linera_io Community Hour roasting bugs as alt microchain antics was gold! #LineraCommunity {random_word}",
            f"Geeked out over the Rust SDK at @linera_io Community Hour as my fave memory! {random_word} #LineraCommunity",
            f"#LineraCommunity infinite parallel worlds at @linera_io Community Hour is my fave! {random_word}",
            f"@linera_io microchains are spicy blockchain in Community Hour was epic! #LineraCommunity {random_word}",
            f"@linera_io Community Hour name that microchain game was so fun! {random_word} #LineraCommunity",
            f"#LineraCommunity bugs are microchain hugs at @linera_io Community Hour was cute! {random_word}",
            f"A dev called microchains blockchain confetti at @linera_io Community Hour and I love it! #LineraCommunity {random_word}",
            f"@linera_io Community Hour devnet launch with memes was peak #LineraCommunity! {random_word}",
            f"#LineraCommunity Pomodoro Pour at @linera_io Community Hour is legendary! {random_word}",
            f"@linera_io Community Hour microchain pun-off was the best hour ever! #LineraCommunity {random_word}",
            f"Cheering a dev’s first microchain app at @linera_io Community Hour was epic! {random_word} #LineraCommunity",
        ]

        # Randomly select one template
        random_word = random.choice(words)
        tweet_text = random.choice(templates).format(random_word=random_word)
        return self.task_tweet(1515, tweet_text)

    def task_1375(self):
        return self.task_tweet(1375, "Real-Time Blockchain @linera_io #microchains ")

    def task_1376(self):
        return self.task_tweet(1376, "gmicrochains")

    def task_1369(self):
        return self.task_retweet(1369, "1912868732764135505")

    def task_1408(self):
        return self.task_retweet(1408, "1920877250096099660")

    def task_1407(self):
        return self.task_visit(1407)

    def _get_auth_headers(self):
        """获取auth headers."""
        for _ in range(3):
            tab = None
            try:
                tab = self.page.new_tab()
                tab.listen.start(targets="https://api\\.getdrops\\.co/v1/.*", is_regex=True)
                tab.get("https://drops.linera.io/home")
                for packet in tab.listen.steps(timeout=90, count=3):
                    if "Authorization" in packet.request.headers:
                        return packet.request.headers
            except Exception as e:
                logger.error(f"【{self.id}】获取token失败, error={str(e)}")
                continue
            finally:
                if tab:
                    tab.close()
        logger.error(f"【{self.id}】获取token失败, 用户可能未登录")
        return None

    def _get_auth_token(self, headers):
        """获取auth token."""
        try:
            # 获取token
            if headers:
                token = headers.get("Authorization")
                if token:
                    return token
            else:
                logger.error(f"【{self.id}】未获取到token")
                return None
        except Exception as e:
            logger.error(f"{self.id} {e}")
            return None

    def task_referral_code(self):
        """获取邀请码."""
        try:
            register = self.data_util.get(self.address)
            _referral_code = register.get("my_referral_code", None)
            if _referral_code:
                logger.success(f"【{self.id}】 已经获取过邀请码")
                return True

            # 获取auth headers
            referral_code = self.get_referral_code_from_request()
            if not referral_code:
                logger.error(f"【{self.id}】 未获取到drops邀请码")
                return False

            # 更新注册信息
            self.data_util.update(self.address, {"my_referral_code": referral_code})
            logger.success(f"【{self.id}】 更新注册信息成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 获取邀请码失败: {e}")
            return False

    def _update_point(self):
        try:
            task_status = self.task_status()
            if not task_status:
                logger.error(f"【{self.id}】更新积分失败,接口未响应")
                return False

            # 更新分数
            current_points = task_status.get("total_points_earned", 0)
            last_points = self.data_util.get(self.address).get("points")
            logger.success(f"【{self.id}】最新分数: {current_points}")
            self.data_util.update(
                self.address,
                {
                    "points": current_points,
                    "last_points": last_points,
                    "points_last_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                },
            )
        except Exception as e:
            logger.error(f"【{self.id}】更新积分失败, error={str(e)}")
            return False

    def task_drops(self, tab):
        """处理drops任务."""
        try:
            task_status = self.task_status()
            if not task_status:
                logger.error(f"【{self.id}】 获取任务状态失败")
                return False

            task_map = {
                "1357": self.task_1357,
                "1369": self.task_1369,
                "1358": self.task_1358,
                "1375": self.task_1375,
                "1376": self.task_1376,
                "1458": self.task_1458,
                "1459": self.task_1459,
                "1514": self.task_1514,
                "1515": self.task_1515,
            }

            # 判断是否所有任务都已完成
            completed_tasks = set(task_status.get("campaign_step_ids", []))
            remain_tasks = [x for x in task_map if x not in completed_tasks]
            if len(remain_tasks) == 0:
                logger.success(f"【{self.id}】所有任务已完成，跳过执行")
                return True

            result = self.task_connect_x(tab)
            if not result:
                logger.error(f"【{self.id}】连接Twitter失败")
                return False

            campaign_step_ids = task_status.get("campaign_step_ids", [])
            for step_id, task_func in task_map.items():
                if step_id in campaign_step_ids:
                    logger.success(f"【{self.id}】已完成任务:{step_id}, 跳过...")
                    continue
                try:
                    task_func()
                    sleep(5)
                except Exception as e:
                    logger.error(f"【{self.id}】执行任务: {step_id} 发成异常, error={str(e)}")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】drops任务异常, error={str(e)}")
            return False

    def check_proxy(self) -> dict:
        """
        Check if a proxy is usable by making a test request and return IP, country, region, and success status.

        Args:
            proxy (str): Proxy in format 'http://ip:port'

        Returns
        -------
            dict: Contains {'success': bool, 'ip': str, 'country': str, 'region': str}.
                  If proxy fails, returns {'success': False, 'ip': '', 'country': '', 'region': ''}.
        """
        proxy = self.browser_controller.browser_config.proxy
        proxies = {
            "http": proxy,
            "https": proxy
        }

        try:
            response = requests.get(
                "https://ipinfo.io/json",
                proxies=proxies,
                timeout=5
            )
            response.raise_for_status()  # Raises an HTTPError for bad responses
            data = response.json()
            result = {
                "success": True,
                "ip": data.get("ip", ""),
                "country": data.get("country", ""),
                "region": data.get("region", "")
            }
            logger.info(f"【{self.id}】获取代理成功，IP: {result['ip']}, 国家: {result['country']}, 地区: {result['region']}")
            return result
        except requests.RequestException as e:
            logger.error(f"【{self.id}】代理不可用,  {str(e)}")
            return {"success": False, "ip": "", "国家": "", "region": ""}

    def task(self):
        try:
            start_time = time.time()

            # if not self.check_proxy().get("success", False):
            #     logger.error(f"【{self.id}】代理不可用，跳过任务执行...")
            #     self.data_util.update(self.address, {"error_code": "代理不可用"})
            #     return False

            self.browser_controller.window_max()
            if not self.register():
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False

            # 判断是否登录
            tab = self.page.new_tab("https://drops.linera.io/home")
            if not get_element(tab, "x://img[@alt='User avatar']", timeout=20):
                result = self.login()
                if not result:
                    logger.error(f"【{self.id}】登录失败")
                    return False

            result = self.task_drops(tab)

            self._update_point()

            end_time = time.time()
            duration = end_time - start_time
            logger.success(f"【{self.id}】drops 任务执行结束, 耗时{duration:.3f}秒")
            return result
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
            return False


    def _verify_email(self):
        try:
            from src.emails.imap4.email_client import EmailClient
            email = self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email
            if not email or not email_pwd:
                self.logger.error(f"{self.id} 邮箱或密码未配置")
                return False

            email_client = EmailClient(email, email_pwd)
            subject = "VERIFY YOUR EMAIL"
            from_email = "drops.house"
            emails = email_client.search_emails_common(email, email_pwd, proxy_email, subject, from_email)
            if not emails:
                self.logger.error(f"{self.id} 未找到验证邮件")
                return False

            email_info = emails[0]
            logger.success(f"{self.id} 找到验证邮件: {email_info['subject']}, 发送时间: {email_info['date']}")

            pattern = r"token=([^&]+)"
            match = re.search(pattern, email_info["content"])

            if not match:
                logger.error(f"{self.id} 未找到验证链接")
                return False

            token = match.group(1)
            url = f"https://app.drops.house/email-verification?token={token}&return_url=https://drops.linera.io/home?ext_id=5oqo4TUSG"

            tab = self.page.new_tab(url)
            sleep(5)  # 等待页面加载完成

            # 检查是否验证成功
            success_message = get_element(tab, "x://p[.='Your email has been successfully verified!']", timeout=20)
            if not success_message:
                logger.error(f"{self.id} 邮箱验证失败，未找到成功提示")
                tab.close()
                return False

            tab.close()

            self.data_util.update(
                self.address,
                {
                    "email": proxy_email or email,
                },
            )
            return True
        except Exception as e:
            logger.error(f"【{self.id}】验证邮箱失败，error={str(e)}")
            return False


    def change_email(self):
        """验证邮箱."""
        self.browser_controller.window_max()

        for _ in range(3):
            try:
                tab = self.page.new_tab("https://drops.linera.io/home")

                avatar = get_element(tab, "x://img[@alt='User avatar']", timeout=20)
                if not avatar:
                    result = self.login()
                    if not result:
                        logger.error(f"【{self.id}】登录失败")
                        tab.close()
                        continue

                # 点击头像进入设置
                avatar.click()
                sleep(2)

                # 点击设置页
                try_click(tab, "x://p[.='Settings']/..")
                sleep(2)

                pending_ele = get_element(tab, "Pending Verification", timeout=6)
                if not pending_ele:
                    logger.success(f"【{self.id}】邮箱已经验证，跳过修改")
                    tab.close()
                    return True

                # 点击编辑邮箱
                try_click(tab, "x://*[name()='svg'][contains(@data-testid, 'EditIcon')]/../..")
                sleep(2)

                # 判断是否有弹窗
                div_ele = get_element(tab, "x://div[@maxwidth='400']")
                if not div_ele:
                    logger.error(f"【{self.id}】未找到设置元素")
                    tab.close()
                    continue

                # 获取当前邮箱
                email_input = get_element(tab, "x://input[@type='email']")
                if not email_input:
                    logger.error(f"【{self.id}】未找到邮箱输入框")
                    tab.close()
                    continue

                email = self.browser_controller.browser_config.email
                email_input.input(email)

                tab.listen.start(targets="https://api.getdrops.co/v1/user/email/request_change")
                # 点击更新邮箱
                try_click(tab, "x://button[.='Update Email']")

                res = tab.listen.wait(timeout=60)
                if not res or not res.response:
                    logger.error(f"【{self.id}】邮箱更新请求失败，网络超时无响应")
                    continue

                is_success = res.response.body.get("success", False)
                if not is_success:
                    logger.success(f"【{self.id}】邮箱更新请求失败，可能是邮箱已存在或其他原因")
                    continue

                result = self._verify_email()
                if not result:
                    logger.error(f"【{self.id}】邮箱验证失败")
                    tab.close()
                    continue

                logger.success(f"【{self.id}】邮箱修改成功")
                return True
            except Exception as e:
                logger.error(f"【{self.id}】邮箱修改失败，error={str(e)}")
                continue

    @staticmethod
    def _convert_and_filter_headers(header) -> TypeAlias:
        # Convert CaseInsensitiveDict to dict and filter out keys starting with ':'
        return {key: value for key, value in header.items() if not key.startswith(":")}


def _run_task(type, index):
    try:
        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)
        faucet = Linera(type, str(index), data_util)
        faucet.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")



def _run_referral_code(type, index):
    try:
        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)

        faucet = Linera(type, str(index), data_util)
        faucet.task_referral_code()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


def _run_change_email(type, index):
    faucet = None
    try:
        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)
        faucet = Linera(type, str(index), data_util)
        faucet.change_email()
        # faucet._verify_email()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
    finally:
        if faucet:
            faucet.browser_controller.close_page()

@click.group()
def cli():
    pass


@cli.command("code")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-c", "--close", type=bool, default=True, help="是否关闭浏览器")
def task_referral_code(type, index, workers, close):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []
        failed_indices = []

        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            linera = None
            try:
                linera = Linera(type, str(index), data_util)
                result = linera.task_referral_code()
                if result:
                    successful_indices.append(index)
                else:
                    failed_indices.append(index)
                return result
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)
                return False
            finally:
                if close and linera:
                    linera.browser_controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=1200,  # 20分钟超时
            retries=3,
            interval=10,
            task_name=f"linera_code-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-c", "--close", type=bool, default=True, help="是否关闭浏览器")
def run(type, index, workers, close):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            linera = None
            try:
                linera = Linera(type, str(index), data_util)
                result = linera.task()
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                else:
                    failed_indices.append(index)  # 将失败的索引添加到失败列表
                return result
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False
            finally:
                if close and linera:
                    linera.browser_controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Linera-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise

@cli.command("ce")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def change_email(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            linera = None
            try:
                linera = Linera(type, str(index), data_util)
                result = linera.change_email()
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    linera.browser_controller.close_page()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False
            finally:
                if linera:
                    linera.browser_controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Linera-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")



@cli.command("heybeluga")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def heybeluga(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        data_dir = os.path.join(get_project_root_path(), "examples", "linera")
        csv_path = os.path.join(data_dir, "linera.csv")
        data_util = DataUtil(csv_path)

        def process_task(index):
            try:
                agent = Linera(type, str(index), data_util)
                result = agent.task()
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    # faucet.page.quit()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                # faucet.page.quit()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Linera-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/linera/index.py run -t ads -i 1-10
# python3 examples/linera/index.py run -t bit -i 1-10
# python3 examples/linera/index.py run -t chrome -i 1-10
# python3 examples/linera/index.py run -t brave -i 1-10

if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.MORE, 1)
    # _run_referral_code(BrowserType.BIT, 1)
    # _run_change_email(BrowserType.ADS, 5)
