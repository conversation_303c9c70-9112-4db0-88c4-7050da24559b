import sys
import time
import imaplib
import click
from pathlib import Path
from typing import List, Tuple
from loguru import logger
from concurrent.futures import ThreadPoolExecutor, as_completed


def batch_check(credentials_file: str, output: str, threads: int, delay: float):
    """
    批量检测邮箱有效性
    
    CREDENTIALS_FILE: 包含邮箱和密码的文本文件，每行一条记录，格式为"邮箱 密码"
    """
    logger.info(f"开始批量检测邮箱，文件: {credentials_file}")
    
    # 读取凭证文件
    credentials = read_credentials(credentials_file)
    logger.info(f"共读取 {len(credentials)} 条邮箱记录")
    
    # 检测结果
    valid_accounts = []
    invalid_accounts = []
    
    # 使用线程池进行并发检测
    with ThreadPoolExecutor(max_workers=threads) as executor:
        futures = {}
        for i, (email, password) in enumerate(credentials):
            # 添加延迟，避免请求过于频繁
            if i > 0 and delay > 0:
                time.sleep(delay)
            
            future = executor.submit(check_rambler_email, email, password)
            futures[future] = (email, password)
        
        # 处理结果
        for future in as_completed(futures):
            email, password = futures[future]
            try:
                is_valid = future.result()
                if is_valid:
                    valid_accounts.append((email, password))
                    logger.success(f"邮箱有效: {email}")
                else:
                    invalid_accounts.append((email, password))
                    logger.error(f"邮箱无效: {email}")
            except Exception as e:
                invalid_accounts.append((email, password))
                logger.error(f"检测出错 {email}: {str(e)}")
    
    # 输出结果
    logger.info(f"检测完成，有效邮箱: {len(valid_accounts)}，无效邮箱: {len(invalid_accounts)}")
    
    # 保存结果到文件
    if output:
        save_results(output, valid_accounts, invalid_accounts)
        logger.info(f"结果已保存到: {output}")

def read_credentials(file_path: str) -> List[Tuple[str, str]]:
    """读取凭证文件"""
    credentials = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) >= 2:
                email = parts[0]
                # 密码可能包含空格，将剩余部分合并为密码
                password = ' '.join(parts[1:])
                credentials.append((email, password))
    
    return credentials

def check_rambler_email(email: str, password: str) -> bool:
    """检测 Rambler 邮箱是否有效"""
    try:
        # Rambler 邮箱的 IMAP 服务器
        imap_server = "imap.rambler.ru"
        imap_port = 993
        
        # 连接到 IMAP 服务器
        imap = imaplib.IMAP4_SSL(imap_server, imap_port)
        
        # 尝试登录
        imap.login(email, password)
        
        # 选择收件箱
        imap.select("INBOX")
        
        # 登出
        imap.logout()
        
        return True
    except Exception as e:
        logger.debug(f"邮箱检测失败 {email}: {str(e)}")
        return False

def save_results(output_path: str, valid_accounts: List[Tuple[str, str]], invalid_accounts: List[Tuple[str, str]]):
    """保存检测结果到文件"""
    output_dir = Path(output_path).parent
    if output_dir != Path('.') and not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("=== 有效邮箱 ===\n")
        for email, password in valid_accounts:
            f.write(f"{email} {password}\n")
        
        f.write("\n=== 无效邮箱 ===\n")
        for email, password in invalid_accounts:
            f.write(f"{email} {password}\n")

if __name__ == "__main__":
    input_file = 'cli/emails.txt'
    output_file = 'cli/results.txt'
    threads = 10
    delay = 0
    batch_check(input_file, output_file, threads, delay)