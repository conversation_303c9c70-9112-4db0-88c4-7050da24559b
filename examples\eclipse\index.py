import click
import random
from loguru import logger
import pyautogui
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices, get_project_root_path
from time import sleep
from retry import retry
from src.browsers.operations import try_click
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


class Eclipse:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.today_clicks = 0
        self.max_daily_clicks = random.randint(8000, 12000)

    # 随机获取一行，并返回
    def get_invite_code(self):
        try:
            invite_codes_path = (
                get_project_root_path() + "/examples/eclipse/invite_codes.txt"
            )
            with open(invite_codes_path, "r") as f:
                lines = f.readlines()
                if not lines:
                    logger.error(f"【{self.id}】 邀请码文件为空")
                    return None
                return random.choice(lines).strip()
        except FileNotFoundError:
            logger.error(f"【{self.id}】 邀请码文件不存在")
            return None
        except Exception as e:
            logger.error(f"【{self.id}】 获取邀请码失败: {e}")
            return None

    def _task_invite(self, lasted_tab):
        try:
            # 找到OTP输入组
            otp_group = lasted_tab.wait.ele_displayed(
                "x://div[@data-sentry-element='InputOTPGroup']", timeout=6
            )
            if otp_group:
                invite_code = self.get_invite_code()
                logger.info(f"【{self.id}】 获取邀请码成功: {invite_code}")
                js_code = """
                  // 1. 获取元素
                  const inputRoot = document.querySelector("input[data-sentry-element='InputOTPRoot']");
                  
                  // 2. 设置值并触发事件
                  const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                  nativeInputValueSetter.call(inputRoot, arguments[0]);

                  // 触发 input 事件
                  const ev = new Event('input', { bubbles: true });
                  inputRoot.dispatchEvent(ev);
                  
                  // 触发 change 事件
                  const changeEvent = new Event('change', { bubbles: true });
                  inputRoot.dispatchEvent(changeEvent);
              """

                lasted_tab.run_js(js_code, invite_code)
                sleep(3)

                # 点击Continue按钮
                ele = lasted_tab.wait.ele_displayed(
                    "x://button[.='Continue']", timeout=6
                )
                if not ele:
                    logger.error(f"【{self.id}】 未能找到Continue按钮")
                    return False

                try_click(lasted_tab, "x://button[.='Continue']", timeout=6)
                sleep(3)

                ele = lasted_tab.wait.ele_displayed("Invalid invite code", timeout=6)
                if ele:
                    logger.error(f"【{self.id}】 邀请码无效")
                    return False

                if lasted_tab.wait.ele_deleted("x://button[.='Continue']", timeout=6):
                    logger.success(f"【{self.id}】 邀请码输入成功")
                    return True

                return False

            else:
                logger.info(f"【{self.id}】 已经输入过邀请码")
        except Exception as e:
            logger.error(f"【{self.id}】 输入邀请码失败: {e}")
            return False

    def _task_connect_wallet(self, lasted_tab):
        try:
            ele = lasted_tab.wait.ele_displayed("Select Your Eclipse Wallet", timeout=5)
            if not ele:
                logger.info(f"【{self.id}】 已经连接过钱包")
                return True

            lasted_tab.ele("x://p[text()='Backpack Wallet']/../..").click()
            sleep(1)

            shadow_ele = lasted_tab.ele(
                "x://div[@data-testid='dynamic-modal-shadow']", timeout=6
            )
            if not shadow_ele:
                logger.error(f"【{self.id}】 未找到钱包选择页面")
                return False

            modal_ele = shadow_ele.sr(
                "x://div[@class='dynamic-shadow-dom-content']//div[@class='modal']",
                timeout=6,
            )
            if not modal_ele:
                logger.error(f"【{self.id}】 未打开钱包选择页面")
                return False

            modal_ele.ele("x://button[.='Connect']").click()

            result = self.browser_controller.backpack_wallet_connect()
            sleep(1)
            return result
        except Exception as e:
            logger.error(f"【{self.id}】 连接钱包失败: {e}")
            return False

    def _task_connect_x(self, lasted_tab):
        try:
            ele = lasted_tab.wait.ele_displayed("Connect With X", timeout=5)
            if not ele:
                logger.info(f"【{self.id}】 已经连接过X")
                return True

            lasted_tab.ele("x://button[.='Continue']").click()
            sleep(1)
            auth_ele = lasted_tab.wait.ele_displayed(
                "x://button[@data-testid='OAuth_Consent_Button']", timeout=10
            )
            if not auth_ele:
                logger.error(f"【{self.id}】 未找到授权按钮")
                return False

            auth_ele.click()
            sleep(3)
            ele = lasted_tab.wait.ele_displayed(
                "Twitter connected successfully!", timeout=10
            )
            if not ele:
                logger.error(f"【{self.id}】 授权X失败")
                return False

            logger.success(f"【{self.id}】 连接X成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 授权X失败")
            return False

    def _task_connect_dc(self, lasted_tab):
        try:
            ele = lasted_tab.wait.ele_displayed("Join Discord Community", timeout=5)
            if not ele:
                logger.info(f"【{self.id}】 已经加入过Discord")
                return True

            lasted_tab.ele("x://button[.='Join our Discord Community']").click()
            sleep(3)
            if "discord" not in self.page.latest_tab.url:
                logger.error(f"【{self.id}】 未能打开dc页面, 请检查")
                return False

            self.page.latest_tab.close()
            sleep(3)

            ele = self.page.latest_tab.wait.ele_displayed(
                "x://button[.='Verify Join']", timeout=10
            )
            if not ele:
                logger.error(f"【{self.id}】 未能验证加入Discord")
                return False

            ele.click()
            sleep(3)

            ele = self.page.latest_tab.wait.ele_displayed(
                "x://header[@id='oauth2-authorize-header-id']", timeout=10
            )
            if not ele:
                logger.error(f"【{self.id}】 未能验证加入Discord")
                return False

            self.page.latest_tab.ele("x:(//button)[2]").click()
            sleep(3)

            ele = self.page.latest_tab.wait.ele_displayed(
                "Mint Your Domain", timeout=10
            )
            if not ele:
                logger.error(f"【{self.id}】 未能验证加入Discord")
                return False

            logger.success(f"【{self.id}】 加入Discord成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 连接Discord失败")
            return False

    def _task_agree_terms(self, lasted_tab):
        try:
            ele = lasted_tab.wait.ele_displayed("I agree to Eclipse's", timeout=6)
            if ele:
                lasted_tab.ele("x://footer/div/div").click()
                sleep(1)
                lasted_tab.ele("x://footer/button[.='Continue']").click()
                sleep(1)
            else:
                logger.info(f"【{self.id}】 已经同意协议")
        except Exception as e:
            logger.error(f"【{self.id}】 同意协议失败: {e}")
            return False

    def _task_mint_domain(self, lasted_tab):
        try:
            ele = lasted_tab.wait.ele_displayed("Mint Your Domain", timeout=5)
            if not ele:
                logger.info(f"【{self.id}】 已经mint过域名")
                return True
        except Exception as e:
            logger.error(f"【{self.id}】 mint域名失败: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_register(self):
        self.browser_controller.backpack_wallet_login()

        lasted_tab = self.page.new_tab("https://tap.eclipse.xyz")
        self._task_agree_terms(lasted_tab)
        self._task_invite(lasted_tab)
        self._task_connect_wallet(lasted_tab)
        self._task_connect_x(lasted_tab)
        self._task_connect_dc(lasted_tab)
        self._task_mint_domain(lasted_tab)

        logger.success(f"【{self.id}】 任务完成")

    def _tap_wallet_connect(self, lasted_tab):
        if ele := lasted_tab.ele("x:(//button[.='Connect Wallet'])[2]", timeout=3):
            ele.click()
            sleep(3)

            shadow_ele = lasted_tab.ele(
                "x://div[@data-testid='dynamic-modal-shadow']", timeout=6
            )
            if not shadow_ele:
                logger.error(f"【{self.id}】 未找到钱包选择页面")
                return False

            modal_ele = shadow_ele.sr(
                "x://div[@class='dynamic-shadow-dom-content']//div[@class='modal']",
                timeout=6,
            )
            if not modal_ele:
                logger.error(f"【{self.id}】 未打开钱包选择页面")
                return False

            backpack_ele = modal_ele.ele("x://span[.='Backpack']/../..", timeout=6)
            if backpack_ele:
                backpack_ele.click()
                sleep(1)
                modal_ele.ele("x://button[.='Connect']").click()
                sleep(1)
                return True
            else:
                modal_ele.ele("x://button[.='Connect']").click()
                sleep(1)
                return self.browser_controller.backpack_wallet_connect()

        login_ele = lasted_tab.ele("x://button[.='Log in']", timeout=3)
        if login_ele:
            login_ele.click()
            return self.browser_controller.backpack_wallet_connect()

        logger.info(f"【{self.id}】 钱包已经连接")
        return True

    def _connect_wallet(self, lasted_tab):
        self.browser_controller.backpack_wallet_login()

        try_count = 6
        for i in range(try_count):
            result = self._tap_wallet_connect(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                sleep(1)
                continue

            sleep(3)

            turbo_ele = lasted_tab.ele("x://button[contains(., '.turbo')]", timeout=6)
            if not turbo_ele:
                logger.error(f"【{self.id}】 未能找到用户名信息, 登录异常")
                lasted_tab.refresh()
                sleep(3)
                continue

            number_element = lasted_tab.ele("x://number-flow-react", timeout=6)
            if not number_element:
                logger.error(f"【{self.id}】 第{i+1}次未能找到积分元素")
                continue

            data_str = number_element.attr("aria-label")
            if data_str:
                number = int(data_str.replace(",", "").replace(".", ""))
                if number > 0:
                    logger.success(f"【{self.id}】 连接钱包成功, 积分: {number}")
                    return True
                else:
                    logger.error(f"【{self.id}】 连接钱包成功, 积分获取失败")
                    continue
            else:
                logger.error(f"【{self.id}】 连接钱包成功, 积分获取失败")
                continue

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def inject_click_js(self, lasted_tab):
        """注入点击JS代码，只需注入一次"""
        js_code = """
            window.getClickArea = function() {
                const width = window.innerWidth || document.documentElement.clientWidth;
                const height = window.innerHeight || document.documentElement.clientHeight;

                const centerX = width * (0.3 + Math.random() * 0.1);
                const centerY = height * (0.55 + Math.random() * 0.1);

                return {
                    minX: centerX - 100,
                    maxX: centerX + 100,
                    minY: centerY - 50,
                    maxY: centerY + 50,
                };
            }

            window.performClick = function() {
                const area = getClickArea();
                const x = Math.random() * (area.maxX - area.minX) + area.minX;
                const y = Math.random() * (area.maxY - area.minY) + area.minY;
                
                const clickEvent = new MouseEvent("click", {
                    view: window,
                    bubbles: true,
                    cancelable: true,
                    clientX: x,
                    clientY: y
                });
                document.elementFromPoint(x, y)?.dispatchEvent(clickEvent);
            }
        """
        lasted_tab.run_js(js_code)

    # def perform_click(self, lasted_tab):
    #     """执行点击"""
    #     lasted_tab.run_js("performClick()")


    def perform_click(self, lasted_tab):
        """使用pyautogui执行点击"""
        try:
            # 获取浏览器窗口的位置和大小
            # 注意：这部分需要根据您的浏览器控制方式来获取窗口信息
            # 假设您可以通过某种方式获取浏览器窗口的位置和大小
            window_info = self.get_browser_window_info()  # 这个函数需要您自己实现
            
            # 计算点击区域（浏览器窗口内的相对位置）
            width = window_info['width']
            height = window_info['height']
            
            # 计算中心区域的随机点
            center_x = window_info['x'] + width * (0.3 + random.random() * 0.1)
            center_y = window_info['y'] + height * (0.55 + random.random() * 0.1)
            
            # 在中心区域周围随机选择点击位置
            x = center_x + random.randint(-100, 100)
            y = center_y + random.randint(-50, 50)
            
            # 确保坐标在窗口内
            x = max(window_info['x'], min(window_info['x'] + width, x))
            y = max(window_info['y'], min(window_info['y'] + height, y))
            
            # 执行点击
            pyautogui.click(x, y)
            
            # 可选：添加一些随机的鼠标移动，使行为更自然
            if random.random() < 0.3:  # 30%的概率
                # 点击后随机移动鼠标
                pyautogui.moveTo(
                    x + random.randint(-50, 50),
                    y + random.randint(-30, 30),
                    duration=random.uniform(0.1, 0.3)
                )
                
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 pyautogui点击失败: {e}")
            return False

    # 获取浏览器窗口信息的示例函数
    def get_browser_window_info(self):
        """
        获取浏览器窗口的位置和大小
        这个函数需要根据您的具体环境来实现
        """
        # 方法1：如果您使用的是DrissionPage，可能可以通过以下方式获取
        try:
            rect = self.page.run_js("return { x: window.screenX, y: window.screenY, width: window.outerWidth, height: window.outerHeight };")
            return rect
        except:
            pass
        
        # 方法2：使用pyautogui查找浏览器窗口（需要窗口标题）
        try:
            import pygetwindow as gw
            window = gw.getWindowsWithTitle("Eclipse")[0]  # 替换为您的浏览器窗口标题
            return {
                'x': window.left,
                'y': window.top,
                'width': window.width,
                'height': window.height
            }
        except:
            pass
        
        # 方法3：如果以上方法都不可行，可以使用固定值或让用户手动指定
        return {
            'x': 0,
            'y': 0,
            'width': 1920,
            'height': 1080
        }



    # 检查页面状态
    # todo: 这个检测目前还在寻找规律
    def check_page_status(self, lasted_tab):
        try:
            number_element = lasted_tab.ele("x://number-flow-react", timeout=3)
            if not number_element:
                logger.error(f"【{self.id}】 页面状态异常，需要重新连接")
                return False
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 页面检查失败: {e}")
            return False

    def _get_clicks_left(self, lasted_tab):
        try:
            ele = lasted_tab.ele(
                "x://div[@data-sentry-component='WithdrawClicks']/div/div/span[2]/span",
                timeout=3,
            )
            if not ele:
                logger.error(f"【{self.id}】 未能找到积分元素")
                return 0

            text = ele.text.lower()
            if "k" in text:
                number = float(text.replace("k", ""))
                return int(number * 1000)

            return int(text)
        except Exception as e:
            logger.error(f"【{self.id}】 获取点击次数失败: {e}")
            return 0

    @retry(tries=3, delay=1)
    def task_tap(self):
        try:
            # 1. 初始化页面
            lasted_tab = self.page.new_tab("https://tap.eclipse.xyz")
            result = self._connect_wallet(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return False

            # 2. 注入点击JS代码
            self.inject_click_js(lasted_tab)

            clicks_left = self._get_clicks_left(lasted_tab)
            logger.info(f"【{self.id}】 剩余点击次数: {clicks_left}")
            if clicks_left <= 200:
                logger.warning(f"【{self.id}】 剩余点击次数小于等于200，停止点击")
                return True

            today_clicks = self._get_today_clicks()
            if today_clicks >= self.max_daily_clicks:
                logger.info(f"【{self.id}】 今日点击次数已达到上限，停止点击")
                return True

            if clicks_left <= today_clicks:
                self.max_daily_clicks = clicks_left

            logger.info(f"【{self.id}】 今日需要点击次数: {self.max_daily_clicks}")

            while today_clicks < self.max_daily_clicks:
                try:
                    # 3. 每轮开始前检查页面状态
                    if not self.check_page_status(lasted_tab):
                        logger.info(f"【{self.id}】 页面状态异常，尝试重新连接")
                        lasted_tab.refresh()
                        sleep(3)
                        if not self._connect_wallet(lasted_tab):
                            raise Exception("重新连接钱包失败")

                        self.inject_click_js(lasted_tab)
                        continue

                    # 4. 设置本轮点击参数
                    round_clicks = random.randint(300, 900)
                    logger.info(
                        f"【{self.id}】 开始新一轮点击，本轮计划点击 {round_clicks} 次"
                    )

                    # 5. 执行点击
                    successful_clicks = 0
                    for i in range(round_clicks):
                        if today_clicks + successful_clicks >= self.max_daily_clicks:
                            logger.info(
                                f"【{self.id}】 达到每日上限 {self.max_daily_clicks} 次，停止点击"
                            )
                            return True

                        try:
                            self.perform_click(lasted_tab)
                            successful_clicks += 1

                            # 随机化点击间隔，模拟人类行为
                            if random.random() < 0.1:  # 10%概率出现较长停顿
                                sleep(random.uniform(1.0, 3.0))
                            else:
                                sleep(random.uniform(0.2, 0.8))

                        except Exception as e:
                            if "disconnected" in str(e).lower():
                                logger.error(f"【{self.id}】 页面连接已断开，停止任务")
                                return False
                            continue

                    # 6. 更新点击计数
                    today_clicks += successful_clicks
                    self.today_clicks = today_clicks
                    logger.success(
                        f"【{self.id}】 完成一轮点击，本轮成功 {successful_clicks} 次，今日总计: {today_clicks}"
                    )

                    # 7. 轮次间休息，使用更自然的时间间隔
                    if today_clicks < self.max_daily_clicks:
                        # 休息时间
                        total_break = random.uniform(5, 15)
                        logger.info(
                            f"【{self.id}】 休息 {total_break:.1f} 分钟后开始下一轮"
                        )
                        sleep(total_break * 60)

                except Exception as e:
                    logger.error(f"【{self.id}】 本轮点击异常: {e}")
                    sleep(60)
                    continue

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")

    def _get_today_clicks(self):
        """获取今日已点击次数"""
        return self.today_clicks


def _tap_task(type, index):
    try:
        browser = Eclipse(type, str(index))
        browser.task_tap()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


def _register_task(type, index):
    try:
        browser = Eclipse(type, str(index))
        browser.task_register()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("tap")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=5, help="并发数量")
def tap(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                browser = Eclipse(type, str(index))
                return browser.task_tap()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Eclipse-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("register")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=4, help="并发数量")
def register(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                browser = Eclipse(type, str(index))
                return browser.task_register()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Eclipse-Register-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/eclipse/index.py tap -t ads -i 1-10 -w 5

# 注册(还没写完)
# python3 examples/eclipse/index.py register -t ads -i 1-10 -w 4

if __name__ == "__main__":
    cli()
    # _tap_task(BrowserType.ADS, "21")
